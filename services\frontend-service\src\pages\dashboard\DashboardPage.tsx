import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Briefcase, 
  FileText, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  XCircle,
  Plus,
  ArrowRight,
  Loader2
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/Button';
import { dashboardService } from '@/services/dashboard.service';
import { DashboardStats, RecentActivity } from '@/types/api';

export const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalApplications: 0,
    pendingApplications: 0,
    interviewsScheduled: 0,
    offersReceived: 0,
    resumesCreated: 0,
    profileViews: 0,
    jobsSaved: 0,
    applicationsThisWeek: 0,
    applicationsThisMonth: 0,
    averageResponseTime: 0,
    successRate: 0,
  });
  const [recentApplications, setRecentApplications] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const [statsData, recentActivity] = await Promise.all([
          dashboardService.getDashboardStats(),
          dashboardService.getRecentActivity(5)
        ]);
        
        setStats(statsData);
        setRecentApplications(recentActivity);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'interview':
        return <Clock className="w-4 h-4 text-warning-500" />;
      case 'accepted':
        return <CheckCircle className="w-4 h-4 text-success-500" />;
      case 'rejected':
        return <XCircle className="w-4 h-4 text-error-500" />;
      default:
        return <FileText className="w-4 h-4 text-primary-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'interview':
        return 'status-interview';
      case 'accepted':
        return 'status-accepted';
      case 'rejected':
        return 'status-rejected';
      default:
        return 'status-submitted';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>Loading dashboard...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircle className="w-5 h-5 text-red-500 mr-2" />
            <span className="text-red-700">{error}</span>
          </div>
          <Button 
            onClick={() => window.location.reload()} 
            className="mt-2"
            variant="outline"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Welcome section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.firstName}!
        </h1>
        <p className="text-gray-600 mt-2">
          Here's an overview of your job search progress
        </p>
      </div>

      {/* Stats grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                  <FileText className="w-4 h-4 text-primary-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Applications</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalApplications}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-warning-100 rounded-lg flex items-center justify-center">
                  <Clock className="w-4 h-4 text-warning-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Applications</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pendingApplications}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center">
                  <Briefcase className="w-4 h-4 text-success-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Interviews</p>
                <p className="text-2xl font-bold text-gray-900">{stats.interviewsScheduled}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 text-blue-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Response Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.totalApplications > 0 ? Math.round((stats.offersReceived / stats.totalApplications) * 100) : 0}%
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <Link to="/jobs" className="card hover-lift">
          <div className="card-body text-center">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Briefcase className="w-6 h-6 text-primary-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Find Jobs</h3>
            <p className="text-gray-600 text-sm mb-4">
              Discover new opportunities that match your skills
            </p>
            <Button variant="outline" size="sm">
              Browse Jobs
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </Link>

        <Link to="/resumes" className="card hover-lift">
          <div className="card-body text-center">
            <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <FileText className="w-6 h-6 text-success-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Upload Resume</h3>
            <p className="text-gray-600 text-sm mb-4">
              Add or optimize your resume for better results
            </p>
            <Button variant="outline" size="sm">
              Manage Resumes
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </Link>

        <Link to="/profile" className="card hover-lift">
          <div className="card-body text-center">
            <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Plus className="w-6 h-6 text-warning-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Complete Profile</h3>
            <p className="text-gray-600 text-sm mb-4">
              Enhance your profile to get better job matches
            </p>
            <Button variant="outline" size="sm">
              Update Profile
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </Link>
      </div>

      {/* Recent applications */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Recent Applications</h2>
            <Link to="/applications" className="text-sm text-primary-600 hover:text-primary-500">
              View all
            </Link>
          </div>
        </div>
        <div className="card-body p-0">
          <div className="divide-y divide-gray-200">
            {recentApplications.length > 0 ? (
              recentApplications.map((activity) => (
                <div key={activity.id} className="p-6 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(activity.status || 'submitted')}
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">
                        {activity.title}
                      </h3>
                      <p className="text-sm text-gray-600">{activity.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className={`badge ${getStatusColor(activity.status || 'submitted')}`}>
                      {activity.status || 'submitted'}
                    </span>
                    <span className="text-sm text-gray-500">
                      {new Date(activity.timestamp).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-6 text-center text-gray-500">
                <p>No recent activity found.</p>
                <p className="text-sm mt-1">Start by applying to jobs or uploading resumes!</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
