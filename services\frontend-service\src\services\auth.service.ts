import { apiService } from './api';
import { 
  User, 
  AuthResponse, 
  LoginRequest, 
  RegisterRequest,
  AuthTokens 
} from '@/types/api';

class AuthService {
  // Authentication methods
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/auth/login', credentials);
    
    // Store tokens and user data
    this.storeAuthData(response);
    
    return response;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/auth/register', userData);
    
    // Store tokens and user data
    this.storeAuthData(response);
    
    return response;
  }

  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearAuthData();
    }
  }

  async refreshToken(): Promise<AuthTokens> {
    const refreshToken = localStorage.getItem('refreshToken');
    
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await apiService.post<AuthTokens>('/auth/refresh', {
      refreshToken,
    });

    // Update stored tokens
    localStorage.setItem('accessToken', response.accessToken);
    localStorage.setItem('refreshToken', response.refreshToken);

    return response;
  }

  async forgotPassword(email: string): Promise<void> {
    await apiService.post('/auth/forgot-password', { email });
  }

  async resetPassword(token: string, password: string, confirmPassword: string): Promise<void> {
    await apiService.post('/auth/reset-password', {
      token,
      password,
      confirmPassword,
    });
  }

  async changePassword(currentPassword: string, newPassword: string, confirmPassword: string): Promise<void> {
    await apiService.post('/auth/change-password', {
      currentPassword,
      newPassword,
      confirmPassword,
    });
  }

  async verifyEmail(email: string, token: string): Promise<void> {
    await apiService.post('/auth/verify-email', { email, token });
  }

  async resendVerification(email: string): Promise<void> {
    await apiService.post('/auth/resend-verification', { email });
  }

  // Google OAuth
  getGoogleAuthUrl(): string {
    return '/api/v1/auth/google';
  }

  // Token management
  private storeAuthData(authResponse: AuthResponse): void {
    const { user, tokens, accessToken, refreshToken, expiresIn } = authResponse;
    
    const finalTokens = tokens || { accessToken: accessToken || '', refreshToken: refreshToken || '', expiresIn: expiresIn || 0 };
    localStorage.setItem('accessToken', finalTokens.accessToken);
    localStorage.setItem('refreshToken', finalTokens.refreshToken);
    localStorage.setItem('user', JSON.stringify(user));
    
    // Generate session ID
    const sessionId = Math.random().toString(36).substring(2, 15);
    localStorage.setItem('sessionId', sessionId);
  }

  private clearAuthData(): void {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('sessionId');
    localStorage.removeItem('user');
  }

  // Utility methods
  isAuthenticated(): boolean {
    const token = localStorage.getItem('accessToken');
    const user = localStorage.getItem('user');
    return !!(token && user);
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (error) {
        console.error('Error parsing user data:', error);
        this.clearAuthData();
      }
    }
    return null;
  }

  getAccessToken(): string | null {
    return localStorage.getItem('accessToken');
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }

  updateCurrentUser(user: User): void {
    localStorage.setItem('user', JSON.stringify(user));
  }

  // Token validation
  isTokenExpired(token?: string): boolean {
    const accessToken = token || this.getAccessToken();
    
    if (!accessToken) return true;

    try {
      const payload = JSON.parse(atob(accessToken.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  // Check user permissions
  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user?.role === role;
  }

  hasAnyRole(roles: string[]): boolean {
    const user = this.getCurrentUser();
    return user ? roles.includes(user.role) : false;
  }

  isAdmin(): boolean {
    return this.hasRole('admin');
  }

  isEmployer(): boolean {
    return this.hasRole('employer');
  }

  isPremiumUser(): boolean {
    const user = this.getCurrentUser();
    return user?.subscriptionTier === 'premium' || user?.subscriptionTier === 'enterprise';
  }
}

export const authService = new AuthService();
