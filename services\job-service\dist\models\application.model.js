"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Application = void 0;
const mongoose_1 = require("mongoose");
const applicationSchema = new mongoose_1.Schema({
    jobId: {
        type: String,
        required: true,
    },
    userId: {
        type: String,
        required: true,
    },
    resumeId: {
        type: String,
        required: true,
    },
    status: {
        type: String,
        enum: ['pending', 'reviewed', 'shortlisted', 'interviewed', 'rejected', 'accepted', 'withdrawn'],
        default: 'pending',
    },
    appliedAt: {
        type: Date,
        default: Date.now,
    },
    notes: String,
    coverLetter: String,
    customAnswers: mongoose_1.Schema.Types.Mixed,
    interviewScheduledAt: Date,
    interviewNotes: String,
    rejectionReason: String,
    salaryExpectation: Number,
    availabilityDate: Date,
    isReferred: {
        type: Boolean,
        default: false,
    },
    referredBy: String,
    source: {
        type: String,
        enum: ['direct', 'job-board', 'referral', 'recruiter'],
        default: 'direct',
    },
    trackingData: {
        views: {
            type: Number,
            default: 0,
        },
        downloads: {
            type: Number,
            default: 0,
        },
        lastViewedAt: Date,
    },
}, {
    timestamps: true,
    versionKey: false,
    toJSON: {
        transform(doc, ret) {
            ret.id = ret._id.toString();
            delete ret._id;
            return ret;
        },
    },
});
// Indexes
applicationSchema.index({ userId: 1 });
applicationSchema.index({ jobId: 1 });
applicationSchema.index({ status: 1 });
applicationSchema.index({ appliedAt: -1 });
exports.Application = (0, mongoose_1.model)('Application', applicationSchema);
//# sourceMappingURL=application.model.js.map