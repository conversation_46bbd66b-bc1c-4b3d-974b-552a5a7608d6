"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.debugRoutes = void 0;
const express_1 = require("express");
const connection_1 = require("../database/connection");
const resume_model_1 = require("../models/resume.model");
const logger_1 = require("../utils/logger");
const response_1 = require("../utils/response");
const router = (0, express_1.Router)();
exports.debugRoutes = router;
// Database connection status
router.get('/db-status', async (req, res) => {
    try {
        const connectionState = connection_1.database.getConnectionState();
        const isHealthy = connection_1.database.isHealthy();
        // Try to perform a simple database operation
        let dbOperationResult = null;
        let dbError = null;
        try {
            const resumeCount = await resume_model_1.Resume.countDocuments();
            dbOperationResult = { resumeCount };
        }
        catch (error) {
            dbError = error instanceof Error ? error.message : 'Unknown database error';
        }
        const response = response_1.ResponseUtil.success({
            connectionState,
            isHealthy,
            dbOperationResult,
            dbError,
            timestamp: new Date().toISOString(),
        }, 'Database status retrieved');
        res.status(response.statusCode).json(response);
    }
    catch (error) {
        logger_1.logger.error('Database status check failed:', error);
        const response = response_1.ResponseUtil.error('Failed to check database status', 500);
        res.status(response.statusCode).json(response);
    }
});
// Test resume creation
router.post('/test-resume-creation', async (req, res) => {
    try {
        const testTitle = `Test Resume ${Date.now()}`;
        logger_1.logger.info('Testing resume creation with title:', testTitle);
        // Create a test resume
        const testResume = new resume_model_1.Resume({
            userId: 'test-user-id',
            title: testTitle,
            fileName: 'test-resume.pdf',
            filePath: '/test/path/test-resume.pdf',
            fileSize: 1024,
            fileType: 'application/pdf',
            content: {
                personalInfo: {
                    firstName: 'Test',
                    lastName: 'User',
                    email: '<EMAIL>',
                },
                skills: ['JavaScript', 'TypeScript', 'Node.js'],
                experience: [],
                education: [],
            },
            isPublic: false,
            isDefault: false,
            tags: ['test'],
            downloadCount: 0,
            viewCount: 0,
        });
        logger_1.logger.info('Attempting to save test resume...');
        const savedResume = await testResume.save();
        logger_1.logger.info('Test resume saved successfully:', { id: savedResume.id, title: savedResume.title });
        // Verify the resume was saved by finding it
        const foundResume = await resume_model_1.Resume.findById(savedResume.id);
        logger_1.logger.info('Test resume found in database:', { found: !!foundResume });
        // Clean up - delete the test resume
        await resume_model_1.Resume.findByIdAndDelete(savedResume.id);
        logger_1.logger.info('Test resume cleaned up');
        const response = response_1.ResponseUtil.success({
            testPassed: true,
            resumeId: savedResume.id,
            title: savedResume.title,
            foundInDb: !!foundResume,
            cleanedUp: true,
        }, 'Resume creation test passed');
        res.status(response.statusCode).json(response);
    }
    catch (error) {
        logger_1.logger.error('Resume creation test failed:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const response = response_1.ResponseUtil.error(`Resume creation test failed: ${errorMessage}`, 500);
        res.status(response.statusCode).json(response);
    }
});
// List recent resumes (for debugging)
router.get('/recent-resumes', async (req, res) => {
    try {
        const resumes = await resume_model_1.Resume.find({})
            .sort({ createdAt: -1 })
            .limit(10)
            .select('userId title fileName createdAt fileSize');
        const response = response_1.ResponseUtil.success({
            resumes,
            count: resumes.length,
        }, 'Recent resumes retrieved');
        res.status(response.statusCode).json(response);
    }
    catch (error) {
        logger_1.logger.error('Failed to retrieve recent resumes:', error);
        const response = response_1.ResponseUtil.error('Failed to retrieve recent resumes', 500);
        res.status(response.statusCode).json(response);
    }
});
// Test stats endpoint directly
router.get('/test-stats', async (req, res) => {
    try {
        logger_1.logger.info('Testing stats endpoint directly');
        const { userId } = req.query;
        const filter = {};
        if (userId)
            filter.userId = userId;
        logger_1.logger.info('Filter for stats test:', filter);
        const totalResumes = await resume_model_1.Resume.countDocuments(filter);
        logger_1.logger.info('Total resumes for stats test:', totalResumes);
        // Get resumes with analysis data
        const resumesWithAnalysis = await resume_model_1.Resume.find({
            ...filter,
            'analysis.overallScore': { $exists: true }
        });
        logger_1.logger.info('Resumes with analysis for stats test:', resumesWithAnalysis.length);
        const averageScore = resumesWithAnalysis.length > 0
            ? resumesWithAnalysis.reduce((sum, resume) => sum + (resume.analysis?.overallScore || 0), 0) / resumesWithAnalysis.length
            : 0;
        // Extract top skills from all resumes
        const allSkills = resumesWithAnalysis.flatMap(resume => resume.content?.skills || []);
        const skillCounts = allSkills.reduce((acc, skill) => {
            acc[skill] = (acc[skill] || 0) + 1;
            return acc;
        }, {});
        const topSkills = Object.entries(skillCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([skill]) => skill);
        const statsData = {
            totalResumes,
            resumesByStatus: {
                ready: totalResumes,
                processing: 0,
                error: 0,
            },
            averageScore: Math.round(averageScore * 100) / 100,
            topSkills,
        };
        logger_1.logger.info('Stats test completed successfully:', statsData);
        const response = response_1.ResponseUtil.success({
            testPassed: true,
            stats: statsData,
            debug: {
                filter,
                totalResumes,
                resumesWithAnalysisCount: resumesWithAnalysis.length,
                allSkillsCount: allSkills.length,
            }
        }, 'Stats test completed successfully');
        res.status(response.statusCode).json(response);
    }
    catch (error) {
        logger_1.logger.error('Stats test failed:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const response = response_1.ResponseUtil.error(`Stats test failed: ${errorMessage}`, 500);
        res.status(response.statusCode).json(response);
    }
});
//# sourceMappingURL=debug.routes.js.map