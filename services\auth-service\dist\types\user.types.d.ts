export interface CreateUserData {
    email: string;
    password?: string | undefined;
    firstName: string;
    lastName: string;
    googleId?: string | undefined;
    avatar?: string | undefined;
    role?: string | undefined;
    subscriptionTier?: string | undefined;
}
export interface UpdateUserData {
    firstName?: string;
    lastName?: string;
    avatar?: string;
    isVerified?: boolean;
    isActive?: boolean;
    isSuspended?: boolean;
    lastLoginAt?: Date;
}
export interface UserQueryOptions {
    isActive?: boolean;
    isVerified?: boolean;
    role?: string;
    subscriptionTier?: string;
}
//# sourceMappingURL=user.types.d.ts.map