{"version": 3, "file": "session.service.js", "sourceRoot": "", "sources": ["../../src/services/session.service.ts"], "names": [], "mappings": ";;;AACA,2DAAiE;AACjE,4CAAyC;AAEzC,MAAa,cAAc;IACzB;;OAEG;IACI,KAAK,CAAC,aAAa,CAAC,WAK1B;QACC,MAAM,OAAO,GAAG,IAAI,4BAAY,CAAC;YAC/B,GAAG,WAAW;YACd,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QACrB,eAAM,CAAC,IAAI,CAAC,4BAA4B,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACtE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe,CAAC,SAAiB;QAC5C,OAAO,MAAM,4BAAY,CAAC,OAAO,CAAC;YAChC,SAAS;YACT,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,sBAAsB,CACjC,MAAsB;QAEtB,OAAO,MAAM,4BAAY,CAAC,IAAI,CAAC;YAC7B,MAAM;YACN,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QAC9C,MAAM,4BAAY,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;QACjE,eAAM,CAAC,IAAI,CAAC,WAAW,SAAS,cAAc,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,yBAAyB,CACpC,MAAsB;QAEtB,MAAM,4BAAY,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;QAC/D,eAAM,CAAC,IAAI,CAAC,qCAAqC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,sBAAsB;QACjC,MAAM,MAAM,GAAG,MAAM,4BAAY,CAAC,UAAU,CAAC;YAC3C,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,EAAE;SAC/B,CAAC,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,cAAc,MAAM,CAAC,YAAY,mBAAmB,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,mBAAmB,CAAC,MAAsB;QAIrD,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAClD,4BAAY,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;YACvD,4BAAY,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC;SACxC,CAAC,CAAC;QAEH,OAAO;YACL,mBAAmB,EAAE,WAAW;YAChC,kBAAkB,EAAE,UAAU;SAC/B,CAAC;IACJ,CAAC;CACF;AAvFD,wCAuFC"}