import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as JwtStrategy, ExtractJwt } from 'passport-jwt';
import { googleConfig, jwtConfig } from './environment';
import { logger } from '../utils/logger';
import { UserService } from '../services/user.service';
import { AuthService } from '../services/auth.service';

export const setupPassport = (): void => {
  // Google OAuth Strategy
  if (googleConfig.clientId && googleConfig.clientSecret) {
    passport.use(
      new GoogleStrategy(
        {
          clientID: googleConfig.clientId,
          clientSecret: googleConfig.clientSecret,
          callbackURL: googleConfig.callbackURL,
          scope: ['profile', 'email'],
        },
        (accessToken, refreshToken, profile, done): void => {
          void (async (): Promise<void> => {
            try {
              logger.debug('Google OAuth callback received:', {
                profileId: profile.id,
                email: profile.emails?.[0]?.value,
              });

              const googleUser = {
                googleId: profile.id,
                email: profile.emails?.[0]?.value ?? '',
                firstName: profile.name?.givenName ?? '',
                lastName: profile.name?.familyName ?? '',
                avatar: profile.photos?.[0]?.value,
                emailVerified: profile.emails?.[0]?.verified ?? false,
              };

              const authService = new AuthService();
              const user = await authService.handleGoogleAuth(googleUser);

              return done(null, user);
            } catch (error) {
              logger.error('Google OAuth error:', error);
              return done(error as Error, undefined);
            }
          })();
        }
      )
    );
  } else {
    logger.warn('Google OAuth not configured - missing client credentials');
  }

  // JWT Strategy
  passport.use(
    new JwtStrategy(
      {
        jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
        secretOrKey: jwtConfig.secret,
        issuer: 'job-platform',
        audience: 'job-platform-users',
      },
      (payload: { userId: string }, done): void => {
        void (async (): Promise<void> => {
          try {
            const userService = new UserService();
            const user = await userService.findById(payload.userId);

            if (!user) {
              logger.warn(
                'JWT validation failed - user not found:',
                payload.userId
              );
              return done(null, false);
            }

            if (!user.isActive) {
              logger.warn(
                'JWT validation failed - user inactive:',
                payload.userId
              );
              return done(null, false);
            }

            // Check if token is blacklisted
            const authService = new AuthService();
            const isBlacklisted = await authService.isTokenBlacklisted(
              ExtractJwt.fromAuthHeaderAsBearerToken()(null as unknown) ?? ''
            );

            if (isBlacklisted) {
              logger.warn(
                'JWT validation failed - token blacklisted:',
                payload.userId
              );
              return done(null, false);
            }

            return done(null, {
              id: (user._id as { toString: () => string }).toString(),
              email: user.email,
              role: user.role,
              isVerified: user.isVerified,
              subscriptionTier: user.subscriptionTier,
              permissions: [], // TODO: Load user permissions
            });
          } catch (error) {
            logger.error('JWT validation error:', error);
            return done(error, false);
          }
        })();
      }
    )
  );

  // Serialize/Deserialize user for session support (if needed)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  passport.serializeUser((user: any, done): void => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    done(null, user.id);
  });

  passport.deserializeUser((id: string, done): void => {
    void (async (): Promise<void> => {
      try {
        const userService = new UserService();
        const user = await userService.findById(id);
        done(null, user);
      } catch (error) {
        done(error as Error, null);
      }
    })();
  });
};
