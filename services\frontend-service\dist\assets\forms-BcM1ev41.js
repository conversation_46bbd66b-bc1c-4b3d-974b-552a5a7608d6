import{R as I}from"./router-CTBu-pz9.js";var de=e=>e.type==="checkbox",re=e=>e instanceof Date,R=e=>e==null;const tt=e=>typeof e=="object";var S=e=>!R(e)&&!Array.isArray(e)&&tt(e)&&!re(e),_t=e=>S(e)&&e.target?de(e.target)?e.target.checked:e.target.value:e,Vt=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,Ft=(e,s)=>e.has(Vt(s)),At=e=>{const s=e.constructor&&e.constructor.prototype;return S(s)&&s.hasOwnProperty("isPrototypeOf")},Se=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function C(e){let s;const r=Array.isArray(e),a=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)s=new Date(e);else if(!(Se&&(e instanceof Blob||a))&&(r||S(e)))if(s=r?[]:Object.create(Object.getPrototypeOf(e)),!r&&!At(e))s=e;else for(const o in e)e.hasOwnProperty(o)&&(s[o]=C(e[o]));else return e;return s}var be=e=>/^\w*$/.test(e),E=e=>e===void 0,Oe=e=>Array.isArray(e)?e.filter(Boolean):[],Te=e=>Oe(e.replace(/["|']|\]/g,"").split(/\.|\[/)),h=(e,s,r)=>{if(!s||!S(e))return r;const a=(be(s)?[s]:Te(s)).reduce((o,n)=>R(o)?o:o[n],e);return E(a)||a===e?E(e[s])?r:e[s]:a},Y=e=>typeof e=="boolean",m=(e,s,r)=>{let a=-1;const o=be(s)?[s]:Te(s),n=o.length,c=n-1;for(;++a<n;){const y=o[a];let U=r;if(a!==c){const p=e[y];U=S(p)||Array.isArray(p)?p:isNaN(+o[a+1])?{}:[]}if(y==="__proto__"||y==="constructor"||y==="prototype")return;e[y]=U,e=e[y]}};const pe={BLUR:"blur",FOCUS_OUT:"focusout"},W={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Q={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},xt=I.createContext(null);xt.displayName="HookFormContext";var mt=(e,s,r,a=!0)=>{const o={defaultValues:s._defaultValues};for(const n in e)Object.defineProperty(o,n,{get:()=>{const c=n;return s._proxyFormState[c]!==W.all&&(s._proxyFormState[c]=!a||W.all),e[c]}});return o};const wt=typeof window<"u"?I.useLayoutEffect:I.useEffect;var N=e=>typeof e=="string",Dt=(e,s,r,a,o)=>N(e)?(a&&s.watch.add(e),h(r,e,o)):Array.isArray(e)?e.map(n=>(a&&s.watch.add(n),h(r,n))):(a&&(s.watchAll=!0),r),ke=e=>R(e)||!tt(e);function ee(e,s,r=new WeakSet){if(ke(e)||ke(s))return e===s;if(re(e)&&re(s))return e.getTime()===s.getTime();const a=Object.keys(e),o=Object.keys(s);if(a.length!==o.length)return!1;if(r.has(e)||r.has(s))return!0;r.add(e),r.add(s);for(const n of a){const c=e[n];if(!o.includes(n))return!1;if(n!=="ref"){const y=s[n];if(re(c)&&re(y)||S(c)&&S(y)||Array.isArray(c)&&Array.isArray(y)?!ee(c,y,r):c!==y)return!1}}return!0}var kt=(e,s,r,a,o)=>s?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[a]:o||!0}}:{},oe=e=>Array.isArray(e)?e:[e],$e=()=>{let e=[];return{get observers(){return e},next:o=>{for(const n of e)n.next&&n.next(o)},subscribe:o=>(e.push(o),{unsubscribe:()=>{e=e.filter(n=>n!==o)}}),unsubscribe:()=>{e=[]}}};function rt(e,s){const r={};for(const a in e)if(e.hasOwnProperty(a)){const o=e[a],n=s[a];if(o&&S(o)&&n){const c=rt(o,n);S(c)&&(r[a]=c)}else e[a]&&(r[a]=n)}return r}var B=e=>S(e)&&!Object.keys(e).length,Ce=e=>e.type==="file",H=e=>typeof e=="function",ge=e=>{if(!Se)return!1;const s=e?e.ownerDocument:0;return e instanceof(s&&s.defaultView?s.defaultView.HTMLElement:HTMLElement)},st=e=>e.type==="select-multiple",Le=e=>e.type==="radio",Et=e=>Le(e)||de(e),De=e=>ge(e)&&e.isConnected;function St(e,s){const r=s.slice(0,-1).length;let a=0;for(;a<r;)e=E(e)?a++:e[s[a++]];return e}function Ot(e){for(const s in e)if(e.hasOwnProperty(s)&&!E(e[s]))return!1;return!0}function k(e,s){const r=Array.isArray(s)?s:be(s)?[s]:Te(s),a=r.length===1?e:St(e,r),o=r.length-1,n=r[o];return a&&delete a[n],o!==0&&(S(a)&&B(a)||Array.isArray(a)&&Ot(a))&&k(e,r.slice(0,-1)),e}var Tt=e=>{for(const s in e)if(H(e[s]))return!0;return!1};function it(e){return Array.isArray(e)||S(e)&&!Tt(e)}function Ee(e,s={}){for(const r in e)it(e[r])?(s[r]=Array.isArray(e[r])?[]:{},Ee(e[r],s[r])):R(e[r])||(s[r]=!0);return s}function ae(e,s,r){r||(r=Ee(s));for(const a in e)it(e[a])?E(s)||ke(r[a])?r[a]=Ee(e[a],Array.isArray(e[a])?[]:{}):ae(e[a],R(s)?{}:s[a],r[a]):r[a]=!ee(e[a],s[a]);return r}const Ke={value:!1,isValid:!1},ze={value:!0,isValid:!0};var at=e=>{if(Array.isArray(e)){if(e.length>1){const s=e.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:s,isValid:!!s.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!E(e[0].attributes.value)?E(e[0].value)||e[0].value===""?ze:{value:e[0].value,isValid:!0}:ze:Ke}return Ke},lt=(e,{valueAsNumber:s,valueAsDate:r,setValueAs:a})=>E(e)?e:s?e===""?NaN:e&&+e:r&&N(e)?new Date(e):a?a(e):e;const Ye={isValid:!1,value:null};var nt=e=>Array.isArray(e)?e.reduce((s,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:s,Ye):Ye;function Je(e){const s=e.ref;return Ce(s)?s.files:Le(s)?nt(e.refs).value:st(s)?[...s.selectedOptions].map(({value:r})=>r):de(s)?at(e.refs).value:lt(E(s.value)?e.ref.value:s.value,e)}var Ct=(e,s,r,a)=>{const o={};for(const n of e){const c=h(s,n);c&&m(o,n,c._f)}return{criteriaMode:r,names:[...e],fields:o,shouldUseNativeValidation:a}},ve=e=>e instanceof RegExp,ue=e=>E(e)?e:ve(e)?e.source:S(e)?ve(e.value)?e.value.source:e.value:e,Qe=e=>({isOnSubmit:!e||e===W.onSubmit,isOnBlur:e===W.onBlur,isOnChange:e===W.onChange,isOnAll:e===W.all,isOnTouch:e===W.onTouched});const Xe="AsyncFunction";var Lt=e=>!!e&&!!e.validate&&!!(H(e.validate)&&e.validate.constructor.name===Xe||S(e.validate)&&Object.values(e.validate).find(s=>s.constructor.name===Xe)),Rt=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Ze=(e,s,r)=>!r&&(s.watchAll||s.watch.has(e)||[...s.watch].some(a=>e.startsWith(a)&&/^\.\w+/.test(e.slice(a.length))));const fe=(e,s,r,a)=>{for(const o of r||Object.keys(e)){const n=h(e,o);if(n){const{_f:c,...y}=n;if(c){if(c.refs&&c.refs[0]&&s(c.refs[0],o)&&!a)return!0;if(c.ref&&s(c.ref,c.name)&&!a)return!0;if(fe(y,s))break}else if(S(y)&&fe(y,s))break}}};function Ge(e,s,r){const a=h(e,r);if(a||be(r))return{error:a,name:r};const o=r.split(".");for(;o.length;){const n=o.join("."),c=h(s,n),y=h(e,n);if(c&&!Array.isArray(c)&&r!==n)return{name:r};if(y&&y.type)return{name:n,error:y};if(y&&y.root&&y.root.type)return{name:`${n}.root`,error:y.root};o.pop()}return{name:r}}var Ut=(e,s,r,a)=>{r(e);const{name:o,...n}=e;return B(n)||Object.keys(n).length>=Object.keys(s).length||Object.keys(n).find(c=>s[c]===(!a||W.all))},Mt=(e,s,r)=>!e||!s||e===s||oe(e).some(a=>a&&(r?a===s:a.startsWith(s)||s.startsWith(a))),Bt=(e,s,r,a,o)=>o.isOnAll?!1:!r&&o.isOnTouch?!(s||e):(r?a.isOnBlur:o.isOnBlur)?!e:(r?a.isOnChange:o.isOnChange)?e:!0,It=(e,s)=>!Oe(h(e,s)).length&&k(e,s),Nt=(e,s,r)=>{const a=oe(h(e,r));return m(a,"root",s[r]),m(e,r,a),e};function je(e,s,r="validate"){if(N(e)||Array.isArray(e)&&e.every(N)||Y(e)&&!e)return{type:r,message:N(e)?e:"",ref:s}}var ie=e=>S(e)&&!ve(e)?e:{value:e,message:""},et=async(e,s,r,a,o,n)=>{const{ref:c,refs:y,required:U,maxLength:p,minLength:x,min:D,max:v,pattern:le,validate:X,name:O,valueAsNumber:Z,mount:_e}=e._f,_=h(r,O);if(!_e||s.has(O))return{};const J=y?y[0]:c,$=b=>{o&&J.reportValidity&&(J.setCustomValidity(Y(b)?"":b||""),J.reportValidity())},T={},ce=Le(c),G=de(c),Ve=ce||G,q=(Z||Ce(c))&&E(c.value)&&E(_)||ge(c)&&c.value===""||_===""||Array.isArray(_)&&!_.length,te=kt.bind(null,O,a,T),K=(b,F,w,L=Q.maxLength,M=Q.minLength)=>{const z=b?F:w;T[O]={type:b?L:M,message:z,ref:c,...te(b?L:M,z)}};if(n?!Array.isArray(_)||!_.length:U&&(!Ve&&(q||R(_))||Y(_)&&!_||G&&!at(y).isValid||ce&&!nt(y).isValid)){const{value:b,message:F}=N(U)?{value:!!U,message:U}:ie(U);if(b&&(T[O]={type:Q.required,message:F,ref:J,...te(Q.required,F)},!a))return $(F),T}if(!q&&(!R(D)||!R(v))){let b,F;const w=ie(v),L=ie(D);if(!R(_)&&!isNaN(_)){const M=c.valueAsNumber||_&&+_;R(w.value)||(b=M>w.value),R(L.value)||(F=M<L.value)}else{const M=c.valueAsDate||new Date(_),z=ye=>new Date(new Date().toDateString()+" "+ye),ne=c.type=="time",se=c.type=="week";N(w.value)&&_&&(b=ne?z(_)>z(w.value):se?_>w.value:M>new Date(w.value)),N(L.value)&&_&&(F=ne?z(_)<z(L.value):se?_<L.value:M<new Date(L.value))}if((b||F)&&(K(!!b,w.message,L.message,Q.max,Q.min),!a))return $(T[O].message),T}if((p||x)&&!q&&(N(_)||n&&Array.isArray(_))){const b=ie(p),F=ie(x),w=!R(b.value)&&_.length>+b.value,L=!R(F.value)&&_.length<+F.value;if((w||L)&&(K(w,b.message,F.message),!a))return $(T[O].message),T}if(le&&!q&&N(_)){const{value:b,message:F}=ie(le);if(ve(b)&&!_.match(b)&&(T[O]={type:Q.pattern,message:F,ref:c,...te(Q.pattern,F)},!a))return $(F),T}if(X){if(H(X)){const b=await X(_,r),F=je(b,J);if(F&&(T[O]={...F,...te(Q.validate,F.message)},!a))return $(F.message),T}else if(S(X)){let b={};for(const F in X){if(!B(b)&&!a)break;const w=je(await X[F](_,r),J,F);w&&(b={...w,...te(F,w.message)},$(w.message),a&&(T[O]=b))}if(!B(b)&&(T[O]={ref:J,...b},!a))return T}}return $(!0),T};const Pt={mode:W.onSubmit,reValidateMode:W.onChange,shouldFocusError:!0};function qt(e={}){let s={...Pt,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:H(s.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:s.errors||{},disabled:s.disabled||!1},a={},o=S(s.defaultValues)||S(s.values)?C(s.defaultValues||s.values)||{}:{},n=s.shouldUnregister?{}:C(o),c={action:!1,mount:!1,watch:!1},y={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},U,p=0;const x={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let D={...x};const v={array:$e(),state:$e()},le=s.criteriaMode===W.all,X=t=>i=>{clearTimeout(p),p=setTimeout(t,i)},O=async t=>{if(!s.disabled&&(x.isValid||D.isValid||t)){const i=s.resolver?B((await G()).errors):await q(a,!0);i!==r.isValid&&v.state.next({isValid:i})}},Z=(t,i)=>{!s.disabled&&(x.isValidating||x.validatingFields||D.isValidating||D.validatingFields)&&((t||Array.from(y.mount)).forEach(l=>{l&&(i?m(r.validatingFields,l,i):k(r.validatingFields,l))}),v.state.next({validatingFields:r.validatingFields,isValidating:!B(r.validatingFields)}))},_e=(t,i=[],l,d,f=!0,u=!0)=>{if(d&&l&&!s.disabled){if(c.action=!0,u&&Array.isArray(h(a,t))){const g=l(h(a,t),d.argA,d.argB);f&&m(a,t,g)}if(u&&Array.isArray(h(r.errors,t))){const g=l(h(r.errors,t),d.argA,d.argB);f&&m(r.errors,t,g),It(r.errors,t)}if((x.touchedFields||D.touchedFields)&&u&&Array.isArray(h(r.touchedFields,t))){const g=l(h(r.touchedFields,t),d.argA,d.argB);f&&m(r.touchedFields,t,g)}(x.dirtyFields||D.dirtyFields)&&(r.dirtyFields=ae(o,n)),v.state.next({name:t,isDirty:K(t,i),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else m(n,t,i)},_=(t,i)=>{m(r.errors,t,i),v.state.next({errors:r.errors})},J=t=>{r.errors=t,v.state.next({errors:r.errors,isValid:!1})},$=(t,i,l,d)=>{const f=h(a,t);if(f){const u=h(n,t,E(l)?h(o,t):l);E(u)||d&&d.defaultChecked||i?m(n,t,i?u:Je(f._f)):w(t,u),c.mount&&O()}},T=(t,i,l,d,f)=>{let u=!1,g=!1;const V={name:t};if(!s.disabled){if(!l||d){(x.isDirty||D.isDirty)&&(g=r.isDirty,r.isDirty=V.isDirty=K(),u=g!==V.isDirty);const A=ee(h(o,t),i);g=!!h(r.dirtyFields,t),A?k(r.dirtyFields,t):m(r.dirtyFields,t,!0),V.dirtyFields=r.dirtyFields,u=u||(x.dirtyFields||D.dirtyFields)&&g!==!A}if(l){const A=h(r.touchedFields,t);A||(m(r.touchedFields,t,l),V.touchedFields=r.touchedFields,u=u||(x.touchedFields||D.touchedFields)&&A!==l)}u&&f&&v.state.next(V)}return u?V:{}},ce=(t,i,l,d)=>{const f=h(r.errors,t),u=(x.isValid||D.isValid)&&Y(i)&&r.isValid!==i;if(s.delayError&&l?(U=X(()=>_(t,l)),U(s.delayError)):(clearTimeout(p),U=null,l?m(r.errors,t,l):k(r.errors,t)),(l?!ee(f,l):f)||!B(d)||u){const g={...d,...u&&Y(i)?{isValid:i}:{},errors:r.errors,name:t};r={...r,...g},v.state.next(g)}},G=async t=>{Z(t,!0);const i=await s.resolver(n,s.context,Ct(t||y.mount,a,s.criteriaMode,s.shouldUseNativeValidation));return Z(t),i},Ve=async t=>{const{errors:i}=await G(t);if(t)for(const l of t){const d=h(i,l);d?m(r.errors,l,d):k(r.errors,l)}else r.errors=i;return i},q=async(t,i,l={valid:!0})=>{for(const d in t){const f=t[d];if(f){const{_f:u,...g}=f;if(u){const V=y.array.has(u.name),A=f._f&&Lt(f._f);A&&x.validatingFields&&Z([u.name],!0);const P=await et(f,y.disabled,n,le,s.shouldUseNativeValidation&&!i,V);if(A&&x.validatingFields&&Z([u.name]),P[u.name]&&(l.valid=!1,i))break;!i&&(h(P,u.name)?V?Nt(r.errors,P,u.name):m(r.errors,u.name,P[u.name]):k(r.errors,u.name))}!B(g)&&await q(g,i,l)}}return l.valid},te=()=>{for(const t of y.unMount){const i=h(a,t);i&&(i._f.refs?i._f.refs.every(l=>!De(l)):!De(i._f.ref))&&Fe(t)}y.unMount=new Set},K=(t,i)=>!s.disabled&&(t&&i&&m(n,t,i),!ee(ye(),o)),b=(t,i,l)=>Dt(t,y,{...c.mount?n:E(i)?o:N(t)?{[t]:i}:i},l,i),F=t=>Oe(h(c.mount?n:o,t,s.shouldUnregister?h(o,t,[]):[])),w=(t,i,l={})=>{const d=h(a,t);let f=i;if(d){const u=d._f;u&&(!u.disabled&&m(n,t,lt(i,u)),f=ge(u.ref)&&R(i)?"":i,st(u.ref)?[...u.ref.options].forEach(g=>g.selected=f.includes(g.value)):u.refs?de(u.ref)?u.refs.forEach(g=>{(!g.defaultChecked||!g.disabled)&&(Array.isArray(f)?g.checked=!!f.find(V=>V===g.value):g.checked=f===g.value||!!f)}):u.refs.forEach(g=>g.checked=g.value===f):Ce(u.ref)?u.ref.value="":(u.ref.value=f,u.ref.type||v.state.next({name:t,values:C(n)})))}(l.shouldDirty||l.shouldTouch)&&T(t,f,l.shouldTouch,l.shouldDirty,!0),l.shouldValidate&&se(t)},L=(t,i,l)=>{for(const d in i){if(!i.hasOwnProperty(d))return;const f=i[d],u=t+"."+d,g=h(a,u);(y.array.has(t)||S(f)||g&&!g._f)&&!re(f)?L(u,f,l):w(u,f,l)}},M=(t,i,l={})=>{const d=h(a,t),f=y.array.has(t),u=C(i);m(n,t,u),f?(v.array.next({name:t,values:C(n)}),(x.isDirty||x.dirtyFields||D.isDirty||D.dirtyFields)&&l.shouldDirty&&v.state.next({name:t,dirtyFields:ae(o,n),isDirty:K(t,u)})):d&&!d._f&&!R(u)?L(t,u,l):w(t,u,l),Ze(t,y)&&v.state.next({...r,name:t}),v.state.next({name:c.mount?t:void 0,values:C(n)})},z=async t=>{c.mount=!0;const i=t.target;let l=i.name,d=!0;const f=h(a,l),u=A=>{d=Number.isNaN(A)||re(A)&&isNaN(A.getTime())||ee(A,h(n,l,A))},g=Qe(s.mode),V=Qe(s.reValidateMode);if(f){let A,P;const he=i.type?Je(f._f):_t(t),j=t.type===pe.BLUR||t.type===pe.FOCUS_OUT,gt=!Rt(f._f)&&!s.resolver&&!h(r.errors,l)&&!f._f.deps||Bt(j,h(r.touchedFields,l),r.isSubmitted,V,g),me=Ze(l,y,j);m(n,l,he),j?(!i||!i.readOnly)&&(f._f.onBlur&&f._f.onBlur(t),U&&U(0)):f._f.onChange&&f._f.onChange(t);const we=T(l,he,j),vt=!B(we)||me;if(!j&&v.state.next({name:l,type:t.type,values:C(n)}),gt)return(x.isValid||D.isValid)&&(s.mode==="onBlur"?j&&O():j||O()),vt&&v.state.next({name:l,...me?{}:we});if(!j&&me&&v.state.next({...r}),s.resolver){const{errors:We}=await G([l]);if(u(he),d){const bt=Ge(r.errors,a,l),He=Ge(We,a,bt.name||l);A=He.error,l=He.name,P=B(We)}}else Z([l],!0),A=(await et(f,y.disabled,n,le,s.shouldUseNativeValidation))[l],Z([l]),u(he),d&&(A?P=!1:(x.isValid||D.isValid)&&(P=await q(a,!0)));d&&(f._f.deps&&(!Array.isArray(f._f.deps)||f._f.deps.length>0)&&se(f._f.deps),ce(l,P,A,we))}},ne=(t,i)=>{if(h(r.errors,i)&&t.focus)return t.focus(),1},se=async(t,i={})=>{let l,d;const f=oe(t);if(s.resolver){const u=await Ve(E(t)?t:f);l=B(u),d=t?!f.some(g=>h(u,g)):l}else t?(d=(await Promise.all(f.map(async u=>{const g=h(a,u);return await q(g&&g._f?{[u]:g}:g)}))).every(Boolean),!(!d&&!r.isValid)&&O()):d=l=await q(a);return v.state.next({...!N(t)||(x.isValid||D.isValid)&&l!==r.isValid?{}:{name:t},...s.resolver||!t?{isValid:l}:{},errors:r.errors}),i.shouldFocus&&!d&&fe(a,ne,t?f:y.mount),d},ye=(t,i)=>{let l={...c.mount?n:o};return i&&(l=rt(i.dirtyFields?r.dirtyFields:r.touchedFields,l)),E(t)?l:N(t)?h(l,t):t.map(d=>h(l,d))},Re=(t,i)=>({invalid:!!h((i||r).errors,t),isDirty:!!h((i||r).dirtyFields,t),error:h((i||r).errors,t),isValidating:!!h(r.validatingFields,t),isTouched:!!h((i||r).touchedFields,t)}),ut=t=>{t&&oe(t).forEach(i=>k(r.errors,i)),v.state.next({errors:t?r.errors:{}})},Ue=(t,i,l)=>{const d=(h(a,t,{_f:{}})._f||{}).ref,f=h(r.errors,t)||{},{ref:u,message:g,type:V,...A}=f;m(r.errors,t,{...A,...i,ref:d}),v.state.next({name:t,errors:r.errors,isValid:!1}),l&&l.shouldFocus&&d&&d.focus&&d.focus()},ot=(t,i)=>H(t)?v.state.subscribe({next:l=>"values"in l&&t(b(void 0,i),l)}):b(t,i,!0),Me=t=>v.state.subscribe({next:i=>{Mt(t.name,i.name,t.exact)&&Ut(i,t.formState||x,ht,t.reRenderRoot)&&t.callback({values:{...n},...r,...i,defaultValues:o})}}).unsubscribe,ft=t=>(c.mount=!0,D={...D,...t.formState},Me({...t,formState:D})),Fe=(t,i={})=>{for(const l of t?oe(t):y.mount)y.mount.delete(l),y.array.delete(l),i.keepValue||(k(a,l),k(n,l)),!i.keepError&&k(r.errors,l),!i.keepDirty&&k(r.dirtyFields,l),!i.keepTouched&&k(r.touchedFields,l),!i.keepIsValidating&&k(r.validatingFields,l),!s.shouldUnregister&&!i.keepDefaultValue&&k(o,l);v.state.next({values:C(n)}),v.state.next({...r,...i.keepDirty?{isDirty:K()}:{}}),!i.keepIsValid&&O()},Be=({disabled:t,name:i})=>{(Y(t)&&c.mount||t||y.disabled.has(i))&&(t?y.disabled.add(i):y.disabled.delete(i))},Ae=(t,i={})=>{let l=h(a,t);const d=Y(i.disabled)||Y(s.disabled);return m(a,t,{...l||{},_f:{...l&&l._f?l._f:{ref:{name:t}},name:t,mount:!0,...i}}),y.mount.add(t),l?Be({disabled:Y(i.disabled)?i.disabled:s.disabled,name:t}):$(t,!0,i.value),{...d?{disabled:i.disabled||s.disabled}:{},...s.progressive?{required:!!i.required,min:ue(i.min),max:ue(i.max),minLength:ue(i.minLength),maxLength:ue(i.maxLength),pattern:ue(i.pattern)}:{},name:t,onChange:z,onBlur:z,ref:f=>{if(f){Ae(t,i),l=h(a,t);const u=E(f.value)&&f.querySelectorAll&&f.querySelectorAll("input,select,textarea")[0]||f,g=Et(u),V=l._f.refs||[];if(g?V.find(A=>A===u):u===l._f.ref)return;m(a,t,{_f:{...l._f,...g?{refs:[...V.filter(De),u,...Array.isArray(h(o,t))?[{}]:[]],ref:{type:u.type,name:t}}:{ref:u}}}),$(t,!1,void 0,u)}else l=h(a,t,{}),l._f&&(l._f.mount=!1),(s.shouldUnregister||i.shouldUnregister)&&!(Ft(y.array,t)&&c.action)&&y.unMount.add(t)}}},xe=()=>s.shouldFocusError&&fe(a,ne,y.mount),dt=t=>{Y(t)&&(v.state.next({disabled:t}),fe(a,(i,l)=>{const d=h(a,l);d&&(i.disabled=d._f.disabled||t,Array.isArray(d._f.refs)&&d._f.refs.forEach(f=>{f.disabled=d._f.disabled||t}))},0,!1))},Ie=(t,i)=>async l=>{let d;l&&(l.preventDefault&&l.preventDefault(),l.persist&&l.persist());let f=C(n);if(v.state.next({isSubmitting:!0}),s.resolver){const{errors:u,values:g}=await G();r.errors=u,f=C(g)}else await q(a);if(y.disabled.size)for(const u of y.disabled)k(f,u);if(k(r.errors,"root"),B(r.errors)){v.state.next({errors:{}});try{await t(f,l)}catch(u){d=u}}else i&&await i({...r.errors},l),xe(),setTimeout(xe);if(v.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:B(r.errors)&&!d,submitCount:r.submitCount+1,errors:r.errors}),d)throw d},ct=(t,i={})=>{h(a,t)&&(E(i.defaultValue)?M(t,C(h(o,t))):(M(t,i.defaultValue),m(o,t,C(i.defaultValue))),i.keepTouched||k(r.touchedFields,t),i.keepDirty||(k(r.dirtyFields,t),r.isDirty=i.defaultValue?K(t,C(h(o,t))):K()),i.keepError||(k(r.errors,t),x.isValid&&O()),v.state.next({...r}))},Ne=(t,i={})=>{const l=t?C(t):o,d=C(l),f=B(t),u=f?o:d;if(i.keepDefaultValues||(o=l),!i.keepValues){if(i.keepDirtyValues){const g=new Set([...y.mount,...Object.keys(ae(o,n))]);for(const V of Array.from(g))h(r.dirtyFields,V)?m(u,V,h(n,V)):M(V,h(u,V))}else{if(Se&&E(t))for(const g of y.mount){const V=h(a,g);if(V&&V._f){const A=Array.isArray(V._f.refs)?V._f.refs[0]:V._f.ref;if(ge(A)){const P=A.closest("form");if(P){P.reset();break}}}}if(i.keepFieldsRef)for(const g of y.mount)M(g,h(u,g));else a={}}n=s.shouldUnregister?i.keepDefaultValues?C(o):{}:C(u),v.array.next({values:{...u}}),v.state.next({values:{...u}})}y={mount:i.keepDirtyValues?y.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},c.mount=!x.isValid||!!i.keepIsValid||!!i.keepDirtyValues,c.watch=!!s.shouldUnregister,v.state.next({submitCount:i.keepSubmitCount?r.submitCount:0,isDirty:f?!1:i.keepDirty?r.isDirty:!!(i.keepDefaultValues&&!ee(t,o)),isSubmitted:i.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:f?{}:i.keepDirtyValues?i.keepDefaultValues&&n?ae(o,n):r.dirtyFields:i.keepDefaultValues&&t?ae(o,t):i.keepDirty?r.dirtyFields:{},touchedFields:i.keepTouched?r.touchedFields:{},errors:i.keepErrors?r.errors:{},isSubmitSuccessful:i.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1,defaultValues:o})},Pe=(t,i)=>Ne(H(t)?t(n):t,i),yt=(t,i={})=>{const l=h(a,t),d=l&&l._f;if(d){const f=d.refs?d.refs[0]:d.ref;f.focus&&(f.focus(),i.shouldSelect&&H(f.select)&&f.select())}},ht=t=>{r={...r,...t}},qe={control:{register:Ae,unregister:Fe,getFieldState:Re,handleSubmit:Ie,setError:Ue,_subscribe:Me,_runSchema:G,_focusError:xe,_getWatch:b,_getDirty:K,_setValid:O,_setFieldArray:_e,_setDisabledField:Be,_setErrors:J,_getFieldArray:F,_reset:Ne,_resetDefaultValues:()=>H(s.defaultValues)&&s.defaultValues().then(t=>{Pe(t,s.resetOptions),v.state.next({isLoading:!1})}),_removeUnmounted:te,_disableForm:dt,_subjects:v,_proxyFormState:x,get _fields(){return a},get _formValues(){return n},get _state(){return c},set _state(t){c=t},get _defaultValues(){return o},get _names(){return y},set _names(t){y=t},get _formState(){return r},get _options(){return s},set _options(t){s={...s,...t}}},subscribe:ft,trigger:se,register:Ae,handleSubmit:Ie,watch:ot,setValue:M,getValues:ye,reset:Pe,resetField:ct,clearErrors:ut,unregister:Fe,setError:Ue,setFocus:yt,getFieldState:Re};return{...qe,formControl:qe}}function pt(e={}){const s=I.useRef(void 0),r=I.useRef(void 0),[a,o]=I.useState({isDirty:!1,isValidating:!1,isLoading:H(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:H(e.defaultValues)?void 0:e.defaultValues});if(!s.current)if(e.formControl)s.current={...e.formControl,formState:a},e.defaultValues&&!H(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:c,...y}=qt(e);s.current={...y,formState:a}}const n=s.current.control;return n._options=e,wt(()=>{const c=n._subscribe({formState:n._proxyFormState,callback:()=>o({...n._formState}),reRenderRoot:!0});return o(y=>({...y,isReady:!0})),n._formState.isReady=!0,c},[n]),I.useEffect(()=>n._disableForm(e.disabled),[n,e.disabled]),I.useEffect(()=>{e.mode&&(n._options.mode=e.mode),e.reValidateMode&&(n._options.reValidateMode=e.reValidateMode)},[n,e.mode,e.reValidateMode]),I.useEffect(()=>{e.errors&&(n._setErrors(e.errors),n._focusError())},[n,e.errors]),I.useEffect(()=>{e.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})},[n,e.shouldUnregister]),I.useEffect(()=>{if(n._proxyFormState.isDirty){const c=n._getDirty();c!==a.isDirty&&n._subjects.state.next({isDirty:c})}},[n,a.isDirty]),I.useEffect(()=>{e.values&&!ee(e.values,r.current)?(n._reset(e.values,{keepFieldsRef:!0,...n._options.resetOptions}),r.current=e.values,o(c=>({...c}))):n._resetDefaultValues()},[n,e.values]),I.useEffect(()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()}),s.current.formState=mt(a,n),s.current}export{pt as u};
//# sourceMappingURL=forms-BcM1ev41.js.map
