"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRoutes = void 0;
const express_1 = require("express");
const response_1 = require("../utils/response");
const user_model_1 = require("../models/user.model");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = (0, express_1.Router)();
exports.userRoutes = router;
// Get user profile
router.get('/profile', auth_middleware_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const user = await user_model_1.User.findById(userId).select('-analytics');
        if (!user) {
            const response = response_1.ResponseUtil.error('User not found', 404);
            return res.status(response.statusCode).json(response);
        }
        const response = response_1.ResponseUtil.success(user, 'User profile retrieved');
        return res.status(response.statusCode).json(response);
    }
    catch (error) {
        const response = response_1.ResponseUtil.error('Failed to retrieve user profile', 500);
        return res.status(response.statusCode).json(response);
    }
});
// Update user profile
router.put('/profile', async (req, res) => {
    try {
        const { userId } = req.query;
        if (!userId) {
            const response = response_1.ResponseUtil.error('User ID is required', 400);
            return res.status(response.statusCode).json(response);
        }
        const user = await user_model_1.User.findByIdAndUpdate(userId, { ...req.body, updatedAt: new Date() }, { new: true, runValidators: true }).select('-analytics');
        if (!user) {
            const response = response_1.ResponseUtil.error('User not found', 404);
            return res.status(response.statusCode).json(response);
        }
        const response = response_1.ResponseUtil.success(user, 'Profile updated successfully');
        return res.status(response.statusCode).json(response);
    }
    catch (error) {
        const response = response_1.ResponseUtil.error('Failed to update profile', 500);
        return res.status(response.statusCode).json(response);
    }
});
// Get user analytics
router.get('/analytics', auth_middleware_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const user = await user_model_1.User.findById(userId).select('analytics');
        if (!user) {
            const response = response_1.ResponseUtil.error('User not found', 404);
            return res.status(response.statusCode).json(response);
        }
        const analytics = user.analytics || {
            profileViews: 0,
            searchAppearances: 0,
            applicationsSent: 0,
            interviewsScheduled: 0,
            offersReceived: 0,
            loginStreak: 0,
            totalLogins: 0,
            averageSessionDuration: 0,
            responseRate: 0,
            interviewRate: 0,
            offerRate: 0,
        };
        const response = response_1.ResponseUtil.success(analytics, 'User analytics retrieved successfully');
        return res.status(response.statusCode).json(response);
    }
    catch (error) {
        const response = response_1.ResponseUtil.error('Failed to retrieve user analytics', 500);
        return res.status(response.statusCode).json(response);
    }
});
//# sourceMappingURL=user.routes.js.map