"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRoutes = void 0;
const express_1 = require("express");
const response_1 = require("../utils/response");
const router = (0, express_1.Router)();
exports.userRoutes = router;
// Get user profile
router.get('/profile', (req, res) => {
    // Mock response - implement actual logic
    const response = response_1.ResponseUtil.success({
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
    }, 'User profile retrieved');
    res.status(response.statusCode).json(response);
});
// Update user profile
router.put('/profile', (req, res) => {
    // Mock response - implement actual logic
    const response = response_1.ResponseUtil.success({
        message: 'Profile updated successfully',
    });
    res.status(response.statusCode).json(response);
});
//# sourceMappingURL=user.routes.js.map