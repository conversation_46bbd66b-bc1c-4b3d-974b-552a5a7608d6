import { Router } from 'express';
import { ResponseUtil } from '../utils/response';
import { User } from '../models/user.model';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();

// Get user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;

    const user = await User.findById(userId).select('-analytics');
    if (!user) {
      const response = ResponseUtil.error('User not found', 404);
      return res.status(response.statusCode).json(response);
    }

    const response = ResponseUtil.success(user, 'User profile retrieved');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve user profile', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Update user profile
router.put('/profile', async (req, res) => {
  try {
    const { userId } = req.query;
    if (!userId) {
      const response = ResponseUtil.error('User ID is required', 400);
      return res.status(response.statusCode).json(response);
    }
    
    const user = await User.findByIdAndUpdate(
      userId,
      { ...req.body, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).select('-analytics');
    
    if (!user) {
      const response = ResponseUtil.error('User not found', 404);
      return res.status(response.statusCode).json(response);
    }
    
    const response = ResponseUtil.success(user, 'Profile updated successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to update profile', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Get user analytics
router.get('/analytics', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;

    const user = await User.findById(userId).select('analytics');
    if (!user) {
      const response = ResponseUtil.error('User not found', 404);
      return res.status(response.statusCode).json(response);
    }

    const analytics = user.analytics || {
      profileViews: 0,
      searchAppearances: 0,
      applicationsSent: 0,
      interviewsScheduled: 0,
      offersReceived: 0,
      loginStreak: 0,
      totalLogins: 0,
      averageSessionDuration: 0,
      responseRate: 0,
      interviewRate: 0,
      offerRate: 0,
    };
    
    const response = ResponseUtil.success(
      analytics,
      'User analytics retrieved successfully'
    );

    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve user analytics', 500);
    return res.status(response.statusCode).json(response);
  }
});

export { router as userRoutes };
