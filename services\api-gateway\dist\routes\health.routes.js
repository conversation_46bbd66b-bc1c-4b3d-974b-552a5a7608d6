"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthCheckRoutes = void 0;
const express_1 = require("express");
const connection_1 = require("../database/connection");
const response_1 = require("../utils/response");
const service_registry_1 = require("../services/service-registry");
const os = __importStar(require("os"));
const router = (0, express_1.Router)();
exports.healthCheckRoutes = router;
/**
 * Gateway health check
 */
router.get('/', (req, res) => {
    const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: process.env.API_VERSION ?? '1.0.0',
        database: {
            connected: connection_1.database.isHealthy(),
            state: 'connected', // Simplified for now
        },
        services: service_registry_1.serviceRegistry.getServiceHealth(),
    };
    const isHealthy = health.database.connected &&
        Object.values(health.services).some(service => service.isHealthy);
    if (isHealthy) {
        return res.json(response_1.ResponseUtil.success(health, 'API Gateway is healthy'));
    }
    else {
        return res.status(503).json({
            success: false,
            message: 'API Gateway is unhealthy',
            data: health,
        });
    }
});
/**
 * Detailed health check
 */
router.get('/detailed', (req, res) => {
    const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: process.env.API_VERSION ?? '1.0.0',
        system: {
            memory: {
                used: process.memoryUsage(),
                free: os.freemem(),
                total: os.totalmem(),
            },
            cpu: {
                usage: process.cpuUsage(),
                loadavg: os.loadavg(),
            },
            platform: process.platform,
            nodeVersion: process.version,
        },
        database: {
            connected: connection_1.database.isHealthy(),
            state: 'connected', // Simplified for now
        },
        services: service_registry_1.serviceRegistry.getServiceHealth(),
    };
    const healthyServices = service_registry_1.serviceRegistry.getHealthyServices().length;
    const totalServices = service_registry_1.serviceRegistry.getServiceList().length;
    health.status =
        connection_1.database.isHealthy() && healthyServices > 0 ? 'healthy' : 'unhealthy';
    const statusCode = health.status === 'healthy' ? 200 : 503;
    return res.status(statusCode).json({
        success: health.status === 'healthy',
        message: `API Gateway is ${health.status}. ${healthyServices}/${totalServices} services healthy.`,
        data: health,
    });
});
/**
 * Readiness check
 */
router.get('/ready', (req, res) => {
    const isReady = connection_1.database.isHealthy() && service_registry_1.serviceRegistry.getHealthyServices().length > 0;
    if (isReady) {
        return res.json(response_1.ResponseUtil.success({ ready: true }, 'API Gateway is ready'));
    }
    else {
        return res.status(503).json({
            success: false,
            message: 'API Gateway is not ready',
            data: { ready: false },
        });
    }
});
/**
 * Liveness check
 */
router.get('/live', (req, res) => {
    return res.json(response_1.ResponseUtil.success({ alive: true }, 'API Gateway is alive'));
});
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function getConnectionStateDescription(state) {
    const states = {
        0: 'disconnected',
        1: 'connected',
        2: 'connecting',
        3: 'disconnecting',
        99: 'uninitialized',
    };
    return states[state] || 'unknown';
}
//# sourceMappingURL=health.routes.js.map