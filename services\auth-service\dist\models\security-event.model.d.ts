import { Schema } from 'mongoose';
export declare const SecurityEvent: import("mongoose").Model<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    userId: import("mongoose").Types.ObjectId;
    userAgent: string;
    eventType: "login" | "logout" | "failed_login" | "password_change" | "account_locked" | "suspicious_activity" | "2fa_enabled" | "2fa_disabled" | "password_reset" | "user_registered";
    ipAddress: string;
    riskLevel: "low" | "medium" | "high";
    location?: {
        country?: string | null;
        city?: string | null;
        region?: string | null;
    } | null;
    metadata?: any;
}, {}, {}, {}, import("mongoose").Document<unknown, {}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    userId: import("mongoose").Types.ObjectId;
    userAgent: string;
    eventType: "login" | "logout" | "failed_login" | "password_change" | "account_locked" | "suspicious_activity" | "2fa_enabled" | "2fa_disabled" | "password_reset" | "user_registered";
    ipAddress: string;
    riskLevel: "low" | "medium" | "high";
    location?: {
        country?: string | null;
        city?: string | null;
        region?: string | null;
    } | null;
    metadata?: any;
}, {}, {
    timestamps: true;
    versionKey: false;
}> & {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    userId: import("mongoose").Types.ObjectId;
    userAgent: string;
    eventType: "login" | "logout" | "failed_login" | "password_change" | "account_locked" | "suspicious_activity" | "2fa_enabled" | "2fa_disabled" | "password_reset" | "user_registered";
    ipAddress: string;
    riskLevel: "low" | "medium" | "high";
    location?: {
        country?: string | null;
        city?: string | null;
        region?: string | null;
    } | null;
    metadata?: any;
} & {
    _id: import("mongoose").Types.ObjectId;
}, Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    versionKey: false;
}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    userId: import("mongoose").Types.ObjectId;
    userAgent: string;
    eventType: "login" | "logout" | "failed_login" | "password_change" | "account_locked" | "suspicious_activity" | "2fa_enabled" | "2fa_disabled" | "password_reset" | "user_registered";
    ipAddress: string;
    riskLevel: "low" | "medium" | "high";
    location?: {
        country?: string | null;
        city?: string | null;
        region?: string | null;
    } | null;
    metadata?: any;
}, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    userId: import("mongoose").Types.ObjectId;
    userAgent: string;
    eventType: "login" | "logout" | "failed_login" | "password_change" | "account_locked" | "suspicious_activity" | "2fa_enabled" | "2fa_disabled" | "password_reset" | "user_registered";
    ipAddress: string;
    riskLevel: "low" | "medium" | "high";
    location?: {
        country?: string | null;
        city?: string | null;
        region?: string | null;
    } | null;
    metadata?: any;
}>, {}, import("mongoose").ResolveSchemaOptions<{
    timestamps: true;
    versionKey: false;
}>> & import("mongoose").FlatRecord<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    userId: import("mongoose").Types.ObjectId;
    userAgent: string;
    eventType: "login" | "logout" | "failed_login" | "password_change" | "account_locked" | "suspicious_activity" | "2fa_enabled" | "2fa_disabled" | "password_reset" | "user_registered";
    ipAddress: string;
    riskLevel: "low" | "medium" | "high";
    location?: {
        country?: string | null;
        city?: string | null;
        region?: string | null;
    } | null;
    metadata?: any;
}> & {
    _id: import("mongoose").Types.ObjectId;
}>>;
//# sourceMappingURL=security-event.model.d.ts.map