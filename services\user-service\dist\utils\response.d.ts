export interface ApiResponse<T = unknown> {
    success: boolean;
    message: string;
    data?: T | undefined;
    errors?: string[] | undefined;
    timestamp: string;
    path?: string | undefined;
    statusCode: number;
}
export declare class ResponseUtil {
    static success<T>(data: T, message?: string, statusCode?: number): ApiResponse<T>;
    static error(message?: string, statusCode?: number, errors?: string[], path?: string): ApiResponse;
    static created<T>(data: T, message?: string): ApiResponse<T>;
    static updated<T>(data: T, message?: string): ApiResponse<T>;
    static deleted(message?: string): ApiResponse;
}
//# sourceMappingURL=response.d.ts.map