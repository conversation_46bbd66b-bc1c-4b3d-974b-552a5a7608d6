"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceConfig = exports.databaseConfig = exports.appConfig = exports.env = void 0;
const zod_1 = require("zod");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const envSchema = zod_1.z.object({
    NODE_ENV: zod_1.z
        .enum(['development', 'production', 'test'])
        .default('development'),
    PORT: zod_1.z.string().default('3000'),
    MONGODB_URI: zod_1.z.string().default('mongodb://localhost:27017/job_platform'),
    REDIS_URL: zod_1.z.string().default('redis://localhost:6379'),
    CORS_ORIGIN: zod_1.z.string().default('*'),
    LOG_LEVEL: zod_1.z.string().default('info'),
    AUTH_SERVICE_URL: zod_1.z.string().default('http://resume-automator-services-auth-s:3001'),
    USER_SERVICE_URL: zod_1.z.string().default('http://resume-automator-services-user-s:3002'),
    JOB_SERVICE_URL: zod_1.z.string().default('http://resume-automator-services-job-se:3003'),
    RESUME_SERVICE_URL: zod_1.z.string().default('http://resume-automator-services-resume:8080'),
});
exports.env = envSchema.parse(process.env);
exports.appConfig = {
    port: parseInt(exports.env.PORT),
    nodeEnv: exports.env.NODE_ENV,
    corsOrigin: exports.env.CORS_ORIGIN,
    logLevel: exports.env.LOG_LEVEL,
    isDevelopment: exports.env.NODE_ENV === 'development',
    isProduction: exports.env.NODE_ENV === 'production',
    apiVersion: '1.0.0',
};
exports.databaseConfig = {
    mongoUri: exports.env.MONGODB_URI,
    redisUrl: exports.env.REDIS_URL,
};
exports.serviceConfig = {
    authServiceUrl: exports.env.AUTH_SERVICE_URL,
    userServiceUrl: exports.env.USER_SERVICE_URL,
    jobServiceUrl: exports.env.JOB_SERVICE_URL,
    resumeServiceUrl: exports.env.RESUME_SERVICE_URL,
};
//# sourceMappingURL=environment.js.map