import { Types } from 'mongoose';
export declare class SecurityEventService {
    /**
     * Log a security event
     */
    logEvent(eventData: {
        userId: Types.ObjectId;
        eventType: string;
        ipAddress: string;
        userAgent: string;
        metadata?: Record<string, unknown>;
    }): Promise<void>;
    /**
     * Calculate risk level based on event type
     */
    private calculateRiskLevel;
    /**
     * Get location from IP address (mock implementation)
     */
    private getLocationFromIP;
    /**
     * Get security events for a user
     */
    getUserSecurityEvents(userId: Types.ObjectId, limit?: number): Promise<unknown[]>;
    /**
     * Get high-risk security events
     */
    getHighRiskEvents(hours?: number): Promise<unknown[]>;
}
//# sourceMappingURL=security-event.service.d.ts.map