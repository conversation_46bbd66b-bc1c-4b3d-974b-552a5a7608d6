export declare class EncryptionUtils {
    static hashPassword(password: string): Promise<string>;
    static comparePassword(password: string, hashedPassword: string): Promise<boolean>;
    static generateAccessToken(payload: Record<string, unknown>): string;
    static generateRefreshToken(payload: Record<string, unknown>): string;
    static verifyAccessToken(token: string): Record<string, unknown>;
    static verifyRefreshToken(token: string): Record<string, unknown>;
    static generateRandomToken(length?: number): string;
    static generateUUID(): string;
    static encrypt(text: string): string;
    static decrypt(encryptedData: string): string;
}
//# sourceMappingURL=encryption.d.ts.map