version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: job-platform-mongodb-prod
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: job_platform
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data_prod:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - job-platform-network-prod
    command: ["mongod", "--auth"]

  # Redis for caching and sessions
  redis:
    image: redis:7.4-alpine
    container_name: job-platform-redis-prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data_prod:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    networks:
      - job-platform-network-prod

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
      target: production
    container_name: job-platform-api-gateway-prod
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - MONGODB_URI=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD}@mongodb:27017/job_platform?authSource=admin
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - CORS_ORIGIN=${CORS_ORIGIN:-*}
      - AUTH_SERVICE_URL=http://auth-service:8080
      - USER_SERVICE_URL=http://user-service:8080
      - JOB_SERVICE_URL=http://job-service:8080
      - RESUME_SERVICE_URL=http://resume-service:8080
    depends_on:
      - mongodb
      - redis
    networks:
      - job-platform-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Auth Service
  auth-service:
    build:
      context: .
      dockerfile: services/auth-service/Dockerfile
      target: production
    container_name: job-platform-auth-service-prod
    restart: unless-stopped
    ports:
      - "8081:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - MONGODB_URI=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD}@mongodb:27017/job_platform?authSource=admin
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - FROM_EMAIL=${FROM_EMAIL}
      - GOOGLE_CALLBACK_URL=${GOOGLE_CALLBACK_URL}
      - FRONTEND_URL=${FRONTEND_URL}
      - USER_SERVICE_URL=http://user-service:8080
    depends_on:
      - mongodb
      - redis
    networks:
      - job-platform-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # User Service
  user-service:
    build:
      context: .
      dockerfile: services/user-service/Dockerfile
      target: production
    container_name: job-platform-user-service-prod
    restart: unless-stopped
    ports:
      - "8082:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - MONGODB_URI=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD}@mongodb:27017/job_platform?authSource=admin
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - AUTH_SERVICE_URL=http://auth-service:8080
    depends_on:
      - mongodb
      - redis
    networks:
      - job-platform-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Job Service
  job-service:
    build:
      context: .
      dockerfile: services/job-service/Dockerfile
      target: production
    container_name: job-platform-job-service-prod
    restart: unless-stopped
    ports:
      - "8083:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - MONGODB_URI=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD}@mongodb:27017/job_platform?authSource=admin
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - USER_SERVICE_URL=http://user-service:8080
    depends_on:
      - mongodb
      - redis
    networks:
      - job-platform-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Resume Service
  resume-service:
    build:
      context: .
      dockerfile: services/resume-service/Dockerfile
      target: production
    container_name: job-platform-resume-service-prod
    restart: unless-stopped
    ports:
      - "8084:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - MONGODB_URI=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD}@mongodb:27017/job_platform?authSource=admin
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
      - AWS_REGION=${AWS_REGION}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_S3_BUCKET=${AWS_S3_BUCKET}
      - USER_SERVICE_URL=http://user-service:8080
    depends_on:
      - mongodb
      - redis
    networks:
      - job-platform-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Analytics Service (commented out - not implemented yet)
  # analytics-service:
  #   build:
  #     context: .
  #     dockerfile: services/analytics-service/Dockerfile
  #     target: production
  #   container_name: job-platform-analytics-service-prod
  #   restart: unless-stopped
  #   ports:
  #     - "8085:8080"
  #   environment:
  #     - NODE_ENV=production
  #     - PORT=8080
  #     - MONGODB_URI=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD}@mongodb:27017/job_platform?authSource=admin
  #     - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
  #   depends_on:
  #     - mongodb
  #     - redis
  #   networks:
  #     - job-platform-network-prod

  # Notification Service (commented out - not implemented yet)
  # notification-service:
  #   build:
  #     context: .
  #     dockerfile: services/notification-service/Dockerfile
  #     target: production
  #   container_name: job-platform-notification-service-prod
  #   restart: unless-stopped
  #   ports:
  #     - "8086:8080"
  #   environment:
  #     - NODE_ENV=production
  #     - PORT=8080
  #     - MONGODB_URI=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD}@mongodb:27017/job_platform?authSource=admin
  #     - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
  #     - SENDGRID_API_KEY=${SENDGRID_API_KEY}
  #     - FROM_EMAIL=${FROM_EMAIL}
  #   depends_on:
  #     - mongodb
  #     - redis
  #   networks:
  #     - job-platform-network-prod

  # Integration Service (commented out - not implemented yet)
  # integration-service:
  #   build:
  #     context: .
  #     dockerfile: services/integration-service/Dockerfile
  #     target: production
  #   container_name: job-platform-integration-service-prod
  #   restart: unless-stopped
  #   ports:
  #     - "8087:8080"
  #   environment:
  #     - NODE_ENV=production
  #     - PORT=8080
  #     - MONGODB_URI=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD}@mongodb:27017/job_platform?authSource=admin
  #     - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
  #     - LINKEDIN_CLIENT_ID=${LINKEDIN_CLIENT_ID}
  #     - LINKEDIN_CLIENT_SECRET=${LINKEDIN_CLIENT_SECRET}
  #     - INDEED_API_KEY=${INDEED_API_KEY}
  #     - GLASSDOOR_API_KEY=${GLASSDOOR_API_KEY}
  #   depends_on:
  #     - mongodb
  #     - redis
  #   networks:
  #     - job-platform-network-prod

  # Payment Service (commented out - not implemented yet)
  # payment-service:
  #   build:
  #     context: .
  #     dockerfile: services/payment-service/Dockerfile
  #     target: production
  #   container_name: job-platform-payment-service-prod
  #   restart: unless-stopped
  #   ports:
  #     - "8088:8080"
  #   environment:
  #     - NODE_ENV=production
  #     - PORT=8080
  #     - MONGODB_URI=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD}@mongodb:27017/job_platform?authSource=admin
  #     - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379
  #     - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
  #     - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
  #   depends_on:
  #     - mongodb
  #     - redis
  #   networks:
  #     - job-platform-network-prod

  # Frontend Service
  frontend-service:
    build:
      context: .
      dockerfile: services/frontend-service/Dockerfile
    container_name: job-platform-frontend-prod
    restart: unless-stopped
    ports:
      - "8085:8080"
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=${FRONTEND_API_BASE_URL:-http://localhost:8080/api/v1}
      - VITE_APP_NAME=Job Application Platform
      - VITE_APP_VERSION=1.0.0
      - VITE_APP_ENV=production
    depends_on:
      - api-gateway
    networks:
      - job-platform-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongodb_data_prod:
  redis_data_prod:

networks:
  job-platform-network-prod:
    driver: bridge