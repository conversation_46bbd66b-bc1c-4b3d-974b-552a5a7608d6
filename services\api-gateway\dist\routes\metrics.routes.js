"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.metricsRoutes = void 0;
const express_1 = require("express");
const response_1 = require("../utils/response");
const service_registry_1 = require("../services/service-registry");
const os = __importStar(require("os"));
const router = (0, express_1.Router)();
exports.metricsRoutes = router;
// Note: In production, add authentication middleware here
/**
 * Get gateway metrics
 */
router.get('/', (req, res) => {
    const metrics = {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        system: {
            platform: process.platform,
            nodeVersion: process.version,
            arch: process.arch,
            loadavg: os.loadavg(),
            freemem: os.freemem(),
            totalmem: os.totalmem(),
            cpus: os.cpus().length,
        },
        services: {
            total: service_registry_1.serviceRegistry.getServiceList().length,
            healthy: service_registry_1.serviceRegistry.getHealthyServices().length,
            details: service_registry_1.serviceRegistry.getServiceHealth(),
        },
    };
    res.json(response_1.ResponseUtil.success(metrics, 'Gateway metrics retrieved'));
});
/**
 * Get service-specific metrics
 */
router.get('/services', (req, res) => {
    const serviceMetrics = {
        timestamp: new Date().toISOString(),
        services: service_registry_1.serviceRegistry.getServiceHealth(),
        summary: {
            total: service_registry_1.serviceRegistry.getServiceList().length,
            healthy: service_registry_1.serviceRegistry.getHealthyServices().length,
            unhealthy: service_registry_1.serviceRegistry.getServiceList().length -
                service_registry_1.serviceRegistry.getHealthyServices().length,
        },
    };
    res.json(response_1.ResponseUtil.success(serviceMetrics, 'Service metrics retrieved'));
});
/**
 * Get performance metrics
 */
router.get('/performance', (req, res) => {
    const performanceMetrics = {
        timestamp: new Date().toISOString(),
        process: {
            uptime: process.uptime(),
            memory: {
                ...process.memoryUsage(),
                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
                external: process.memoryUsage().external ?? 0,
            },
            cpu: process.cpuUsage(),
            pid: process.pid,
            ppid: process.ppid,
        },
        system: {
            loadavg: os.loadavg(),
            freemem: os.freemem(),
            totalmem: os.totalmem(),
            uptime: os.uptime(),
        },
        eventLoop: {
            // Note: These would require additional monitoring libraries in production
            lag: 0, // Placeholder - would use @nodejs/node-report or similar
            utilization: 0, // Placeholder
        },
    };
    res.json(response_1.ResponseUtil.success(performanceMetrics, 'Performance metrics retrieved'));
});
/**
 * Reset service health checks
 */
router.post('/services/health/reset', (req, res) => {
    // This would trigger immediate health checks for all services
    // Implementation would depend on the service registry capabilities
    res.json(response_1.ResponseUtil.success({ message: 'Health checks reset initiated' }, 'Health checks reset'));
});
//# sourceMappingURL=metrics.routes.js.map