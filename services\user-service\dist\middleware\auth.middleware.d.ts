import { Request, Response, NextFunction } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                role: string;
                sessionId: string;
            };
        }
    }
}
/**
 * JWT Authentication Middleware
 * Extracts and verifies JWT token from Authorization header
 * Sets req.user with authenticated user information
 */
export declare const authenticateToken: (req: Request, res: Response, next: NextFunction) => void;
/**
 * Optional Authentication Middleware
 * Similar to authenticateToken but doesn't fail if no token is provided
 * Useful for endpoints that work for both authenticated and unauthenticated users
 */
export declare const optionalAuth: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=auth.middleware.d.ts.map