"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const auth_service_1 = require("../services/auth.service");
const response_1 = require("../utils/response");
const logger_1 = require("../utils/logger");
// import { z } from 'zod';
const crypto_1 = require("crypto");
const encryption_1 = require("../utils/encryption");
class AuthController {
    authService;
    constructor() {
        this.authService = new auth_service_1.AuthService();
    }
    register = async (req, res, next) => {
        try {
            const userData = req.body;
            const ipAddress = req.ip ?? req.socket.remoteAddress ?? 'unknown';
            const result = await this.authService.register(userData, ipAddress);
            const response = response_1.ResponseUtil.created(result, 'User registered successfully');
            res.status(response.statusCode).json(response);
        }
        catch (error) {
            next(error);
        }
    };
    login = async (req, res, next) => {
        try {
            const loginData = req.body;
            const ipAddress = req.ip ?? req.socket.remoteAddress ?? 'unknown';
            const userAgent = req.headers['user-agent'] ?? 'unknown';
            const result = await this.authService.login(loginData, ipAddress, userAgent);
            const response = response_1.ResponseUtil.success(result, 'Login successful');
            res.status(response.statusCode).json(response);
        }
        catch (error) {
            next(error);
        }
    };
    googleCallback = (req, res, next) => {
        try {
            // User is available in req.user after passport authentication
            const user = req.user;
            if (!user) {
                // Redirect to frontend with error
                const frontendUrl = process.env.FRONTEND_URL || 'https://jobs-app-ydwim.ondigitalocean.app';
                const errorUrl = `${frontendUrl}/login?error=oauth_failed&message=Google authentication failed`;
                return res.redirect(errorUrl);
            }
            // Create session and generate tokens
            const sessionId = (0, crypto_1.randomUUID)();
            const tokens = {
                accessToken: encryption_1.EncryptionUtils.generateAccessToken({
                    userId: user.id,
                    email: user.email,
                    role: user.role,
                    sessionId,
                }),
                refreshToken: encryption_1.EncryptionUtils.generateRefreshToken({
                    userId: user.id,
                    sessionId,
                    tokenVersion: 1,
                }),
            };
            // Redirect to frontend with tokens as URL parameters
            const frontendUrl = process.env.FRONTEND_URL || 'https://jobs-app-ydwim.ondigitalocean.app';
            const successUrl = `${frontendUrl}/auth/callback?` +
                `access_token=${encodeURIComponent(tokens.accessToken)}&` +
                `refresh_token=${encodeURIComponent(tokens.refreshToken)}&` +
                `user=${encodeURIComponent(JSON.stringify({
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                }))}`;
            logger_1.logger.info('Google OAuth successful, redirecting to frontend');
            res.redirect(successUrl);
        }
        catch (error) {
            logger_1.logger.error('Google OAuth callback error:', error);
            // Redirect to frontend with error
            const frontendUrl = process.env.FRONTEND_URL || 'https://jobs-app-ydwim.ondigitalocean.app';
            const errorUrl = `${frontendUrl}/login?error=oauth_error&message=Authentication failed`;
            res.redirect(errorUrl);
        }
    };
    refreshToken = async (req, res, next) => {
        try {
            const { refreshToken } = req.body;
            if (!refreshToken) {
                const response = response_1.ResponseUtil.error('Refresh token is required', 400);
                res.status(response.statusCode).json(response);
                return;
            }
            const result = await this.authService.refreshToken(refreshToken);
            const response = response_1.ResponseUtil.success(result, 'Token refreshed successfully');
            res.status(response.statusCode).json(response);
        }
        catch (error) {
            next(error);
        }
    };
    logout = async (req, res, next) => {
        try {
            const sessionId = req.headers['x-session-id'];
            const authHeader = req.headers.authorization;
            const accessToken = authHeader?.startsWith('Bearer ')
                ? authHeader.substring(7)
                : '';
            if (!sessionId || !accessToken) {
                const response = response_1.ResponseUtil.error('Session ID and access token are required', 400);
                res.status(response.statusCode).json(response);
                return;
            }
            await this.authService.logout(sessionId, accessToken);
            const response = response_1.ResponseUtil.success(null, 'Logout successful');
            res.status(response.statusCode).json(response);
        }
        catch (error) {
            next(error);
        }
    };
    verifyEmail = async (req, res, next) => {
        try {
            const { email, token } = req.body;
            await this.authService.verifyEmail(email, token);
            const response = response_1.ResponseUtil.success(null, 'Email verified successfully');
            res.status(response.statusCode).json(response);
        }
        catch (error) {
            next(error);
        }
    };
    forgotPassword = async (req, res, next) => {
        try {
            const { email } = req.body;
            await this.authService.requestPasswordReset(email);
            const response = response_1.ResponseUtil.success(null, 'Password reset email sent');
            res.status(response.statusCode).json(response);
        }
        catch (error) {
            next(error);
        }
    };
    resetPassword = async (req, res, next) => {
        try {
            const { token, password } = req.body;
            await this.authService.resetPassword(token, password);
            const response = response_1.ResponseUtil.success(null, 'Password reset successfully');
            res.status(response.statusCode).json(response);
        }
        catch (error) {
            next(error);
        }
    };
    changePassword = (req, res, next) => {
        try {
            // This would require authentication middleware
            const userId = req.user.id;
            if (!userId) {
                const response = response_1.ResponseUtil.error('Authentication required', 401);
                res.status(response.statusCode).json(response);
                return;
            }
            // const { currentPassword, newPassword } = req.body as {
            //   currentPassword: string;
            //   newPassword: string;
            // };
            // TODO: Implement change password logic
            const response = response_1.ResponseUtil.success(null, 'Password changed successfully');
            res.status(response.statusCode).json(response);
        }
        catch (error) {
            next(error);
        }
    };
    resendVerification = (req, res, next) => {
        try {
            // const { email } = req.body as { email: string };
            // TODO: Implement resend verification logic
            const response = response_1.ResponseUtil.success(null, 'Verification email sent');
            res.status(response.statusCode).json(response);
        }
        catch (error) {
            next(error);
        }
    };
}
exports.AuthController = AuthController;
//# sourceMappingURL=auth.controller.js.map