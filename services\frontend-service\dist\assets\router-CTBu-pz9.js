import{r as ue,g as Q,a as ce}from"./vendor-D3F3s8fL.js";function fe(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const a in r)if(a!=="default"&&!(a in e)){const l=Object.getOwnPropertyDescriptor(r,a);l&&Object.defineProperty(e,a,l.get?l:{enumerable:!0,get:()=>r[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var s=ue();const he=Q(s),de=fe({__proto__:null,default:he},[s]);var pe=ce();const vt=Q(pe);/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)}var R;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(R||(R={}));const z="popstate";function me(e){e===void 0&&(e={});function t(r,a){let{pathname:l,search:i,hash:u}=r.location;return k("",{pathname:l,search:i,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:j(a)}return ge(t,n,null,e)}function v(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Y(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ve(){return Math.random().toString(36).substr(2,8)}function J(e,t){return{usr:e.state,key:e.key,idx:t}}function k(e,t,n,r){return n===void 0&&(n=null),B({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?L(t):t,{state:n,key:t&&t.key||r||ve()})}function j(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function L(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function ge(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,u=R.Pop,o=null,f=h();f==null&&(f=0,i.replaceState(B({},i.state,{idx:f}),""));function h(){return(i.state||{idx:null}).idx}function c(){u=R.Pop;let d=h(),x=d==null?null:d-f;f=d,o&&o({action:u,location:m.location,delta:x})}function p(d,x){u=R.Push;let E=k(m.location,d,x);f=h()+1;let C=J(E,f),P=m.createHref(E);try{i.pushState(C,"",P)}catch(O){if(O instanceof DOMException&&O.name==="DataCloneError")throw O;a.location.assign(P)}l&&o&&o({action:u,location:m.location,delta:1})}function y(d,x){u=R.Replace;let E=k(m.location,d,x);f=h();let C=J(E,f),P=m.createHref(E);i.replaceState(C,"",P),l&&o&&o({action:u,location:m.location,delta:0})}function g(d){let x=a.location.origin!=="null"?a.location.origin:a.location.href,E=typeof d=="string"?d:j(d);return E=E.replace(/ $/,"%20"),v(x,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,x)}let m={get action(){return u},get location(){return e(a,i)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(z,c),o=d,()=>{a.removeEventListener(z,c),o=null}},createHref(d){return t(a,d)},createURL:g,encodeLocation(d){let x=g(d);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:y,go(d){return i.go(d)}};return m}var A;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(A||(A={}));function ye(e,t,n){return n===void 0&&(n="/"),xe(e,t,n)}function xe(e,t,n,r){let a=typeof t=="string"?L(t):t,l=W(a.pathname||"/",n);if(l==null)return null;let i=Z(e);Ce(i);let u=null;for(let o=0;u==null&&o<i.length;++o){let f=Ne(l);u=Ue(i[o],f)}return u}function Z(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(l,i,u)=>{let o={relativePath:u===void 0?l.path||"":u,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};o.relativePath.startsWith("/")&&(v(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let f=b([r,o.relativePath]),h=n.concat(o);l.children&&l.children.length>0&&(v(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),Z(l.children,t,h,f)),!(l.path==null&&!l.index)&&t.push({path:f,score:Oe(f,l.index),routesMeta:h})};return e.forEach((l,i)=>{var u;if(l.path===""||!((u=l.path)!=null&&u.includes("?")))a(l,i);else for(let o of ee(l.path))a(l,i,o)}),t}function ee(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return a?[l,""]:[l];let i=ee(r.join("/")),u=[];return u.push(...i.map(o=>o===""?l:[l,o].join("/"))),a&&u.push(...i),u.map(o=>e.startsWith("/")&&o===""?"/":o)}function Ce(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Le(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Ee=/^:[\w-]+$/,Pe=3,we=2,Re=1,be=10,Se=-2,K=e=>e==="*";function Oe(e,t){let n=e.split("/"),r=n.length;return n.some(K)&&(r+=Se),t&&(r+=we),n.filter(a=>!K(a)).reduce((a,l)=>a+(Ee.test(l)?Pe:l===""?Re:be),r)}function Le(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function Ue(e,t,n){let{routesMeta:r}=e,a={},l="/",i=[];for(let u=0;u<r.length;++u){let o=r[u],f=u===r.length-1,h=l==="/"?t:t.slice(l.length)||"/",c=Be({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},h),p=o.route;if(!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:b([l,c.pathname]),pathnameBase:ke(b([l,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(l=b([l,c.pathnameBase]))}return i}function Be(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Ie(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:r.reduce((f,h,c)=>{let{paramName:p,isOptional:y}=h;if(p==="*"){let m=u[c]||"";i=l.slice(0,l.length-m.length).replace(/(.)\/+$/,"$1")}const g=u[c];return y&&!g?f[p]=void 0:f[p]=(g||"").replace(/%2F/g,"/"),f},{}),pathname:l,pathnameBase:i,pattern:e}}function Ie(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Y(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,u,o)=>(r.push({paramName:u,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function Ne(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Y(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function W(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function je(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?L(e):e;return{pathname:n?n.startsWith("/")?n:Te(n,t):t,search:$e(r),hash:Me(a)}}function Te(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function _(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function _e(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function F(e,t){let n=_e(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function D(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=L(e):(a=B({},e),v(!a.pathname||!a.pathname.includes("?"),_("?","pathname","search",a)),v(!a.pathname||!a.pathname.includes("#"),_("#","pathname","hash",a)),v(!a.search||!a.search.includes("#"),_("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,u;if(i==null)u=n;else{let c=t.length-1;if(!r&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}u=c>=0?t[c]:"/"}let o=je(a,u),f=i&&i!=="/"&&i.endsWith("/"),h=(l||i===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(f||h)&&(o.pathname+="/"),o}const b=e=>e.join("/").replace(/\/\/+/g,"/"),ke=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),$e=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Me=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function We(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const te=["post","put","patch","delete"];new Set(te);const Fe=["get",...te];new Set(Fe);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},I.apply(this,arguments)}const V=s.createContext(null),De=s.createContext(null),S=s.createContext(null),T=s.createContext(null),w=s.createContext({outlet:null,matches:[],isDataRoute:!1}),ne=s.createContext(null);function Ve(e,t){let{relative:n}=t===void 0?{}:t;U()||v(!1);let{basename:r,navigator:a}=s.useContext(S),{hash:l,pathname:i,search:u}=le(e,{relative:n}),o=i;return r!=="/"&&(o=i==="/"?r:b([r,i])),a.createHref({pathname:o,search:u,hash:l})}function U(){return s.useContext(T)!=null}function N(){return U()||v(!1),s.useContext(T).location}function re(e){s.useContext(S).static||s.useLayoutEffect(e)}function ae(){let{isDataRoute:e}=s.useContext(w);return e?tt():ze()}function ze(){U()||v(!1);let e=s.useContext(V),{basename:t,future:n,navigator:r}=s.useContext(S),{matches:a}=s.useContext(w),{pathname:l}=N(),i=JSON.stringify(F(a,n.v7_relativeSplatPath)),u=s.useRef(!1);return re(()=>{u.current=!0}),s.useCallback(function(f,h){if(h===void 0&&(h={}),!u.current)return;if(typeof f=="number"){r.go(f);return}let c=D(f,JSON.parse(i),l,h.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:b([t,c.pathname])),(h.replace?r.replace:r.push)(c,h.state,h)},[t,r,i,l,e])}function gt(){let{matches:e}=s.useContext(w),t=e[e.length-1];return t?t.params:{}}function le(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=s.useContext(S),{matches:a}=s.useContext(w),{pathname:l}=N(),i=JSON.stringify(F(a,r.v7_relativeSplatPath));return s.useMemo(()=>D(e,JSON.parse(i),l,n==="path"),[e,i,l,n])}function Je(e,t){return Ae(e,t)}function Ae(e,t,n,r){U()||v(!1);let{navigator:a}=s.useContext(S),{matches:l}=s.useContext(w),i=l[l.length-1],u=i?i.params:{};i&&i.pathname;let o=i?i.pathnameBase:"/";i&&i.route;let f=N(),h;if(t){var c;let d=typeof t=="string"?L(t):t;o==="/"||(c=d.pathname)!=null&&c.startsWith(o)||v(!1),h=d}else h=f;let p=h.pathname||"/",y=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let g=ye(e,{pathname:y}),m=He(g&&g.map(d=>Object.assign({},d,{params:Object.assign({},u,d.params),pathname:b([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:b([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),l,n,r);return t&&m?s.createElement(T.Provider,{value:{location:I({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:R.Pop}},m):m}function Ke(){let e=et(),t=We(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return s.createElement(s.Fragment,null,s.createElement("h2",null,"Unexpected Application Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),n?s.createElement("pre",{style:a},n):null,null)}const qe=s.createElement(Ke,null);class Ge extends s.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?s.createElement(w.Provider,{value:this.props.routeContext},s.createElement(ne.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Xe(e){let{routeContext:t,match:n,children:r}=e,a=s.useContext(V);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),s.createElement(w.Provider,{value:t},r)}function He(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var l;if(!n)return null;if(n.errors)e=n.matches;else if((l=r)!=null&&l.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,u=(a=n)==null?void 0:a.errors;if(u!=null){let h=i.findIndex(c=>c.route.id&&u?.[c.route.id]!==void 0);h>=0||v(!1),i=i.slice(0,Math.min(i.length,h+1))}let o=!1,f=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<i.length;h++){let c=i[h];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(f=h),c.route.id){let{loaderData:p,errors:y}=n,g=c.route.loader&&p[c.route.id]===void 0&&(!y||y[c.route.id]===void 0);if(c.route.lazy||g){o=!0,f>=0?i=i.slice(0,f+1):i=[i[0]];break}}}return i.reduceRight((h,c,p)=>{let y,g=!1,m=null,d=null;n&&(y=u&&c.route.id?u[c.route.id]:void 0,m=c.route.errorElement||qe,o&&(f<0&&p===0?(nt("route-fallback"),g=!0,d=null):f===p&&(g=!0,d=c.route.hydrateFallbackElement||null)));let x=t.concat(i.slice(0,p+1)),E=()=>{let C;return y?C=m:g?C=d:c.route.Component?C=s.createElement(c.route.Component,null):c.route.element?C=c.route.element:C=h,s.createElement(Xe,{match:c,routeContext:{outlet:h,matches:x,isDataRoute:n!=null},children:C})};return n&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?s.createElement(Ge,{location:n.location,revalidation:n.revalidation,component:m,error:y,children:E(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):E()},null)}var ie=(function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e})(ie||{}),oe=(function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e})(oe||{});function Qe(e){let t=s.useContext(V);return t||v(!1),t}function Ye(e){let t=s.useContext(De);return t||v(!1),t}function Ze(e){let t=s.useContext(w);return t||v(!1),t}function se(e){let t=Ze(),n=t.matches[t.matches.length-1];return n.route.id||v(!1),n.route.id}function et(){var e;let t=s.useContext(ne),n=Ye(),r=se();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function tt(){let{router:e}=Qe(ie.UseNavigateStable),t=se(oe.UseNavigateStable),n=s.useRef(!1);return re(()=>{n.current=!0}),s.useCallback(function(a,l){l===void 0&&(l={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,I({fromRouteId:t},l)))},[e,t])}const q={};function nt(e,t,n){q[e]||(q[e]=!0)}function rt(e,t){e?.v7_startTransition,e?.v7_relativeSplatPath}function yt(e){let{to:t,replace:n,state:r,relative:a}=e;U()||v(!1);let{future:l,static:i}=s.useContext(S),{matches:u}=s.useContext(w),{pathname:o}=N(),f=ae(),h=D(t,F(u,l.v7_relativeSplatPath),o,a==="path"),c=JSON.stringify(h);return s.useEffect(()=>f(JSON.parse(c),{replace:n,state:r,relative:a}),[f,c,a,n,r]),null}function at(e){v(!1)}function lt(e){let{basename:t="/",children:n=null,location:r,navigationType:a=R.Pop,navigator:l,static:i=!1,future:u}=e;U()&&v(!1);let o=t.replace(/^\/*/,"/"),f=s.useMemo(()=>({basename:o,navigator:l,static:i,future:I({v7_relativeSplatPath:!1},u)}),[o,u,l,i]);typeof r=="string"&&(r=L(r));let{pathname:h="/",search:c="",hash:p="",state:y=null,key:g="default"}=r,m=s.useMemo(()=>{let d=W(h,o);return d==null?null:{location:{pathname:d,search:c,hash:p,state:y,key:g},navigationType:a}},[o,h,c,p,y,g,a]);return m==null?null:s.createElement(S.Provider,{value:f},s.createElement(T.Provider,{children:n,value:m}))}function xt(e){let{children:t,location:n}=e;return Je($(t),n)}new Promise(()=>{});function $(e,t){t===void 0&&(t=[]);let n=[];return s.Children.forEach(e,(r,a)=>{if(!s.isValidElement(r))return;let l=[...t,a];if(r.type===s.Fragment){n.push.apply(n,$(r.props.children,l));return}r.type!==at&&v(!1),!r.props.index||!r.props.children||v(!1);let i={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=$(r.props.children,l)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function M(){return M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},M.apply(this,arguments)}function it(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,l;for(l=0;l<r.length;l++)a=r[l],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function ot(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function st(e,t){return e.button===0&&(!t||t==="_self")&&!ot(e)}const ut=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],ct="6";try{window.__reactRouterVersion=ct}catch{}const ft="startTransition",G=de[ft];function Ct(e){let{basename:t,children:n,future:r,window:a}=e,l=s.useRef();l.current==null&&(l.current=me({window:a,v5Compat:!0}));let i=l.current,[u,o]=s.useState({action:i.action,location:i.location}),{v7_startTransition:f}=r||{},h=s.useCallback(c=>{f&&G?G(()=>o(c)):o(c)},[o,f]);return s.useLayoutEffect(()=>i.listen(h),[i,h]),s.useEffect(()=>rt(r),[r]),s.createElement(lt,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:i,future:r})}const ht=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",dt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Et=s.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:l,replace:i,state:u,target:o,to:f,preventScrollReset:h,viewTransition:c}=t,p=it(t,ut),{basename:y}=s.useContext(S),g,m=!1;if(typeof f=="string"&&dt.test(f)&&(g=f,ht))try{let C=new URL(window.location.href),P=f.startsWith("//")?new URL(C.protocol+f):new URL(f),O=W(P.pathname,y);P.origin===C.origin&&O!=null?f=O+P.search+P.hash:m=!0}catch{}let d=Ve(f,{relative:a}),x=pt(f,{replace:i,state:u,target:o,preventScrollReset:h,relative:a,viewTransition:c});function E(C){r&&r(C),C.defaultPrevented||x(C)}return s.createElement("a",M({},p,{href:g||d,onClick:m||l?r:E,ref:n,target:o}))});var X;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(X||(X={}));var H;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(H||(H={}));function pt(e,t){let{target:n,replace:r,state:a,preventScrollReset:l,relative:i,viewTransition:u}=t===void 0?{}:t,o=ae(),f=N(),h=le(e,{relative:i});return s.useCallback(c=>{if(st(c,n)){c.preventDefault();let p=r!==void 0?r:j(f)===j(h);o(e,{replace:p,state:a,preventScrollReset:l,relative:i,viewTransition:u})}},[f,o,h,r,a,n,e,l,i,u])}export{Ct as B,Et as L,yt as N,he as R,vt as a,ae as b,gt as c,xt as d,at as e,s as r,N as u};
//# sourceMappingURL=router-CTBu-pz9.js.map
