export declare class BaseError extends Error {
    statusCode: number;
    isOperational: boolean;
    constructor(message: string, statusCode: number, isOperational?: boolean);
}
export declare class ValidationError extends BaseError {
    constructor(message?: string);
}
export declare class AuthenticationError extends BaseError {
    constructor(message?: string);
}
export declare class AuthorizationError extends BaseError {
    constructor(message?: string);
}
export declare class NotFoundError extends BaseError {
    constructor(message?: string);
}
export declare class ConflictError extends BaseError {
    constructor(message?: string);
}
export declare class InternalServerError extends BaseError {
    constructor(message?: string);
}
export declare class ServiceUnavailableError extends BaseError {
    constructor(message?: string);
}
//# sourceMappingURL=errors.d.ts.map