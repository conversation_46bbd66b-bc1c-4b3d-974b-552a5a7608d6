{"version": 3, "file": "health.routes.js", "sourceRoot": "", "sources": ["../../src/routes/health.routes.ts"], "names": [], "mappings": ";;;AAAA,qCAAiC;AACjC,uDAAkD;AAClD,gDAAiD;AAEjD,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AA6BL,8BAAY;AA3B/B,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;IAC3B,MAAM,MAAM,GAAG;QACb,MAAM,EAAE,SAAS;QACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;QACxB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ;QACjC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,OAAO;QAC3C,QAAQ,EAAE;YACR,SAAS,EAAE,qBAAQ,CAAC,SAAS,EAAE;YAC/B,KAAK,EAAE,qBAAQ,CAAC,kBAAkB,EAAE;SACrC;QACD,MAAM,EAAE,OAAO,CAAC,WAAW,EAAE;KAC9B,CAAC;IAEF,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;IAE5C,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,GAAG,CAAC,IAAI,CAAC,uBAAY,CAAC,OAAO,CAAC,MAAM,EAAE,yBAAyB,CAAC,CAAC,CAAC;IAC3E,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,2BAA2B;YACpC,IAAI,EAAE,MAAM;SACb,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CAAC"}