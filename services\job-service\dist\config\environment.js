"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.googleConfig = exports.serviceConfig = exports.jwtConfig = exports.databaseConfig = exports.appConfig = exports.env = void 0;
const zod_1 = require("zod");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const envSchema = zod_1.z.object({
    NODE_ENV: zod_1.z
        .enum(['development', 'production', 'test'])
        .default('development'),
    PORT: zod_1.z.string().default('8080'),
    MONGODB_URI: zod_1.z.string().default('mongodb://localhost:27017/job_platform'),
    REDIS_URL: zod_1.z.string().default('redis://localhost:6379'),
    JWT_SECRET: zod_1.z.string().default('your-super-secret-jwt-key'),
    JWT_REFRESH_SECRET: zod_1.z.string().default('your-super-secret-refresh-key'),
    JWT_ACCESS_EXPIRES_IN: zod_1.z.string().default('15m'),
    JWT_REFRESH_EXPIRES_IN: zod_1.z.string().default('7d'),
    BCRYPT_ROUNDS: zod_1.z.string().default('12'),
    EMAIL_SERVICE_URL: zod_1.z.string().default('http://notification-service:8080'),
    USER_SERVICE_URL: zod_1.z.string().default('http://user-service:8080'),
    GOOGLE_CLIENT_ID: zod_1.z.string().optional(),
    GOOGLE_CLIENT_SECRET: zod_1.z.string().optional(),
    CORS_ORIGIN: zod_1.z.string().default('*'),
    LOG_LEVEL: zod_1.z.string().default('info'),
});
exports.env = envSchema.parse(process.env);
exports.appConfig = {
    port: parseInt(exports.env.PORT),
    nodeEnv: exports.env.NODE_ENV,
    corsOrigin: exports.env.CORS_ORIGIN,
    logLevel: exports.env.LOG_LEVEL,
    isDevelopment: exports.env.NODE_ENV === 'development',
    isProduction: exports.env.NODE_ENV === 'production',
    apiVersion: '1.0.0',
};
exports.databaseConfig = {
    mongoUri: exports.env.MONGODB_URI,
    redisUrl: exports.env.REDIS_URL,
};
exports.jwtConfig = {
    secret: exports.env.JWT_SECRET,
    refreshSecret: exports.env.JWT_REFRESH_SECRET,
    accessExpiresIn: exports.env.JWT_ACCESS_EXPIRES_IN,
    refreshExpiresIn: exports.env.JWT_REFRESH_EXPIRES_IN,
};
exports.serviceConfig = {
    emailServiceUrl: exports.env.EMAIL_SERVICE_URL,
    userServiceUrl: exports.env.USER_SERVICE_URL,
};
exports.googleConfig = {
    clientId: exports.env.GOOGLE_CLIENT_ID,
    clientSecret: exports.env.GOOGLE_CLIENT_SECRET,
};
//# sourceMappingURL=environment.js.map