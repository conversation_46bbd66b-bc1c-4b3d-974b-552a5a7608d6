"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobCleanupService = void 0;
const logger_1 = require("../utils/logger");
const job_model_1 = require("../models/job.model");
const application_model_1 = require("../models/application.model");
class JobCleanupService {
    /**
     * Clean up expired jobs
     */
    async cleanupExpiredJobs() {
        try {
            logger_1.logger.info('Starting job cleanup process...');
            const now = new Date();
            // Find jobs where expiresAt < current date and status is active
            const expiredJobs = await job_model_1.Job.find({
                expiresAt: { $lt: now },
                status: 'active'
            });
            if (expiredJobs.length > 0) {
                // Update their status to 'expired'
                const result = await job_model_1.Job.updateMany({ _id: { $in: expiredJobs.map(job => job._id) } }, { status: 'expired' });
                logger_1.logger.info(`Job cleanup completed. Expired ${result.modifiedCount} jobs.`);
            }
            else {
                logger_1.logger.info('Job cleanup completed. No expired jobs found.');
            }
        }
        catch (error) {
            logger_1.logger.error('Job cleanup failed:', error);
            throw error;
        }
    }
    /**
     * Archive old applications
     */
    async archiveOldApplications() {
        try {
            logger_1.logger.info('Starting application archival process...');
            const sixMonthsAgo = new Date();
            sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
            // Find applications older than 6 months with final status
            const oldApplications = await application_model_1.Application.find({
                appliedAt: { $lt: sixMonthsAgo },
                status: { $in: ['rejected', 'accepted', 'withdrawn'] }
            });
            if (oldApplications.length > 0) {
                // Archive them by adding an archived flag
                const result = await application_model_1.Application.updateMany({ _id: { $in: oldApplications.map(app => app._id) } }, { archived: true, archivedAt: new Date() });
                logger_1.logger.info(`Application archival completed. ` +
                    `Archived ${result.modifiedCount} applications.`);
            }
            else {
                logger_1.logger.info('Application archival completed. No old applications found.');
            }
        }
        catch (error) {
            logger_1.logger.error('Application archival failed:', error);
            throw error;
        }
    }
}
exports.JobCleanupService = JobCleanupService;
//# sourceMappingURL=job-cleanup.service.js.map