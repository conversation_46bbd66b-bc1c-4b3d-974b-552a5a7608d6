"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisClient = void 0;
const ioredis_1 = __importDefault(require("ioredis"));
const logger_1 = require("../utils/logger");
const environment_1 = require("../config/environment");
class RedisClient {
    client;
    isConnected = false;
    constructor() {
        this.client = new ioredis_1.default(environment_1.databaseConfig.redisUrl, {
            lazyConnect: true,
            maxRetriesPerRequest: 3,
            connectTimeout: 10000,
            commandTimeout: 5000,
        });
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        this.client.on('connect', () => {
            logger_1.logger.info('Redis client connected');
            this.isConnected = true;
        });
        this.client.on('ready', () => {
            logger_1.logger.info('Redis client ready');
        });
        this.client.on('error', error => {
            logger_1.logger.error('Redis client error:', error);
            this.isConnected = false;
        });
        this.client.on('close', () => {
            logger_1.logger.warn('Redis client connection closed');
            this.isConnected = false;
        });
        this.client.on('reconnecting', () => {
            logger_1.logger.info('Redis client reconnecting');
        });
    }
    async connect() {
        try {
            await this.client.connect();
            logger_1.logger.info('Redis connection established');
        }
        catch (error) {
            logger_1.logger.error('Failed to connect to Redis:', error);
            this.isConnected = false;
            throw error;
        }
    }
    async disconnect() {
        try {
            await this.client.quit();
            this.isConnected = false;
            logger_1.logger.info('Redis disconnected');
        }
        catch (error) {
            logger_1.logger.error('Error disconnecting from Redis:', error);
        }
    }
    isHealthy() {
        return this.isConnected && this.client.status === 'ready';
    }
    async safeExecute(operation, fallback) {
        if (!this.isConnected) {
            logger_1.logger.warn('Redis operation skipped - not connected');
            return fallback;
        }
        try {
            return await operation();
        }
        catch (error) {
            logger_1.logger.error('Redis operation failed:', error);
            return fallback;
        }
    }
    // Token blacklisting
    async blacklistToken(token, expiresIn) {
        const key = `blacklist:${token}`;
        await this.safeExecute(() => this.client.setex(key, expiresIn, '1'), undefined);
    }
    async isTokenBlacklisted(token) {
        const key = `blacklist:${token}`;
        return await this.safeExecute(async () => {
            const result = await this.client.get(key);
            return result === '1';
        }, false // Default to not blacklisted if Redis is unavailable
        );
    }
    // Session management
    async setSession(sessionId, data, ttl) {
        const key = `session:${sessionId}`;
        await this.client.setex(key, ttl, JSON.stringify(data));
    }
    async getSession(sessionId) {
        const key = `session:${sessionId}`;
        const result = await this.client.get(key);
        return result ? JSON.parse(result) : null;
    }
    async deleteSession(sessionId) {
        const key = `session:${sessionId}`;
        await this.client.del(key);
    }
    async updateSessionExpiry(sessionId, ttl) {
        const key = `session:${sessionId}`;
        await this.client.expire(key, ttl);
    }
    // Rate limiting
    async incrementCounter(key, window) {
        const multi = this.client.multi();
        multi.incr(key);
        multi.expire(key, window);
        const results = await multi.exec();
        return results?.[0]?.[1] || 0;
    }
    // Account lockout
    async incrementFailedAttempts(identifier, window) {
        const key = `failed_attempts:${identifier}`;
        return await this.incrementCounter(key, window);
    }
    async resetFailedAttempts(identifier) {
        const key = `failed_attempts:${identifier}`;
        await this.client.del(key);
    }
    async lockAccount(identifier, lockDuration) {
        const key = `locked:${identifier}`;
        await this.client.setex(key, lockDuration, '1');
    }
    async isAccountLocked(identifier) {
        const key = `locked:${identifier}`;
        const result = await this.client.get(key);
        return result === '1';
    }
    // Password reset tokens
    async setPasswordResetToken(userId, token, ttl) {
        const key = `password_reset:${userId}`;
        await this.client.setex(key, ttl, token);
    }
    async getPasswordResetToken(userId) {
        const key = `password_reset:${userId}`;
        return await this.client.get(key);
    }
    async deletePasswordResetToken(userId) {
        const key = `password_reset:${userId}`;
        await this.client.del(key);
    }
    // Email verification tokens
    async setEmailVerificationToken(email, token, ttl) {
        const key = `email_verification:${email}`;
        await this.client.setex(key, ttl, token);
    }
    async getEmailVerificationToken(email) {
        const key = `email_verification:${email}`;
        return await this.client.get(key);
    }
    async deleteEmailVerificationToken(email) {
        const key = `email_verification:${email}`;
        await this.client.del(key);
    }
    // Generic cache operations
    async set(key, value, ttl) {
        const serializedValue = JSON.stringify(value);
        if (ttl) {
            await this.client.setex(key, ttl, serializedValue);
        }
        else {
            await this.client.set(key, serializedValue);
        }
    }
    async get(key) {
        const result = await this.client.get(key);
        return result ? JSON.parse(result) : null;
    }
    async del(key) {
        await this.client.del(key);
    }
    async exists(key) {
        const result = await this.client.exists(key);
        return result === 1;
    }
}
exports.RedisClient = RedisClient;
//# sourceMappingURL=redis.service.js.map