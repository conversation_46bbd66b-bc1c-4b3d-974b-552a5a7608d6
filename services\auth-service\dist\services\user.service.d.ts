import { Types, FilterQuery } from 'mongoose';
import { UserDocument } from '../types/auth.types';
import { CreateUserData, UpdateUserData } from '../types/user.types';
export declare class UserService {
    /**
     * Create a new user
     */
    create(userData: CreateUserData): Promise<UserDocument>;
    /**
     * Find user by ID
     */
    findById(id: string | Types.ObjectId): Promise<UserDocument | null>;
    /**
     * Find user by email
     */
    findByEmail(email: string): Promise<UserDocument | null>;
    /**
     * Find user by Google ID
     */
    findByGoogleId(googleId: string): Promise<UserDocument | null>;
    /**
     * Find all users with filters
     */
    findAll(filters?: FilterQuery<UserDocument>): Promise<UserDocument[]>;
    /**
     * Update user
     */
    update(id: string | Types.ObjectId, updateData: UpdateUserData): Promise<UserDocument>;
    /**
     * Update user password
     */
    updatePassword(id: string | Types.ObjectId, hashedPassword: string): Promise<void>;
    /**
     * Update last login timestamp
     */
    updateLastLogin(id: string | Types.ObjectId): Promise<void>;
    /**
     * Verify user email
     */
    verifyEmail(id: string | Types.ObjectId): Promise<void>;
    /**
     * Link Google account to existing user
     */
    linkGoogleAccount(id: string | Types.ObjectId, googleId: string): Promise<void>;
    /**
     * Soft delete user
     */
    softDelete(id: string | Types.ObjectId): Promise<void>;
    /**
     * Restore soft deleted user
     */
    restore(id: string | Types.ObjectId): Promise<void>;
    /**
     * Suspend user account
     */
    suspend(id: string | Types.ObjectId, reason: string): Promise<void>;
    /**
     * Unsuspend user account
     */
    unsuspend(id: string | Types.ObjectId): Promise<void>;
    /**
     * Update user analytics
     */
    updateAnalytics(id: string | Types.ObjectId, analyticsUpdate: Record<string, unknown>): Promise<void>;
    /**
     * Search users
     */
    search(query: {
        searchTerm?: string;
        skills?: string[];
        location?: {
            country?: string;
            city?: string;
        };
        experienceLevel?: string;
        page?: number;
        limit?: number;
    }): Promise<{
        users: UserDocument[];
        total: number;
    }>;
}
//# sourceMappingURL=user.service.d.ts.map