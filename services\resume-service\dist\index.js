"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const morgan_1 = __importDefault(require("morgan"));
const environment_1 = require("./config/environment");
const connection_1 = require("./database/connection");
const logger_1 = require("./utils/logger");
const error_middleware_1 = require("./middleware/error.middleware");
const resume_routes_1 = require("./routes/resume.routes");
const health_routes_1 = require("./routes/health.routes");
const s3_service_1 = require("./services/s3.service");
class ResumeService {
    app;
    server = null;
    s3Service;
    constructor() {
        this.app = (0, express_1.default)();
        // Enable trust proxy for proper IP detection behind reverse proxy
        this.app.set('trust proxy', true);
        this.s3Service = new s3_service_1.S3Service();
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
    }
    setupMiddleware() {
        this.app.use((0, helmet_1.default)());
        this.app.use((0, cors_1.default)({ origin: true, credentials: true }));
        this.app.use((0, compression_1.default)());
        this.app.use(express_1.default.json({ limit: '15mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '15mb' }));
        this.app.use((0, morgan_1.default)(environment_1.appConfig.isDevelopment ? 'dev' : 'combined'));
    }
    setupRoutes() {
        this.app.use('/health', health_routes_1.healthRoutes);
        // Support both /resumes and /api/v1 for API Gateway compatibility
        this.app.use('/resumes', resume_routes_1.resumeRoutes);
        this.app.use('/api/v1', resume_routes_1.resumeRoutes);
        this.app.get('/api/v1', (req, res) => {
            res.json({
                name: 'Job Platform Resume Service',
                version: environment_1.appConfig.apiVersion,
                environment: environment_1.appConfig.nodeEnv,
                timestamp: new Date().toISOString(),
                features: [
                    'Resume Upload',
                    'PDF Processing',
                    'ATS Optimization',
                    'AI Analysis',
                ],
            });
        });
    }
    setupErrorHandling() {
        this.app.use((req, res) => {
            res.status(404).json({
                success: false,
                message: `Route ${req.originalUrl} not found`,
            });
        });
        this.app.use(error_middleware_1.errorHandler);
    }
    async start() {
        try {
            // Start server first to respond to health checks
            const port = environment_1.appConfig.port;
            this.server = this.app.listen(port, '0.0.0.0', () => {
                logger_1.logger.info(`📄 Resume Service running on port ${port}`);
                logger_1.logger.info(`📝 Environment: ${environment_1.appConfig.nodeEnv}`);
                logger_1.logger.info(`🔗 Health Check: http://localhost:${port}/health`);
            });
            // Connect to database after server is running
            try {
                await connection_1.database.connect();
                logger_1.logger.info('Database connected successfully');
            }
            catch (dbError) {
                logger_1.logger.warn('Database connection failed - service will run with limited functionality:', dbError);
                // Continue running without database - some features will be disabled
            }
            // Initialize S3 service
            try {
                await this.s3Service.initialize();
                logger_1.logger.info('S3 service initialized successfully');
            }
            catch (s3Error) {
                logger_1.logger.warn('S3 service initialization failed - file upload features will be disabled:', s3Error);
                // Continue running without S3 - file upload features will be disabled
            }
            process.on('SIGTERM', () => void this.shutdown());
            process.on('SIGINT', () => void this.shutdown());
        }
        catch (error) {
            logger_1.logger.error('Failed to start Resume Service:', error);
            process.exit(1);
        }
    }
    async shutdown() {
        logger_1.logger.info('🔄 Resume Service shutting down...');
        if (this.server) {
            await new Promise(resolve => {
                this.server.close(() => {
                    void connection_1.database.disconnect().then(() => {
                        logger_1.logger.info('✅ Resume Service shutdown completed');
                        process.exit(0);
                    });
                });
                resolve();
            });
        }
    }
}
const resumeService = new ResumeService();
resumeService.start().catch(error => {
    logger_1.logger.error('Failed to start Resume Service:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map