import { z } from 'zod';
export declare const UserValidationSchemas: {
    register: z.ZodObject<{
        email: z.ZodEmail;
        password: z.ZodOptional<z.ZodString>;
        firstName: z.ZodString;
        lastName: z.ZodString;
        googleId: z.ZodOptional<z.ZodString>;
        avatar: z.ZodOptional<z.ZodURL>;
    }, z.core.$strip>;
    login: z.ZodObject<{
        email: z.ZodEmail;
        password: z.ZodString;
        rememberMe: z.ZodOptional<z.ZodBoolean>;
    }, z.core.$strip>;
    updateProfile: z.ZodObject<{
        firstName: z.ZodOptional<z.ZodString>;
        lastName: z.ZodOptional<z.ZodString>;
        avatar: z.ZodOptional<z.ZodURL>;
    }, z.core.$strip>;
    changePassword: z.ZodObject<{
        currentPassword: z.ZodString;
        newPassword: z.ZodString;
        confirmPassword: z.ZodString;
    }, z.core.$strip>;
    resetPassword: z.ZodObject<{
        token: z.ZodString;
        newPassword: z.ZodString;
        confirmPassword: z.ZodString;
    }, z.core.$strip>;
    requestPasswordReset: z.ZodObject<{
        email: z.ZodEmail;
    }, z.core.$strip>;
    verifyEmail: z.ZodObject<{
        email: z.ZodEmail;
        token: z.ZodString;
    }, z.core.$strip>;
    confirmResetPassword: z.ZodObject<{
        token: z.ZodString;
        newPassword: z.ZodString;
        confirmPassword: z.ZodString;
    }, z.core.$strip>;
};
export declare const CommonValidationSchemas: {
    refreshToken: z.ZodObject<{
        refreshToken: z.ZodString;
    }, z.core.$strip>;
    pagination: z.ZodObject<{
        page: z.ZodOptional<z.ZodPipe<z.ZodPipe<z.ZodString, z.ZodTransform<number, string>>, z.ZodNumber>>;
        limit: z.ZodOptional<z.ZodPipe<z.ZodPipe<z.ZodString, z.ZodTransform<number, string>>, z.ZodNumber>>;
        sortBy: z.ZodOptional<z.ZodString>;
        sortOrder: z.ZodOptional<z.ZodEnum<{
            asc: "asc";
            desc: "desc";
        }>>;
    }, z.core.$strip>;
    search: z.ZodObject<{
        q: z.ZodOptional<z.ZodString>;
        category: z.ZodOptional<z.ZodString>;
        status: z.ZodOptional<z.ZodString>;
    }, z.core.$strip>;
    id: z.ZodObject<{
        id: z.ZodString;
    }, z.core.$strip>;
};
export declare const isValidEmail: (email: string) => boolean;
export declare const isStrongPassword: (password: string) => boolean;
export declare const sanitizeString: (str: string) => string;
export declare const sanitizeEmail: (email: string) => string;
//# sourceMappingURL=validation.d.ts.map