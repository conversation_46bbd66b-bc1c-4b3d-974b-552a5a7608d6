import Redis from 'ioredis';
import { logger } from '../utils/logger';
import { databaseConfig } from '../config/environment';

export class RedisClient {
  private client: Redis;
  private isConnected = false;

  constructor() {
    this.client = new Redis(databaseConfig.redisUrl, {
      lazyConnect: true,
      maxRetriesPerRequest: 3,
      connectTimeout: 10000,
      commandTimeout: 5000,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.client.on('connect', () => {
      logger.info('Redis client connected');
      this.isConnected = true;
    });

    this.client.on('ready', () => {
      logger.info('Redis client ready');
    });

    this.client.on('error', error => {
      logger.error('Redis client error:', error);
      this.isConnected = false;
    });

    this.client.on('close', () => {
      logger.warn('Redis client connection closed');
      this.isConnected = false;
    });

    this.client.on('reconnecting', () => {
      logger.info('Redis client reconnecting');
    });
  }

  public async connect(): Promise<void> {
    try {
      await this.client.connect();
      logger.info('Redis connection established');
    } catch (error) {
      logger.error('Failed to connect to Redis:', error);
      this.isConnected = false;
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.client.quit();
      this.isConnected = false;
      logger.info('Redis disconnected');
    } catch (error) {
      logger.error('Error disconnecting from Redis:', error);
    }
  }

  public isHealthy(): boolean {
    return this.isConnected && this.client.status === 'ready';
  }

  private async safeExecute<T>(operation: () => Promise<T>, fallback: T): Promise<T> {
    if (!this.isConnected) {
      logger.warn('Redis operation skipped - not connected');
      return fallback;
    }
    try {
      return await operation();
    } catch (error) {
      logger.error('Redis operation failed:', error);
      return fallback;
    }
  }

  // Token blacklisting
  public async blacklistToken(token: string, expiresIn: number): Promise<void> {
    const key = `blacklist:${token}`;
    await this.safeExecute(
      () => this.client.setex(key, expiresIn, '1'),
      undefined
    );
  }

  public async isTokenBlacklisted(token: string): Promise<boolean> {
    const key = `blacklist:${token}`;
    return await this.safeExecute(
      async () => {
        const result = await this.client.get(key);
        return result === '1';
      },
      false // Default to not blacklisted if Redis is unavailable
    );
  }

  // Session management
  public async setSession(
    sessionId: string,
    data: Record<string, unknown>,
    ttl: number
  ): Promise<void> {
    const key = `session:${sessionId}`;
    await this.client.setex(key, ttl, JSON.stringify(data));
  }

  public async getSession(sessionId: string): Promise<unknown> {
    const key = `session:${sessionId}`;
    const result = await this.client.get(key);
    return result ? JSON.parse(result) : null;
  }

  public async deleteSession(sessionId: string): Promise<void> {
    const key = `session:${sessionId}`;
    await this.client.del(key);
  }

  public async updateSessionExpiry(
    sessionId: string,
    ttl: number
  ): Promise<void> {
    const key = `session:${sessionId}`;
    await this.client.expire(key, ttl);
  }

  // Rate limiting
  public async incrementCounter(key: string, window: number): Promise<number> {
    const multi = this.client.multi();
    multi.incr(key);
    multi.expire(key, window);
    const results = await multi.exec();
    return (results?.[0]?.[1] as number) || 0;
  }

  // Account lockout
  public async incrementFailedAttempts(
    identifier: string,
    window: number
  ): Promise<number> {
    const key = `failed_attempts:${identifier}`;
    return await this.incrementCounter(key, window);
  }

  public async resetFailedAttempts(identifier: string): Promise<void> {
    const key = `failed_attempts:${identifier}`;
    await this.client.del(key);
  }

  public async lockAccount(
    identifier: string,
    lockDuration: number
  ): Promise<void> {
    const key = `locked:${identifier}`;
    await this.client.setex(key, lockDuration, '1');
  }

  public async isAccountLocked(identifier: string): Promise<boolean> {
    const key = `locked:${identifier}`;
    const result = await this.client.get(key);
    return result === '1';
  }

  // Password reset tokens
  public async setPasswordResetToken(
    userId: string,
    token: string,
    ttl: number
  ): Promise<void> {
    const key = `password_reset:${userId}`;
    await this.client.setex(key, ttl, token);
  }

  public async getPasswordResetToken(userId: string): Promise<string | null> {
    const key = `password_reset:${userId}`;
    return await this.client.get(key);
  }

  public async deletePasswordResetToken(userId: string): Promise<void> {
    const key = `password_reset:${userId}`;
    await this.client.del(key);
  }

  // Email verification tokens
  public async setEmailVerificationToken(
    email: string,
    token: string,
    ttl: number
  ): Promise<void> {
    const key = `email_verification:${email}`;
    await this.client.setex(key, ttl, token);
  }

  public async getEmailVerificationToken(
    email: string
  ): Promise<string | null> {
    const key = `email_verification:${email}`;
    return await this.client.get(key);
  }

  public async deleteEmailVerificationToken(email: string): Promise<void> {
    const key = `email_verification:${email}`;
    await this.client.del(key);
  }

  // Generic cache operations
  public async set(key: string, value: unknown, ttl?: number): Promise<void> {
    const serializedValue = JSON.stringify(value);
    if (ttl) {
      await this.client.setex(key, ttl, serializedValue);
    } else {
      await this.client.set(key, serializedValue);
    }
  }

  public async get(key: string): Promise<unknown> {
    const result = await this.client.get(key);
    return result ? JSON.parse(result) : null;
  }

  public async del(key: string): Promise<void> {
    await this.client.del(key);
  }

  public async exists(key: string): Promise<boolean> {
    const result = await this.client.exists(key);
    return result === 1;
  }
}
