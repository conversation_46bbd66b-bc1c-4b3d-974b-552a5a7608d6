import { Request } from 'express';
export interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: 'admin' | 'user' | 'premium' | 'enterprise';
    subscriptionTier: 'free' | 'basic' | 'premium' | 'enterprise';
    isVerified: boolean;
    isActive: boolean;
    isSuspended: boolean;
    createdAt: Date;
    updatedAt: Date;
    lastLoginAt?: Date;
    avatar?: string;
    googleId?: string;
    analytics?: {
        jobApplications: number;
        resumesCreated: number;
        profileViews: number;
        lastActivityAt: Date;
    } | null;
}
export interface AuthenticatedRequest extends Request {
    user?: User;
}
export interface AppRequest extends Request {
    user?: User;
}
//# sourceMappingURL=user.types.d.ts.map