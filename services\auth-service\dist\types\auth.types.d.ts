import { Document } from 'mongoose';
export interface UserDocument extends Document {
    id: string;
    email: string;
    password?: string;
    googleId?: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    role: 'admin' | 'user' | 'premium' | 'enterprise';
    subscriptionTier: 'free' | 'basic' | 'premium' | 'enterprise';
    isVerified: boolean;
    isActive: boolean;
    isSuspended: boolean;
    suspendedAt?: Date;
    suspendedReason?: string;
    emailVerificationToken?: string;
    emailVerificationExpires?: Date;
    lastLoginAt?: Date;
    passwordChangedAt?: Date;
    profile?: {
        bio?: string;
        phoneNumber?: string;
        location?: {
            country?: string;
            state?: string;
            city?: string;
            zipCode?: string;
            coordinates?: {
                lat: number;
                lng: number;
            };
            remote?: boolean;
        };
        website?: string;
        linkedin?: string;
        github?: string;
        portfolio?: string;
        currentPosition?: string;
        currentCompany?: string;
        yearsOfExperience?: number;
        expectedSalary?: {
            min: number;
            max: number;
            currency: string;
        };
        dateOfBirth?: Date;
        nationality?: string;
        languages?: Array<{
            language: string;
            proficiency: 'basic' | 'conversational' | 'fluent' | 'native';
        }>;
        skills?: Array<{
            name: string;
            level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
            verified: boolean;
            yearsOfExperience: number;
        }>;
        education?: Array<{
            institution: string;
            degree: string;
            field: string;
            startDate: Date;
            endDate: Date;
            gpa: number;
            description: string;
        }>;
        experience?: Array<{
            company: string;
            position: string;
            startDate: Date;
            endDate: Date;
            current: boolean;
            description: string;
            skills: string[];
            achievements: string[];
        }>;
        profileVisibility: 'public' | 'private' | 'connections';
        searchable: boolean;
    };
    preferences?: {
        notifications?: {
            email?: {
                jobAlerts: boolean;
                applicationUpdates: boolean;
                marketingEmails: boolean;
                weeklyDigest: boolean;
            };
            push?: {
                jobAlerts: boolean;
                applicationUpdates: boolean;
                messages: boolean;
            };
            sms?: {
                criticalUpdates: boolean;
                jobAlerts: boolean;
            };
        };
        jobSearch?: {
            preferredJobTypes: string[];
            preferredLocations: Array<{
                country: string;
                state: string;
                city: string;
                remote: boolean;
            }>;
            salaryRange: {
                min: number;
                max: number;
                currency: string;
            };
            remoteWork: boolean;
            willingToRelocate: boolean;
            availabilityDate: Date;
        };
        privacy?: {
            showProfile: boolean;
            showSalaryExpectations: boolean;
            allowRecruiterContact: boolean;
            showApplicationHistory: boolean;
        };
        interface?: {
            theme: 'light' | 'dark' | 'system';
            language: string;
            timezone: string;
            dateFormat: string;
        };
    };
    analytics?: {
        profileViews: number;
        searchAppearances: number;
        applicationsSent: number;
        interviewsScheduled: number;
        offersReceived: number;
        loginStreak: number;
        totalLogins: number;
        averageSessionDuration: number;
        lastActiveAt: Date;
        featuresUsed: string[];
        premiumFeaturesUsed: string[];
        responseRate: number;
        interviewRate: number;
        offerRate: number;
    };
    createdAt: Date;
    updatedAt: Date;
}
export interface LoginCredentials {
    email: string;
    password: string;
}
export interface RegisterData {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
}
export interface AuthResponse {
    success: boolean;
    message: string;
    user?: Partial<UserDocument>;
    token?: string;
}
export interface JWTPayload {
    userId: string;
    email: string;
    role: string;
    iat?: number;
    exp?: number;
}
export interface LoginRequest {
    email: string;
    password: string;
    rememberMe?: boolean;
}
export interface LoginResponse {
    success: boolean;
    message: string;
    user?: Partial<UserDocument> & {
        permissions?: string[];
    };
    accessToken?: string;
    refreshToken?: string;
    tokens?: {
        accessToken: string;
        refreshToken: string;
    };
    expiresIn?: number;
}
export interface GoogleOAuthUser {
    googleId: string;
    email: string;
    firstName: string;
    lastName: string;
    avatar?: string | undefined;
    emailVerified?: boolean;
}
export interface CreateUserRequest {
    email: string;
    password?: string;
    firstName: string;
    lastName: string;
    googleId?: string;
    avatar?: string | undefined;
    role?: string;
    subscriptionTier?: string;
}
export interface RefreshTokenPayload {
    userId: string;
    tokenId: string;
    iat?: number;
    exp?: number;
}
//# sourceMappingURL=auth.types.d.ts.map