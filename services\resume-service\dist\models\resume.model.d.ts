import { Schema } from 'mongoose';
export declare const Resume: import("mongoose").Model<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    userId: string;
    title: string;
    fileName: string;
    filePath: string;
    fileSize: number;
    fileType: string;
    isPublic: boolean;
    isDefault: boolean;
    tags: string[];
    downloadCount: number;
    viewCount: number;
    lastUsedAt?: NativeDate | null;
    content?: {
        experience: import("mongoose").Types.DocumentArray<{
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }> & {
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }>;
        education: import("mongoose").Types.DocumentArray<{
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }> & {
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }>;
        skills: string[];
        certifications: import("mongoose").Types.DocumentArray<{
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }> & {
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }>;
        summary?: string | null;
        personalInfo?: {
            firstName?: string | null;
            lastName?: string | null;
            email?: string | null;
            phone?: string | null;
            address?: string | null;
            linkedin?: string | null;
            github?: string | null;
            website?: string | null;
        } | null;
    } | null;
    analysis?: {
        recommendedImprovements: string[];
        overallScore?: number | null;
        atsScore?: number | null;
        keywordMatch?: number | null;
        skillsMatch?: number | null;
        experienceMatch?: number | null;
        educationMatch?: number | null;
        lastAnalyzed?: NativeDate | null;
    } | null;
}, {}, {}, {}, import("mongoose").Document<unknown, {}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    userId: string;
    title: string;
    fileName: string;
    filePath: string;
    fileSize: number;
    fileType: string;
    isPublic: boolean;
    isDefault: boolean;
    tags: string[];
    downloadCount: number;
    viewCount: number;
    lastUsedAt?: NativeDate | null;
    content?: {
        experience: import("mongoose").Types.DocumentArray<{
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }> & {
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }>;
        education: import("mongoose").Types.DocumentArray<{
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }> & {
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }>;
        skills: string[];
        certifications: import("mongoose").Types.DocumentArray<{
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }> & {
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }>;
        summary?: string | null;
        personalInfo?: {
            firstName?: string | null;
            lastName?: string | null;
            email?: string | null;
            phone?: string | null;
            address?: string | null;
            linkedin?: string | null;
            github?: string | null;
            website?: string | null;
        } | null;
    } | null;
    analysis?: {
        recommendedImprovements: string[];
        overallScore?: number | null;
        atsScore?: number | null;
        keywordMatch?: number | null;
        skillsMatch?: number | null;
        experienceMatch?: number | null;
        educationMatch?: number | null;
        lastAnalyzed?: NativeDate | null;
    } | null;
}, {}, {
    timestamps: true;
    versionKey: false;
    toJSON: {
        transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown>;
    };
}> & {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    userId: string;
    title: string;
    fileName: string;
    filePath: string;
    fileSize: number;
    fileType: string;
    isPublic: boolean;
    isDefault: boolean;
    tags: string[];
    downloadCount: number;
    viewCount: number;
    lastUsedAt?: NativeDate | null;
    content?: {
        experience: import("mongoose").Types.DocumentArray<{
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }> & {
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }>;
        education: import("mongoose").Types.DocumentArray<{
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }> & {
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }>;
        skills: string[];
        certifications: import("mongoose").Types.DocumentArray<{
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }> & {
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }>;
        summary?: string | null;
        personalInfo?: {
            firstName?: string | null;
            lastName?: string | null;
            email?: string | null;
            phone?: string | null;
            address?: string | null;
            linkedin?: string | null;
            github?: string | null;
            website?: string | null;
        } | null;
    } | null;
    analysis?: {
        recommendedImprovements: string[];
        overallScore?: number | null;
        atsScore?: number | null;
        keywordMatch?: number | null;
        skillsMatch?: number | null;
        experienceMatch?: number | null;
        educationMatch?: number | null;
        lastAnalyzed?: NativeDate | null;
    } | null;
} & {
    _id: import("mongoose").Types.ObjectId;
}, Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    versionKey: false;
    toJSON: {
        transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown>;
    };
}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    userId: string;
    title: string;
    fileName: string;
    filePath: string;
    fileSize: number;
    fileType: string;
    isPublic: boolean;
    isDefault: boolean;
    tags: string[];
    downloadCount: number;
    viewCount: number;
    lastUsedAt?: NativeDate | null;
    content?: {
        experience: import("mongoose").Types.DocumentArray<{
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }> & {
            achievements: string[];
            company?: string | null;
            position?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            current?: boolean | null;
            description?: string | null;
        }>;
        education: import("mongoose").Types.DocumentArray<{
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }> & {
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            institution?: string | null;
            degree?: string | null;
            gpa?: number | null;
        }>;
        skills: string[];
        certifications: import("mongoose").Types.DocumentArray<{
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }> & {
            date?: NativeDate | null;
            name?: string | null;
            issuer?: string | null;
            expiryDate?: NativeDate | null;
        }>;
        summary?: string | null;
        personalInfo?: {
            firstName?: string | null;
            lastName?: string | null;
            email?: string | null;
            phone?: string | null;
            address?: string | null;
            linkedin?: string | null;
            github?: string | null;
            website?: string | null;
        } | null;
    } | null;
    analysis?: {
        recommendedImprovements: string[];
        overallScore?: number | null;
        atsScore?: number | null;
        keywordMatch?: number | null;
        skillsMatch?: number | null;
        experienceMatch?: number | null;
        educationMatch?: number | null;
        lastAnalyzed?: NativeDate | null;
    } | null;
}, unknown>>;
//# sourceMappingURL=resume.model.d.ts.map