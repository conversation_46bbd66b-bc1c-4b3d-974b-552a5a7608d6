import { Schema } from 'mongoose';
export declare const User: import("mongoose").Model<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    email: string;
    firstName: string;
    lastName: string;
    role: "admin" | "user" | "premium" | "enterprise";
    subscriptionTier: "premium" | "enterprise" | "free" | "basic";
    isVerified: boolean;
    isActive: boolean;
    avatar?: string | null;
    lastLoginAt?: NativeDate | null;
    profile?: {
        languages: import("mongoose").Types.DocumentArray<{
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }> & {
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }>;
        skills: import("mongoose").Types.DocumentArray<{
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }> & {
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }>;
        education: import("mongoose").Types.DocumentArray<{
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }> & {
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }>;
        experience: import("mongoose").Types.DocumentArray<{
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }> & {
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }>;
        profileVisibility: "public" | "private" | "connections";
        searchable: boolean;
        bio?: string | null;
        phoneNumber?: string | null;
        website?: string | null;
        linkedin?: string | null;
        github?: string | null;
        portfolio?: string | null;
        currentPosition?: string | null;
        currentCompany?: string | null;
        yearsOfExperience?: number | null;
        dateOfBirth?: NativeDate | null;
        nationality?: string | null;
        location?: {
            country?: string | null;
            state?: string | null;
            city?: string | null;
            zipCode?: string | null;
            remote?: boolean | null;
            coordinates?: {
                lat?: number | null;
                lng?: number | null;
            } | null;
        } | null;
        expectedSalary?: {
            currency: string;
            min?: number | null;
            max?: number | null;
        } | null;
    } | null;
    preferences?: {
        notifications?: {
            push: boolean;
            email: boolean;
            sms: boolean;
        } | null;
        privacy?: {
            profileVisibility: "public" | "private" | "connections";
            showEmail: boolean;
            showPhone: boolean;
        } | null;
        jobAlerts?: {
            frequency: "daily" | "weekly" | "monthly";
            keywords: string[];
            locations: string[];
            salaryRange?: {
                min?: number | null;
                max?: number | null;
            } | null;
        } | null;
    } | null;
    analytics?: {
        profileViews: number;
        searchAppearances: number;
        applicationsSent: number;
        interviewsScheduled: number;
        offersReceived: number;
        loginStreak: number;
        totalLogins: number;
        averageSessionDuration: number;
        featuresUsed: string[];
        premiumFeaturesUsed: string[];
        responseRate: number;
        interviewRate: number;
        offerRate: number;
        lastActiveAt?: NativeDate | null;
    } | null;
}, {}, {}, {}, import("mongoose").Document<unknown, {}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    email: string;
    firstName: string;
    lastName: string;
    role: "admin" | "user" | "premium" | "enterprise";
    subscriptionTier: "premium" | "enterprise" | "free" | "basic";
    isVerified: boolean;
    isActive: boolean;
    avatar?: string | null;
    lastLoginAt?: NativeDate | null;
    profile?: {
        languages: import("mongoose").Types.DocumentArray<{
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }> & {
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }>;
        skills: import("mongoose").Types.DocumentArray<{
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }> & {
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }>;
        education: import("mongoose").Types.DocumentArray<{
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }> & {
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }>;
        experience: import("mongoose").Types.DocumentArray<{
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }> & {
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }>;
        profileVisibility: "public" | "private" | "connections";
        searchable: boolean;
        bio?: string | null;
        phoneNumber?: string | null;
        website?: string | null;
        linkedin?: string | null;
        github?: string | null;
        portfolio?: string | null;
        currentPosition?: string | null;
        currentCompany?: string | null;
        yearsOfExperience?: number | null;
        dateOfBirth?: NativeDate | null;
        nationality?: string | null;
        location?: {
            country?: string | null;
            state?: string | null;
            city?: string | null;
            zipCode?: string | null;
            remote?: boolean | null;
            coordinates?: {
                lat?: number | null;
                lng?: number | null;
            } | null;
        } | null;
        expectedSalary?: {
            currency: string;
            min?: number | null;
            max?: number | null;
        } | null;
    } | null;
    preferences?: {
        notifications?: {
            push: boolean;
            email: boolean;
            sms: boolean;
        } | null;
        privacy?: {
            profileVisibility: "public" | "private" | "connections";
            showEmail: boolean;
            showPhone: boolean;
        } | null;
        jobAlerts?: {
            frequency: "daily" | "weekly" | "monthly";
            keywords: string[];
            locations: string[];
            salaryRange?: {
                min?: number | null;
                max?: number | null;
            } | null;
        } | null;
    } | null;
    analytics?: {
        profileViews: number;
        searchAppearances: number;
        applicationsSent: number;
        interviewsScheduled: number;
        offersReceived: number;
        loginStreak: number;
        totalLogins: number;
        averageSessionDuration: number;
        featuresUsed: string[];
        premiumFeaturesUsed: string[];
        responseRate: number;
        interviewRate: number;
        offerRate: number;
        lastActiveAt?: NativeDate | null;
    } | null;
}, {}, {
    timestamps: true;
    versionKey: false;
    toJSON: {
        transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown>;
    };
}> & {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    email: string;
    firstName: string;
    lastName: string;
    role: "admin" | "user" | "premium" | "enterprise";
    subscriptionTier: "premium" | "enterprise" | "free" | "basic";
    isVerified: boolean;
    isActive: boolean;
    avatar?: string | null;
    lastLoginAt?: NativeDate | null;
    profile?: {
        languages: import("mongoose").Types.DocumentArray<{
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }> & {
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }>;
        skills: import("mongoose").Types.DocumentArray<{
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }> & {
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }>;
        education: import("mongoose").Types.DocumentArray<{
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }> & {
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }>;
        experience: import("mongoose").Types.DocumentArray<{
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }> & {
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }>;
        profileVisibility: "public" | "private" | "connections";
        searchable: boolean;
        bio?: string | null;
        phoneNumber?: string | null;
        website?: string | null;
        linkedin?: string | null;
        github?: string | null;
        portfolio?: string | null;
        currentPosition?: string | null;
        currentCompany?: string | null;
        yearsOfExperience?: number | null;
        dateOfBirth?: NativeDate | null;
        nationality?: string | null;
        location?: {
            country?: string | null;
            state?: string | null;
            city?: string | null;
            zipCode?: string | null;
            remote?: boolean | null;
            coordinates?: {
                lat?: number | null;
                lng?: number | null;
            } | null;
        } | null;
        expectedSalary?: {
            currency: string;
            min?: number | null;
            max?: number | null;
        } | null;
    } | null;
    preferences?: {
        notifications?: {
            push: boolean;
            email: boolean;
            sms: boolean;
        } | null;
        privacy?: {
            profileVisibility: "public" | "private" | "connections";
            showEmail: boolean;
            showPhone: boolean;
        } | null;
        jobAlerts?: {
            frequency: "daily" | "weekly" | "monthly";
            keywords: string[];
            locations: string[];
            salaryRange?: {
                min?: number | null;
                max?: number | null;
            } | null;
        } | null;
    } | null;
    analytics?: {
        profileViews: number;
        searchAppearances: number;
        applicationsSent: number;
        interviewsScheduled: number;
        offersReceived: number;
        loginStreak: number;
        totalLogins: number;
        averageSessionDuration: number;
        featuresUsed: string[];
        premiumFeaturesUsed: string[];
        responseRate: number;
        interviewRate: number;
        offerRate: number;
        lastActiveAt?: NativeDate | null;
    } | null;
} & {
    _id: import("mongoose").Types.ObjectId;
}, Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    versionKey: false;
    toJSON: {
        transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown>;
    };
}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    email: string;
    firstName: string;
    lastName: string;
    role: "admin" | "user" | "premium" | "enterprise";
    subscriptionTier: "premium" | "enterprise" | "free" | "basic";
    isVerified: boolean;
    isActive: boolean;
    avatar?: string | null;
    lastLoginAt?: NativeDate | null;
    profile?: {
        languages: import("mongoose").Types.DocumentArray<{
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }> & {
            language?: string | null;
            proficiency?: "basic" | "conversational" | "fluent" | "native" | null;
        }>;
        skills: import("mongoose").Types.DocumentArray<{
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }> & {
            level?: "beginner" | "intermediate" | "advanced" | "expert" | null;
            name?: string | null;
            yearsOfExperience?: number | null;
            verified?: boolean | null;
        }>;
        education: import("mongoose").Types.DocumentArray<{
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }> & {
            institution?: string | null;
            degree?: string | null;
            field?: string | null;
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            gpa?: number | null;
            description?: string | null;
        }>;
        experience: import("mongoose").Types.DocumentArray<{
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }, import("mongoose").Types.Subdocument<import("bson").ObjectId, any, {
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }> & {
            skills: string[];
            achievements: string[];
            startDate?: NativeDate | null;
            endDate?: NativeDate | null;
            description?: string | null;
            company?: string | null;
            position?: string | null;
            current?: boolean | null;
        }>;
        profileVisibility: "public" | "private" | "connections";
        searchable: boolean;
        bio?: string | null;
        phoneNumber?: string | null;
        website?: string | null;
        linkedin?: string | null;
        github?: string | null;
        portfolio?: string | null;
        currentPosition?: string | null;
        currentCompany?: string | null;
        yearsOfExperience?: number | null;
        dateOfBirth?: NativeDate | null;
        nationality?: string | null;
        location?: {
            country?: string | null;
            state?: string | null;
            city?: string | null;
            zipCode?: string | null;
            remote?: boolean | null;
            coordinates?: {
                lat?: number | null;
                lng?: number | null;
            } | null;
        } | null;
        expectedSalary?: {
            currency: string;
            min?: number | null;
            max?: number | null;
        } | null;
    } | null;
    preferences?: {
        notifications?: {
            push: boolean;
            email: boolean;
            sms: boolean;
        } | null;
        privacy?: {
            profileVisibility: "public" | "private" | "connections";
            showEmail: boolean;
            showPhone: boolean;
        } | null;
        jobAlerts?: {
            frequency: "daily" | "weekly" | "monthly";
            keywords: string[];
            locations: string[];
            salaryRange?: {
                min?: number | null;
                max?: number | null;
            } | null;
        } | null;
    } | null;
    analytics?: {
        profileViews: number;
        searchAppearances: number;
        applicationsSent: number;
        interviewsScheduled: number;
        offersReceived: number;
        loginStreak: number;
        totalLogins: number;
        averageSessionDuration: number;
        featuresUsed: string[];
        premiumFeaturesUsed: string[];
        responseRate: number;
        interviewRate: number;
        offerRate: number;
        lastActiveAt?: NativeDate | null;
    } | null;
}, unknown>>;
//# sourceMappingURL=user.model.d.ts.map