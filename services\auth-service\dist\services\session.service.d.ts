import { Types } from 'mongoose';
import { ISession } from '../models/session.model';
export declare class SessionService {
    /**
     * Create a new session
     */
    createSession(sessionData: {
        userId: Types.ObjectId;
        sessionId: string;
        deviceInfo: Record<string, unknown>;
        expiresAt: Date;
    }): Promise<ISession>;
    /**
     * Find session by session ID
     */
    findBySessionId(sessionId: string): Promise<ISession | null>;
    /**
     * Find all active sessions for a user
     */
    findActiveUserSessions(userId: Types.ObjectId): Promise<ISession[]>;
    /**
     * Deactivate a session
     */
    deactivateSession(sessionId: string): Promise<void>;
    /**
     * Deactivate all sessions for a user
     */
    deactivateAllUserSessions(userId: Types.ObjectId): Promise<void>;
    /**
     * Clean up expired sessions
     */
    cleanupExpiredSessions(): Promise<void>;
    /**
     * Get session statistics for a user
     */
    getUserSessionStats(userId: Types.ObjectId): Promise<{
        activeSessionsCount: number;
        totalSessionsCount: number;
    }>;
}
//# sourceMappingURL=session.service.d.ts.map