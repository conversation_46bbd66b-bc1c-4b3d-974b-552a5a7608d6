import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { toast } from 'react-hot-toast';
import { Loader2 } from 'lucide-react';

const OAuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { setUser } = useAuth();

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        // Check for error parameters
        const error = searchParams.get('error');
        const errorMessage = searchParams.get('message');

        if (error) {
          toast.error(errorMessage || 'Authentication failed');
          navigate('/login');
          return;
        }

        // Extract tokens and user data from URL parameters
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const userParam = searchParams.get('user');

        if (!accessToken || !refreshToken || !userParam) {
          toast.error('Invalid authentication response');
          navigate('/login');
          return;
        }

        // Parse user data
        const userData = JSON.parse(decodeURIComponent(userParam));

        // Store tokens in localStorage
        localStorage.setItem('accessToken', accessToken);
        localStorage.setItem('refreshToken', refreshToken);
        localStorage.setItem('user', JSON.stringify(userData));

        // Update auth context
        setUser(userData);

        toast.success('Login successful!');
        
        // Redirect to dashboard or intended page
        const from = localStorage.getItem('redirectAfterLogin') || '/dashboard';
        localStorage.removeItem('redirectAfterLogin');
        navigate(from, { replace: true });

      } catch (error) {
        console.error('OAuth callback error:', error);
        toast.error('Authentication failed');
        navigate('/login');
      }
    };

    handleOAuthCallback();
  }, [searchParams, navigate, setUser]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary-600" />
        <h2 className="text-lg font-medium text-gray-900 mb-2">
          Completing authentication...
        </h2>
        <p className="text-sm text-gray-600">
          Please wait while we log you in.
        </p>
      </div>
    </div>
  );
};

export default OAuthCallback;
