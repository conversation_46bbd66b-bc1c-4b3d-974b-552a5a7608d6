"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionModel = void 0;
const mongoose_1 = require("mongoose");
const sessionSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        required: true,
    },
    sessionId: {
        type: String,
        required: true,
    },
    deviceInfo: {
        userAgent: { type: String, default: '' },
        ip: { type: String, required: true },
        deviceType: { type: String, default: 'unknown' },
        browser: { type: String, default: 'unknown' },
        os: { type: String, default: 'unknown' },
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    expiresAt: {
        type: Date,
        required: true,
    },
}, {
    timestamps: true,
    versionKey: false,
});
// Indexes
sessionSchema.index({ userId: 1, isActive: 1 });
sessionSchema.index({ sessionId: 1, isActive: 1 });
sessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index for automatic cleanup
exports.SessionModel = (0, mongoose_1.model)('Session', sessionSchema);
//# sourceMappingURL=session.model.js.map