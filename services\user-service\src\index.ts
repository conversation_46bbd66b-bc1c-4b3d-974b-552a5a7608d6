import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { appConfig } from './config/environment';
import { database } from './database/connection';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/error.middleware';
import { userRoutes } from './routes/user.routes';
import { healthRoutes } from './routes/health.routes';

class UserService {
  private app: express.Application;
  private server: ReturnType<express.Application['listen']> | null = null;

  constructor() {
    this.app = express();
    // Enable trust proxy for proper IP detection behind reverse proxy
    this.app.set('trust proxy', true);
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    this.app.use(helmet());
    this.app.use(
      cors({
        origin: appConfig.isDevelopment
          ? true
          : (process.env.CORS_ORIGIN?.split(',') ?? '*'),
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      })
    );
    this.app.use(compression());
    this.app.use(express.json({ limit: '1mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '1mb' }));
    this.app.use(
      morgan('combined', {
        stream: { write: message => logger.info(message.trim()) },
      })
    );
    this.app.use((req, res, next) => {
      req.headers['x-request-id'] =
        req.headers['x-request-id'] ??
        Math.random().toString(36).substring(2, 15);
      res.setHeader('X-Request-ID', req.headers['x-request-id'] as string);
      next();
    });
  }

  private setupRoutes(): void {
    this.app.use('/health', healthRoutes);
    // Support both /users and /api/v1/users for API Gateway compatibility
    this.app.use('/users', userRoutes);
    this.app.use('/api/v1/users', userRoutes);
    this.app.get('/api/v1', (req, res) => {
      res.json({
        name: 'Job Platform User Service',
        version: appConfig.apiVersion,
        environment: appConfig.nodeEnv,
        timestamp: new Date().toISOString(),
        features: ['User Management', 'Profile Management', 'User Preferences'],
      });
    });
  }

  private setupErrorHandling(): void {
    this.app.use((req, res) => {
      res.status(404).json({
        success: false,
        message: `Route ${req.originalUrl} not found`,
        timestamp: new Date().toISOString(),
      });
    });
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      // Start server first to respond to health checks
      const port = appConfig.port;
      this.server = this.app.listen(port, '0.0.0.0', () => {
        logger.info(`👤 User Service running on port ${port}`);
        logger.info(`📝 Environment: ${appConfig.nodeEnv}`);
        logger.info(`🔗 Health Check: http://localhost:${port}/health`);
      });

      // Connect to database after server is running
      try {
        await database.connect();
        logger.info('Database connected successfully');
      } catch (dbError) {
        logger.warn('Database connection failed - service will run with limited functionality:', dbError);
        // Continue running without database - some features will be disabled
      }

      process.on('SIGTERM', () => void this.shutdown());
      process.on('SIGINT', () => void this.shutdown());
    } catch (error) {
      logger.error('Failed to start User Service:', error);
      process.exit(1);
    }
  }

  private async shutdown(): Promise<void> {
    logger.info('🔄 Graceful shutdown initiated...');
    if (this.server) {
      await new Promise<void>(resolve => {
        this.server!.close(() => {
          void database.disconnect().then(() => {
            logger.info('✅ HTTP server closed');
            logger.info('✅ Graceful shutdown completed');
            process.exit(0);
          });
        });
        resolve();
      });
    }
  }
}

const userService = new UserService();
userService.start().catch(error => {
  logger.error('Failed to start User Service:', error);
  process.exit(1);
});
