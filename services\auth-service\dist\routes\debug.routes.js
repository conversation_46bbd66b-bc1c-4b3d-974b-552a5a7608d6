"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.debugRoutes = void 0;
const express_1 = require("express");
const connection_1 = require("../database/connection");
const user_model_1 = require("../models/user.model");
const logger_1 = require("../utils/logger");
const response_1 = require("../utils/response");
const router = (0, express_1.Router)();
exports.debugRoutes = router;
// Database connection status
router.get('/db-status', async (req, res) => {
    try {
        const connectionState = connection_1.database.getConnectionState();
        const isHealthy = connection_1.database.isHealthy();
        // Try to perform a simple database operation
        let dbOperationResult = null;
        let dbError = null;
        try {
            const userCount = await user_model_1.User.countDocuments();
            dbOperationResult = { userCount };
        }
        catch (error) {
            dbError = error instanceof Error ? error.message : 'Unknown database error';
        }
        const response = response_1.ResponseUtil.success({
            connectionState,
            isHealthy,
            dbOperationResult,
            dbError,
            timestamp: new Date().toISOString(),
        }, 'Database status retrieved');
        res.status(response.statusCode).json(response);
    }
    catch (error) {
        logger_1.logger.error('Database status check failed:', error);
        const response = response_1.ResponseUtil.error('Failed to check database status', 500);
        res.status(response.statusCode).json(response);
    }
});
// Test user creation
router.post('/test-user-creation', async (req, res) => {
    try {
        const testEmail = `test-${Date.now()}@example.com`;
        logger_1.logger.info('Testing user creation with email:', testEmail);
        // Create a test user
        const testUser = new user_model_1.User({
            email: testEmail,
            firstName: 'Test',
            lastName: 'User',
            profile: {
                bio: '',
                skills: [],
                education: [],
                experience: [],
                languages: [{ language: 'English', proficiency: 'native' }],
                profileVisibility: 'private',
                searchable: false,
            },
            preferences: {
                emailNotifications: true,
                pushNotifications: false,
                smsNotifications: false,
                marketingEmails: false,
                jobAlerts: true,
                applicationUpdates: true,
                weeklyDigest: false,
                theme: 'light',
                language: 'en',
                timezone: 'UTC',
                dateFormat: 'MM/DD/YYYY',
                currency: 'USD',
            },
            analytics: {
                profileViews: 0,
                searchAppearances: 0,
                applicationsSent: 0,
                interviewsScheduled: 0,
                offersReceived: 0,
                loginStreak: 0,
                totalLogins: 0,
                averageSessionDuration: 0,
                lastActiveAt: new Date(),
                featuresUsed: [],
                premiumFeaturesUsed: [],
                responseRate: 0,
                interviewRate: 0,
                offerRate: 0,
            },
        });
        logger_1.logger.info('Attempting to save test user...');
        const savedUser = await testUser.save();
        logger_1.logger.info('Test user saved successfully:', { id: savedUser.id, email: savedUser.email });
        // Verify the user was saved by finding it
        const foundUser = await user_model_1.User.findById(savedUser.id);
        logger_1.logger.info('Test user found in database:', { found: !!foundUser });
        // Clean up - delete the test user
        await user_model_1.User.findByIdAndDelete(savedUser.id);
        logger_1.logger.info('Test user cleaned up');
        const response = response_1.ResponseUtil.success({
            testPassed: true,
            userId: savedUser.id,
            email: savedUser.email,
            foundInDb: !!foundUser,
            cleanedUp: true,
        }, 'User creation test passed');
        res.status(response.statusCode).json(response);
    }
    catch (error) {
        logger_1.logger.error('User creation test failed:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const response = response_1.ResponseUtil.error(`User creation test failed: ${errorMessage}`, 500);
        res.status(response.statusCode).json(response);
    }
});
// List recent users (for debugging)
router.get('/recent-users', async (req, res) => {
    try {
        const users = await user_model_1.User.find({})
            .sort({ createdAt: -1 })
            .limit(10)
            .select('email firstName lastName createdAt googleId');
        const response = response_1.ResponseUtil.success({
            users,
            count: users.length,
        }, 'Recent users retrieved');
        res.status(response.statusCode).json(response);
    }
    catch (error) {
        logger_1.logger.error('Failed to retrieve recent users:', error);
        const response = response_1.ResponseUtil.error('Failed to retrieve recent users', 500);
        res.status(response.statusCode).json(response);
    }
});
//# sourceMappingURL=debug.routes.js.map