// Environment configuration for the frontend service
export const config = {
  // API Configuration
  apiBaseUrl: getApiBaseUrl(),
  
  // App Configuration
  appName: getEnvVar('VITE_APP_NAME', 'Job Application Platform'),
  appVersion: getEnvVar('VITE_APP_VERSION', '1.0.0'),
  appEnv: getEnvVar('VITE_APP_ENV', 'development'),
  
  // Feature Flags
  enableAnalytics: getEnvVar('VITE_ENABLE_ANALYTICS', 'false') === 'true',
  enableDebug: getEnvVar('VITE_ENABLE_DEBUG', 'false') === 'true',
};

function getEnvVar(key: string, defaultValue: string): string {
  try {
    // Try to get from import.meta.env (Vite)
    const value = (import.meta as any).env?.[key];
    if (value && value !== 'undefined') {
      return value;
    }
  } catch (error) {
    // Fallback for environments where import.meta is not available
  }
  
  // Check window object for runtime environment variables
  if (typeof window !== 'undefined' && (window as any).ENV) {
    const value = (window as any).ENV[key];
    if (value && value !== 'undefined') {
      return value;
    }
  }
  
  return defaultValue;
}

function getApiBaseUrl(): string {
  // Try environment variable first
  const envApiUrl = getEnvVar('VITE_API_BASE_URL', '');
  if (envApiUrl && envApiUrl !== '') {
    console.log('Using VITE_API_BASE_URL:', envApiUrl);
    return envApiUrl;
  }
  
  // Production fallback - detect if we're on DigitalOcean
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    console.log('Current hostname:', hostname);
    
    if (hostname.includes('ondigitalocean.app')) {
      const url = `${window.location.origin}/api/v1`;
      console.log('Detected DigitalOcean environment, using:', url);
      return url;
    }
    
    // Force /api/v1 for any production environment
    if (hostname !== 'localhost' && !hostname.includes('127.0.0.1')) {
      const url = `${window.location.origin}/api/v1`;
      console.log('Detected production environment, using:', url);
      return url;
    }
  }
  
  // Default fallback - force /api/v1 for production
  console.log('Using default API base URL: /api/v1');
  return '/api/v1';
}
