"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityEventService = void 0;
const security_event_model_1 = require("../models/security-event.model");
const logger_1 = require("../utils/logger");
class SecurityEventService {
    /**
     * Log a security event
     */
    async logEvent(eventData) {
        try {
            const securityEvent = new security_event_model_1.SecurityEvent({
                ...eventData,
                riskLevel: this.calculateRiskLevel(eventData.eventType),
                location: await this.getLocationFromIP(),
            });
            await securityEvent.save();
            logger_1.logger.info(`Security event logged: ${eventData.eventType} for user ${String(eventData.userId)}`);
        }
        catch (error) {
            logger_1.logger.error('Failed to log security event:', error);
            // Don't throw error to avoid breaking the main flow
        }
    }
    /**
     * Calculate risk level based on event type
     */
    calculateRiskLevel(eventType) {
        const highRiskEvents = [
            'account_locked',
            'suspicious_activity',
            'multiple_failed_logins',
        ];
        const mediumRiskEvents = [
            'failed_login',
            'password_change',
            '2fa_disabled',
        ];
        if (highRiskEvents.includes(eventType)) {
            return 'high';
        }
        else if (mediumRiskEvents.includes(eventType)) {
            return 'medium';
        }
        return 'low';
    }
    /**
     * Get location from IP address (mock implementation)
     */
    getLocationFromIP() {
        // Mock implementation - in production, use a geolocation service
        return Promise.resolve({
            country: 'US',
            region: 'CA',
            city: 'San Francisco',
        });
    }
    /**
     * Get security events for a user
     */
    async getUserSecurityEvents(userId, limit = 50) {
        return await security_event_model_1.SecurityEvent.find({ userId })
            .sort({ createdAt: -1 })
            .limit(limit)
            .lean();
    }
    /**
     * Get high-risk security events
     */
    async getHighRiskEvents(hours = 24) {
        const since = new Date(Date.now() - hours * 60 * 60 * 1000);
        return await security_event_model_1.SecurityEvent.find({
            riskLevel: 'high',
            createdAt: { $gte: since },
        })
            .sort({ createdAt: -1 })
            .lean();
    }
}
exports.SecurityEventService = SecurityEventService;
//# sourceMappingURL=security-event.service.js.map