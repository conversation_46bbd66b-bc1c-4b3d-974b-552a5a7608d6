export declare class BaseError extends Error {
    statusCode: number;
    constructor(message?: string, statusCode?: number);
}
export declare class AuthenticationError extends BaseError {
    constructor(message?: string);
}
export declare class NotFoundError extends BaseError {
    constructor(message?: string);
}
export declare class ConflictError extends BaseError {
    constructor(message?: string);
}
export declare class ValidationError extends BaseError {
    errors: string[];
    constructor(message?: string, errors?: string[]);
}
export declare class UnauthorizedError extends BaseError {
    constructor(message?: string);
}
export declare class ForbiddenError extends BaseError {
    constructor(message?: string);
}
//# sourceMappingURL=errors.d.ts.map