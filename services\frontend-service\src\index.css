@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

@layer components {
  /* Form components */
  .form-group {
    @apply space-y-1;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }

  .form-error {
    @apply text-sm text-error-600;
  }

  .form-help {
    @apply text-sm text-gray-500;
  }

  /* Card components */
  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply p-6;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Badge components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply bg-secondary-100 text-secondary-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-error {
    @apply bg-error-100 text-error-800;
  }

  /* Status badges */
  .status-active {
    @apply badge badge-success;
  }

  .status-pending {
    @apply badge badge-warning;
  }

  .status-inactive {
    @apply badge badge-secondary;
  }

  .status-error {
    @apply badge badge-error;
  }

  /* Application status badges */
  .status-submitted {
    @apply badge badge-primary;
  }

  .status-reviewed {
    @apply badge badge-secondary;
  }

  .status-interview {
    @apply badge badge-warning;
  }

  .status-accepted {
    @apply badge badge-success;
  }

  .status-rejected {
    @apply badge badge-error;
  }

  .status-withdrawn {
    @apply badge bg-gray-100 text-gray-800;
  }
}

@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Focus utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Hover utilities */
  .hover-lift {
    @apply transition-transform hover:-translate-y-1 hover:shadow-lg;
  }

  /* Loading states */
  .loading-skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  /* Truncate utilities */
  .truncate-2-lines {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .truncate-3-lines {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
