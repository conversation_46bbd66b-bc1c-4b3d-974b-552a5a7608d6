"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const morgan_1 = __importDefault(require("morgan"));
const node_cron_1 = __importDefault(require("node-cron"));
const environment_1 = require("./config/environment");
const connection_1 = require("./database/connection");
const logger_1 = require("./utils/logger");
const error_middleware_1 = require("./middleware/error.middleware");
const job_routes_1 = require("./routes/job.routes");
const application_routes_1 = require("./routes/application.routes");
const health_routes_1 = require("./routes/health.routes");
const job_cleanup_service_1 = require("./services/job-cleanup.service");
class JobService {
    app;
    server = null;
    jobCleanupService;
    constructor() {
        this.app = (0, express_1.default)();
        // Enable trust proxy for proper IP detection behind reverse proxy
        this.app.set('trust proxy', true);
        this.jobCleanupService = new job_cleanup_service_1.JobCleanupService();
        this.setupMiddleware();
        this.setupRoutes();
        this.setupCronJobs();
        this.setupErrorHandling();
    }
    setupMiddleware() {
        this.app.use((0, helmet_1.default)());
        this.app.use((0, cors_1.default)({ origin: true, credentials: true }));
        this.app.use((0, compression_1.default)());
        this.app.use(express_1.default.json({ limit: '5mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '5mb' }));
        this.app.use((0, morgan_1.default)('combined', {
            stream: { write: message => logger_1.logger.info(message.trim()) },
        }));
        this.app.use((req, res, next) => {
            req.headers['x-request-id'] =
                req.headers['x-request-id'] ??
                    Math.random().toString(36).substring(2, 15);
            res.setHeader('X-Request-ID', req.headers['x-request-id']);
            next();
        });
    }
    setupRoutes() {
        this.app.use('/health', health_routes_1.healthRoutes);
        // Support both /jobs and /api/v1/jobs for API Gateway compatibility
        this.app.use('/jobs', job_routes_1.jobRoutes);
        this.app.use('/api/v1/jobs', job_routes_1.jobRoutes);
        this.app.use('/applications', application_routes_1.applicationRoutes);
        this.app.use('/api/v1/applications', application_routes_1.applicationRoutes);
        this.app.get('/api/v1', (req, res) => {
            res.json({
                name: 'Job Platform Job Service',
                version: environment_1.appConfig.apiVersion,
                environment: environment_1.appConfig.nodeEnv,
                timestamp: new Date().toISOString(),
                features: [
                    'Job Management',
                    'Application Tracking',
                    'Job Recommendations',
                ],
            });
        });
    }
    setupCronJobs() {
        // Clean up expired jobs every hour
        node_cron_1.default.schedule('0 * * * *', async () => {
            try {
                await this.jobCleanupService.cleanupExpiredJobs();
            }
            catch (error) {
                logger_1.logger.error('Job cleanup failed:', error);
            }
        });
    }
    setupErrorHandling() {
        this.app.use((req, res) => {
            res.status(404).json({
                success: false,
                message: `Route ${req.originalUrl} not found`,
            });
        });
        this.app.use(error_middleware_1.errorHandler);
    }
    async start() {
        try {
            // Start server first to respond to health checks
            const port = environment_1.appConfig.port;
            this.server = this.app.listen(port, '0.0.0.0', () => {
                logger_1.logger.info(`💼 Job Service running on port ${port}`);
                logger_1.logger.info(`📝 Environment: ${environment_1.appConfig.nodeEnv}`);
                logger_1.logger.info(`🔗 Health Check: http://localhost:${port}/health`);
            });
            // Connect to database after server is running
            try {
                await connection_1.database.connect();
                logger_1.logger.info('Database connected successfully');
            }
            catch (dbError) {
                logger_1.logger.warn('Database connection failed - service will run with limited functionality:', dbError);
                // Continue running without database - some features will be disabled
            }
            process.on('SIGTERM', () => void this.shutdown());
            process.on('SIGINT', () => void this.shutdown());
        }
        catch (error) {
            logger_1.logger.error('Failed to start Job Service:', error);
            process.exit(1);
        }
    }
    async shutdown() {
        logger_1.logger.info('🔄 Job Service shutting down...');
        if (this.server) {
            await new Promise(resolve => {
                this.server.close(() => {
                    void connection_1.database.disconnect().then(() => {
                        logger_1.logger.info('✅ Job Service shutdown completed');
                        process.exit(0);
                    });
                });
                resolve();
            });
        }
    }
}
const jobService = new JobService();
jobService.start().catch(error => {
    logger_1.logger.error('Failed to start Job Service:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map