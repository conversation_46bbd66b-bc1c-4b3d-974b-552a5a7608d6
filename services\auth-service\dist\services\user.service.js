"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const user_model_1 = require("../models/user.model");
const errors_1 = require("../utils/errors");
class UserService {
    /**
     * Create a new user
     */
    async create(userData) {
        try {
            const user = new user_model_1.User({
                ...userData,
                profile: {
                    bio: '',
                    skills: [],
                    education: [],
                    experience: [],
                    languages: [{ language: 'English', proficiency: 'native' }],
                    profileVisibility: 'private',
                    searchable: false,
                },
                preferences: {
                    notifications: {
                        email: {
                            jobAlerts: true,
                            applicationUpdates: true,
                            marketingEmails: false,
                            weeklyDigest: true,
                        },
                        push: {
                            jobAlerts: true,
                            applicationUpdates: true,
                            messages: true,
                        },
                        sms: {
                            criticalUpdates: true,
                            jobAlerts: false,
                        },
                    },
                    jobSearch: {
                        preferredJobTypes: [],
                        preferredLocations: [],
                        salaryRange: { min: 0, max: 0, currency: 'USD' },
                        remoteWork: false,
                        willingToRelocate: false,
                    },
                    privacy: {
                        showProfile: false,
                        showSalaryExpectations: false,
                        allowRecruiterContact: false,
                        showApplicationHistory: false,
                    },
                    interface: {
                        theme: 'light',
                        language: 'en',
                        timezone: 'UTC',
                        dateFormat: 'MM/DD/YYYY',
                    },
                },
                analytics: {
                    profileViews: 0,
                    searchAppearances: 0,
                    applicationsSent: 0,
                    interviewsScheduled: 0,
                    offersReceived: 0,
                    loginStreak: 0,
                    totalLogins: 0,
                    averageSessionDuration: 0,
                    lastActiveAt: new Date(),
                    featuresUsed: [],
                    premiumFeaturesUsed: [],
                    responseRate: 0,
                    interviewRate: 0,
                    offerRate: 0,
                },
            });
            return await user.save();
        }
        catch (error) {
            if (error &&
                typeof error === 'object' &&
                'code' in error &&
                error.code === 11000) {
                throw new errors_1.ConflictError('User already exists with this email');
            }
            throw error;
        }
    }
    /**
     * Find user by ID
     */
    async findById(id) {
        return await user_model_1.User.findById(id);
    }
    /**
     * Find user by email
     */
    async findByEmail(email) {
        return await user_model_1.User.findOne({ email: email.toLowerCase() });
    }
    /**
     * Find user by Google ID
     */
    async findByGoogleId(googleId) {
        return await user_model_1.User.findOne({ googleId });
    }
    /**
     * Find all users with filters
     */
    async findAll(filters = {}) {
        return await user_model_1.User.find(filters);
    }
    /**
     * Update user
     */
    async update(id, updateData) {
        const user = await user_model_1.User.findByIdAndUpdate(id, { ...updateData, updatedAt: new Date() }, { new: true, runValidators: true });
        if (!user) {
            throw new errors_1.NotFoundError('User not found');
        }
        return user;
    }
    /**
     * Update user password
     */
    async updatePassword(id, hashedPassword) {
        const result = await user_model_1.User.updateOne({ _id: id }, {
            password: hashedPassword,
            passwordChangedAt: new Date(),
            updatedAt: new Date(),
        });
        if (result.matchedCount === 0) {
            throw new errors_1.NotFoundError('User not found');
        }
    }
    /**
     * Update last login timestamp
     */
    async updateLastLogin(id) {
        await user_model_1.User.updateOne({ _id: id }, {
            lastLoginAt: new Date(),
            $inc: { 'analytics.totalLogins': 1 },
            updatedAt: new Date(),
        });
    }
    /**
     * Verify user email
     */
    async verifyEmail(id) {
        const result = await user_model_1.User.updateOne({ _id: id }, {
            isVerified: true,
            emailVerificationToken: undefined,
            emailVerificationExpires: undefined,
            updatedAt: new Date(),
        });
        if (result.matchedCount === 0) {
            throw new errors_1.NotFoundError('User not found');
        }
    }
    /**
     * Link Google account to existing user
     */
    async linkGoogleAccount(id, googleId) {
        const result = await user_model_1.User.updateOne({ _id: id }, {
            googleId,
            updatedAt: new Date(),
        });
        if (result.matchedCount === 0) {
            throw new errors_1.NotFoundError('User not found');
        }
    }
    /**
     * Soft delete user
     */
    async softDelete(id) {
        const result = await user_model_1.User.updateOne({ _id: id }, {
            isDeleted: true,
            deletedAt: new Date(),
            isActive: false,
            updatedAt: new Date(),
        });
        if (result.matchedCount === 0) {
            throw new errors_1.NotFoundError('User not found');
        }
    }
    /**
     * Restore soft deleted user
     */
    async restore(id) {
        const result = await user_model_1.User.updateOne({ _id: id }, {
            isDeleted: false,
            deletedAt: undefined,
            isActive: true,
            updatedAt: new Date(),
        });
        if (result.matchedCount === 0) {
            throw new errors_1.NotFoundError('User not found');
        }
    }
    /**
     * Suspend user account
     */
    async suspend(id, reason) {
        const result = await user_model_1.User.updateOne({ _id: id }, {
            isSuspended: true,
            suspendedAt: new Date(),
            suspendedReason: reason,
            updatedAt: new Date(),
        });
        if (result.matchedCount === 0) {
            throw new errors_1.NotFoundError('User not found');
        }
    }
    /**
     * Unsuspend user account
     */
    async unsuspend(id) {
        const result = await user_model_1.User.updateOne({ _id: id }, {
            isSuspended: false,
            suspendedAt: undefined,
            suspendedReason: undefined,
            updatedAt: new Date(),
        });
        if (result.matchedCount === 0) {
            throw new errors_1.NotFoundError('User not found');
        }
    }
    /**
     * Update user analytics
     */
    async updateAnalytics(id, analyticsUpdate) {
        await user_model_1.User.updateOne({ _id: id }, {
            $set: {
                ...Object.keys(analyticsUpdate).reduce((acc, key) => {
                    acc[`analytics.${key}`] = analyticsUpdate[key];
                    return acc;
                }, {}),
                'analytics.lastActiveAt': new Date(),
                updatedAt: new Date(),
            },
        });
    }
    /**
     * Search users
     */
    async search(query) {
        const filters = {
            isDeleted: false,
            isActive: true,
        };
        // Search term (name, email, bio)
        if (query.searchTerm) {
            filters.$or = [
                { firstName: { $regex: query.searchTerm, $options: 'i' } },
                { lastName: { $regex: query.searchTerm, $options: 'i' } },
                { email: { $regex: query.searchTerm, $options: 'i' } },
                { 'profile.bio': { $regex: query.searchTerm, $options: 'i' } },
            ];
        }
        // Skills filter
        if (query.skills && query.skills.length > 0) {
            filters['profile.skills.name'] = { $in: query.skills };
        }
        // Location filter
        if (query.location) {
            if (query.location.country) {
                filters['profile.location.country'] = query.location.country;
            }
            if (query.location.city) {
                filters['profile.location.city'] = query.location.city;
            }
        }
        const page = query.page ?? 1;
        const limit = query.limit ?? 20;
        const skip = (page - 1) * limit;
        const [users, total] = await Promise.all([
            user_model_1.User.find(filters)
                .select('-password -emailVerificationToken')
                .skip(skip)
                .limit(limit)
                .sort({ createdAt: -1 }),
            user_model_1.User.countDocuments(filters),
        ]);
        return { users, total };
    }
}
exports.UserService = UserService;
//# sourceMappingURL=user.service.js.map