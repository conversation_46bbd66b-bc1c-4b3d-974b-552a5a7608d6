import React from 'react';
import { Camera, MapPin, Globe, Linkedin, Github, Mail, Phone } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

export const ProfilePage: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
          <p className="text-gray-600 mt-2">
            Manage your profile information and preferences
          </p>
        </div>

        {/* Profile header */}
        <div className="card mb-6">
          <div className="card-body">
            <div className="flex items-center space-x-6">
              <div className="relative">
                <img
                  className="w-24 h-24 rounded-full"
                  src={user?.avatar || `https://ui-avatars.com/api/?name=${user?.firstName}+${user?.lastName}&background=3B82F6&color=fff&size=96`}
                  alt={`${user?.firstName} ${user?.lastName}`}
                />
                <button className="absolute bottom-0 right-0 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center hover:bg-primary-700">
                  <Camera className="w-4 h-4" />
                </button>
              </div>
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-gray-900">
                  {user?.firstName} {user?.lastName}
                </h2>
                <p className="text-gray-600">{user?.email}</p>
                <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                  <span className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    San Francisco, CA
                  </span>
                  <span className="flex items-center gap-1">
                    <Mail className="w-4 h-4" />
                    Available for work
                  </span>
                </div>
              </div>
              <Button>Edit Profile</Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic information */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
              </div>
              <div className="card-body space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="form-label">First Name</label>
                    <Input value={user?.firstName || ''} readOnly />
                  </div>
                  <div>
                    <label className="form-label">Last Name</label>
                    <Input value={user?.lastName || ''} readOnly />
                  </div>
                </div>
                <div>
                  <label className="form-label">Email</label>
                  <Input value={user?.email || ''} readOnly />
                </div>
                <div>
                  <label className="form-label">Phone</label>
                  <Input placeholder="+****************" />
                </div>
                <div>
                  <label className="form-label">Bio</label>
                  <textarea
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    rows={4}
                    placeholder="Tell us about yourself..."
                  />
                </div>
              </div>
            </div>

            {/* Experience */}
            <div className="card">
              <div className="card-header">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Experience</h3>
                  <Button variant="outline" size="sm">Add Experience</Button>
                </div>
              </div>
              <div className="card-body">
                <div className="space-y-4">
                  <div className="border-l-2 border-primary-200 pl-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">Senior Software Engineer</h4>
                        <p className="text-gray-600">TechCorp Inc.</p>
                        <p className="text-sm text-gray-500">Jan 2022 - Present</p>
                      </div>
                      <Button variant="ghost" size="sm">Edit</Button>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                      Leading development of scalable web applications using React and Node.js.
                      Mentoring junior developers and participating in architectural decisions.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Education */}
            <div className="card">
              <div className="card-header">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Education</h3>
                  <Button variant="outline" size="sm">Add Education</Button>
                </div>
              </div>
              <div className="card-body">
                <div className="space-y-4">
                  <div className="border-l-2 border-primary-200 pl-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">Bachelor of Science in Computer Science</h4>
                        <p className="text-gray-600">University of California, Berkeley</p>
                        <p className="text-sm text-gray-500">2018 - 2022</p>
                      </div>
                      <Button variant="ghost" size="sm">Edit</Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact info */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-gray-900">Contact</h3>
              </div>
              <div className="card-body space-y-3">
                <div className="flex items-center gap-3">
                  <Phone className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">+****************</span>
                </div>
                <div className="flex items-center gap-3">
                  <Globe className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">johndoe.dev</span>
                </div>
                <div className="flex items-center gap-3">
                  <Linkedin className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">linkedin.com/in/johndoe</span>
                </div>
                <div className="flex items-center gap-3">
                  <Github className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600">github.com/johndoe</span>
                </div>
              </div>
            </div>

            {/* Skills */}
            <div className="card">
              <div className="card-header">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Skills</h3>
                  <Button variant="outline" size="sm">Add Skill</Button>
                </div>
              </div>
              <div className="card-body">
                <div className="flex flex-wrap gap-2">
                  <span className="badge badge-primary">React</span>
                  <span className="badge badge-primary">Node.js</span>
                  <span className="badge badge-primary">TypeScript</span>
                  <span className="badge badge-primary">Python</span>
                  <span className="badge badge-primary">AWS</span>
                  <span className="badge badge-primary">PostgreSQL</span>
                </div>
              </div>
            </div>

            {/* Profile completion */}
            <div className="card">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-gray-900">Profile Completion</h3>
              </div>
              <div className="card-body">
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span>Overall Progress</span>
                    <span className="font-semibold">75%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-primary-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                      <span className="text-gray-600">Basic info completed</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                      <span className="text-gray-600">Experience added</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-warning-500 rounded-full"></div>
                      <span className="text-gray-600">Add more skills</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                      <span className="text-gray-600">Upload profile photo</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
