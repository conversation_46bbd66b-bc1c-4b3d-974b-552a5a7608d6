{"version": 3, "file": "forms-BcM1ev41.js", "sources": ["../../../../node_modules/react-hook-form/dist/index.esm.mjs"], "sourcesContent": ["import React from 'react';\n\nvar isCheckBoxInput = (element) => element.type === 'checkbox';\n\nvar isDateObject = (value) => value instanceof Date;\n\nvar isNullOrUndefined = (value) => value == null;\n\nconst isObjectType = (value) => typeof value === 'object';\nvar isObject = (value) => !isNullOrUndefined(value) &&\n    !Array.isArray(value) &&\n    isObjectType(value) &&\n    !isDateObject(value);\n\nvar getEventValue = (event) => isObject(event) && event.target\n    ? isCheckBoxInput(event.target)\n        ? event.target.checked\n        : event.target.value\n    : event;\n\nvar getNodeParentName = (name) => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\n\nvar isPlainObject = (tempObject) => {\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return (isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf'));\n};\n\nvar isWeb = typeof window !== 'undefined' &&\n    typeof window.HTMLElement !== 'undefined' &&\n    typeof document !== 'undefined';\n\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    }\n    else if (!(isWeb && (data instanceof Blob || isFileListInstance)) &&\n        (isArray || isObject(data))) {\n        copy = isArray ? [] : Object.create(Object.getPrototypeOf(data));\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        }\n        else {\n            for (const key in data) {\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    }\n    else {\n        return data;\n    }\n    return copy;\n}\n\nvar isKey = (value) => /^\\w*$/.test(value);\n\nvar isUndefined = (val) => val === undefined;\n\nvar compact = (value) => Array.isArray(value) ? value.filter(Boolean) : [];\n\nvar stringToPath = (input) => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n\nvar get = (object, path, defaultValue) => {\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = (isKey(path) ? [path] : stringToPath(path)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object\n        ? isUndefined(object[path])\n            ? defaultValue\n            : object[path]\n        : result;\n};\n\nvar isBoolean = (value) => typeof value === 'boolean';\n\nvar set = (object, path, value) => {\n    let index = -1;\n    const tempPath = isKey(path) ? [path] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while (++index < length) {\n        const key = tempPath[index];\n        let newValue = value;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue =\n                isObject(objValue) || Array.isArray(objValue)\n                    ? objValue\n                    : !isNaN(+tempPath[index + 1])\n                        ? []\n                        : {};\n        }\n        if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\n\nconst EVENTS = {\n    BLUR: 'blur',\n    FOCUS_OUT: 'focusout',\n    CHANGE: 'change',\n};\nconst VALIDATION_MODE = {\n    onBlur: 'onBlur',\n    onChange: 'onChange',\n    onSubmit: 'onSubmit',\n    onTouched: 'onTouched',\n    all: 'all',\n};\nconst INPUT_VALIDATION_RULES = {\n    max: 'max',\n    min: 'min',\n    maxLength: 'maxLength',\n    minLength: 'minLength',\n    pattern: 'pattern',\n    required: 'required',\n    validate: 'validate',\n};\n\nconst HookFormContext = React.createContext(null);\nHookFormContext.displayName = 'HookFormContext';\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => React.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = (props) => {\n    const { children, ...data } = props;\n    return (React.createElement(HookFormContext.Provider, { value: data }, children));\n};\n\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n    const result = {\n        defaultValues: control._defaultValues,\n    };\n    for (const key in formState) {\n        Object.defineProperty(result, key, {\n            get: () => {\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            },\n        });\n    }\n    return result;\n};\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = React.useState(control._formState);\n    const _localProxyFormState = React.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    });\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n            !disabled &&\n                updateFormState({\n                    ...control._formState,\n                    ...formState,\n                });\n        },\n    }), [name, disabled, exact]);\n    React.useEffect(() => {\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [control]);\n    return React.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\n\nvar isString = (value) => typeof value === 'string';\n\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName) => (isGlobal && _names.watch.add(fieldName),\n            get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n\nvar isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);\n\nfunction deepEqual(object1, object2, _internal_visited = new WeakSet()) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n        return true;\n    }\n    _internal_visited.add(object1);\n    _internal_visited.add(object2);\n    for (const key of keys1) {\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== 'ref') {\n            const val2 = object2[key];\n            if ((isDateObject(val1) && isDateObject(val2)) ||\n                (isObject(val1) && isObject(val2)) ||\n                (Array.isArray(val1) && Array.isArray(val2))\n                ? !deepEqual(val1, val2, _internal_visited)\n                : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact, compute, } = props || {};\n    const _defaultValue = React.useRef(defaultValue);\n    const _compute = React.useRef(compute);\n    const _computeFormValues = React.useRef(undefined);\n    _compute.current = compute;\n    const defaultValueMemo = React.useMemo(() => control._getWatch(name, _defaultValue.current), [control, name]);\n    const [value, updateValue] = React.useState(_compute.current ? _compute.current(defaultValueMemo) : defaultValueMemo);\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: {\n            values: true,\n        },\n        exact,\n        callback: (formState) => {\n            if (!disabled) {\n                const formValues = generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current);\n                if (_compute.current) {\n                    const computedFormValues = _compute.current(formValues);\n                    if (!deepEqual(computedFormValues, _computeFormValues.current)) {\n                        updateValue(computedFormValues);\n                        _computeFormValues.current = computedFormValues;\n                    }\n                }\n                else {\n                    updateValue(formValues);\n                }\n            }\n        },\n    }), [control, disabled, name, exact]);\n    React.useEffect(() => control._removeUnmounted());\n    return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister, defaultValue, } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const defaultValueMemo = React.useMemo(() => get(control._formValues, name, get(control._defaultValues, name, defaultValue)), [control, name, defaultValue]);\n    const value = useWatch({\n        control,\n        name,\n        defaultValue: defaultValueMemo,\n        exact: true,\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true,\n    });\n    const _props = React.useRef(props);\n    const _previousNameRef = React.useRef(undefined);\n    const _registerProps = React.useRef(control.register(name, {\n        ...props.rules,\n        value,\n        ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }));\n    _props.current = props;\n    const fieldState = React.useMemo(() => Object.defineProperties({}, {\n        invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n        },\n    }), [formState, name]);\n    const onChange = React.useCallback((event) => _registerProps.current.onChange({\n        target: {\n            value: getEventValue(event),\n            name: name,\n        },\n        type: EVENTS.CHANGE,\n    }), [name]);\n    const onBlur = React.useCallback(() => _registerProps.current.onBlur({\n        target: {\n            value: get(control._formValues, name),\n            name: name,\n        },\n        type: EVENTS.BLUR,\n    }), [name, control._formValues]);\n    const ref = React.useCallback((elm) => {\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: () => elm.focus && elm.focus(),\n                select: () => elm.select && elm.select(),\n                setCustomValidity: (message) => elm.setCustomValidity(message),\n                reportValidity: () => elm.reportValidity(),\n            };\n        }\n    }, [control._fields, name]);\n    const field = React.useMemo(() => ({\n        name,\n        value,\n        ...(isBoolean(disabled) || formState.disabled\n            ? { disabled: formState.disabled || disabled }\n            : {}),\n        onChange,\n        onBlur,\n        ref,\n    }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n    React.useEffect(() => {\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        const previousName = _previousNameRef.current;\n        if (previousName && previousName !== name && !isArrayField) {\n            control.unregister(previousName);\n        }\n        control.register(name, {\n            ..._props.current.rules,\n            ...(isBoolean(_props.current.disabled)\n                ? { disabled: _props.current.disabled }\n                : {}),\n        });\n        const updateMounted = (name, value) => {\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value);\n            }\n        }\n        !isArrayField && control.register(name);\n        _previousNameRef.current = name;\n        return () => {\n            (isArrayField\n                ? _shouldUnregisterField && !control._state.action\n                : _shouldUnregisterField)\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, isArrayField, shouldUnregister]);\n    React.useEffect(() => {\n        control._setDisabledField({\n            disabled,\n            name,\n        });\n    }, [disabled, name, control]);\n    return React.useMemo(() => ({\n        field,\n        formState,\n        fieldState,\n    }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = (props) => props.render(useController(props));\n\nconst flatten = (obj) => {\n    const output = {};\n    for (const key of Object.keys(obj)) {\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)) {\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        }\n        else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\n\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = React.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event) => {\n        let hasError = false;\n        let type = '';\n        await control.handleSubmit(async (data) => {\n            const formData = new FormData();\n            let formDataJson = '';\n            try {\n                formDataJson = JSON.stringify(data);\n            }\n            catch (_a) { }\n            const flattenFormValues = flatten(control._formValues);\n            for (const key in flattenFormValues) {\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson,\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers['Content-Type'],\n                        encType,\n                    ].some((value) => value && value.includes('json'));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...(encType && encType !== 'multipart/form-data'\n                                ? { 'Content-Type': encType }\n                                : {}),\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData,\n                    });\n                    if (response &&\n                        (validateStatus\n                            ? !validateStatus(response.status)\n                            : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({ response });\n                        type = String(response.status);\n                    }\n                    else {\n                        onSuccess && onSuccess({ response });\n                    }\n                }\n                catch (error) {\n                    hasError = true;\n                    onError && onError({ error });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false,\n            });\n            props.control.setError('root.server', {\n                type,\n            });\n        }\n    };\n    React.useEffect(() => {\n        setMounted(true);\n    }, []);\n    return render ? (React.createElement(React.Fragment, null, render({\n        submit,\n    }))) : (React.createElement(\"form\", { noValidate: mounted, action: action, method: method, encType: encType, onSubmit: submit, ...rest }, children));\n}\n\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n            ...(errors[name] && errors[name].types ? errors[name].types : {}),\n            [type]: message || true,\n        },\n    }\n    : {};\n\nvar convertToArrayPayload = (value) => (Array.isArray(value) ? value : [value]);\n\nvar createSubject = () => {\n    let _observers = [];\n    const next = (value) => {\n        for (const observer of _observers) {\n            observer.next && observer.next(value);\n        }\n    };\n    const subscribe = (observer) => {\n        _observers.push(observer);\n        return {\n            unsubscribe: () => {\n                _observers = _observers.filter((o) => o !== observer);\n            },\n        };\n    };\n    const unsubscribe = () => {\n        _observers = [];\n    };\n    return {\n        get observers() {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe,\n    };\n};\n\nfunction extractFormValues(fieldsState, formValues) {\n    const values = {};\n    for (const key in fieldsState) {\n        if (fieldsState.hasOwnProperty(key)) {\n            const fieldState = fieldsState[key];\n            const fieldValue = formValues[key];\n            if (fieldState && isObject(fieldState) && fieldValue) {\n                const nestedFieldsState = extractFormValues(fieldState, fieldValue);\n                if (isObject(nestedFieldsState)) {\n                    values[key] = nestedFieldsState;\n                }\n            }\n            else if (fieldsState[key]) {\n                values[key] = fieldValue;\n            }\n        }\n    }\n    return values;\n}\n\nvar isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;\n\nvar isFileInput = (element) => element.type === 'file';\n\nvar isFunction = (value) => typeof value === 'function';\n\nvar isHTMLElement = (value) => {\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value ? value.ownerDocument : 0;\n    return (value instanceof\n        (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement));\n};\n\nvar isMultipleSelect = (element) => element.type === `select-multiple`;\n\nvar isRadioInput = (element) => element.type === 'radio';\n\nvar isRadioOrCheckbox = (ref) => isRadioInput(ref) || isCheckBoxInput(ref);\n\nvar live = (ref) => isHTMLElement(ref) && ref.isConnected;\n\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while (index < length) {\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path)\n        ? path\n        : isKey(path)\n            ? [path]\n            : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 &&\n        ((isObject(childObject) && isEmptyObject(childObject)) ||\n            (Array.isArray(childObject) && isEmptyArray(childObject)))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\n\nvar objectHasFunction = (data) => {\n    for (const key in data) {\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\n\nfunction isTraversable(value) {\n    return Array.isArray(value) || (isObject(value) && !objectHasFunction(value));\n}\nfunction markFieldsDirty(data, fields = {}) {\n    for (const key in data) {\n        if (isTraversable(data[key])) {\n            fields[key] = Array.isArray(data[key]) ? [] : {};\n            markFieldsDirty(data[key], fields[key]);\n        }\n        else if (!isNullOrUndefined(data[key])) {\n            fields[key] = true;\n        }\n    }\n    return fields;\n}\nfunction getDirtyFields(data, formValues, dirtyFieldsFromValues) {\n    if (!dirtyFieldsFromValues) {\n        dirtyFieldsFromValues = markFieldsDirty(formValues);\n    }\n    for (const key in data) {\n        if (isTraversable(data[key])) {\n            if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                dirtyFieldsFromValues[key] = markFieldsDirty(data[key], Array.isArray(data[key]) ? [] : {});\n            }\n            else {\n                getDirtyFields(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n            }\n        }\n        else {\n            dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n        }\n    }\n    return dirtyFieldsFromValues;\n}\n\nconst defaultResult = {\n    value: false,\n    isValid: false,\n};\nconst validResult = { value: true, isValid: true };\nvar getCheckboxValue = (options) => {\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options\n                .filter((option) => option && option.checked && !option.disabled)\n                .map((option) => option.value);\n            return { value: values, isValid: !!values.length };\n        }\n        return options[0].checked && !options[0].disabled\n            ? // @ts-expect-error expected to work in the browser\n                options[0].attributes && !isUndefined(options[0].attributes.value)\n                    ? isUndefined(options[0].value) || options[0].value === ''\n                        ? validResult\n                        : { value: options[0].value, isValid: true }\n                    : validResult\n            : defaultResult;\n    }\n    return defaultResult;\n};\n\nvar getFieldValueAs = (value, { valueAsNumber, valueAsDate, setValueAs }) => isUndefined(value)\n    ? value\n    : valueAsNumber\n        ? value === ''\n            ? NaN\n            : value\n                ? +value\n                : value\n        : valueAsDate && isString(value)\n            ? new Date(value)\n            : setValueAs\n                ? setValueAs(value)\n                : value;\n\nconst defaultReturn = {\n    isValid: false,\n    value: null,\n};\nvar getRadioValue = (options) => Array.isArray(options)\n    ? options.reduce((previous, option) => option && option.checked && !option.disabled\n        ? {\n            isValid: true,\n            value: option.value,\n        }\n        : previous, defaultReturn)\n    : defaultReturn;\n\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [...ref.selectedOptions].map(({ value }) => value);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n    const fields = {};\n    for (const name of fieldsNames) {\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [...fieldsNames],\n        fields,\n        shouldUseNativeValidation,\n    };\n};\n\nvar isRegex = (value) => value instanceof RegExp;\n\nvar getRuleValue = (rule) => isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n        ? rule.source\n        : isObject(rule)\n            ? isRegex(rule.value)\n                ? rule.value.source\n                : rule.value\n            : rule;\n\nvar getValidationModes = (mode) => ({\n    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n    isOnBlur: mode === VALIDATION_MODE.onBlur,\n    isOnChange: mode === VALIDATION_MODE.onChange,\n    isOnAll: mode === VALIDATION_MODE.all,\n    isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = (fieldReference) => !!fieldReference &&\n    !!fieldReference.validate &&\n    !!((isFunction(fieldReference.validate) &&\n        fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n        (isObject(fieldReference.validate) &&\n            Object.values(fieldReference.validate).find((validateFunction) => validateFunction.constructor.name === ASYNC_FUNCTION)));\n\nvar hasValidation = (options) => options.mount &&\n    (options.required ||\n        options.min ||\n        options.max ||\n        options.maxLength ||\n        options.minLength ||\n        options.pattern ||\n        options.validate);\n\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent &&\n    (_names.watchAll ||\n        _names.watch.has(name) ||\n        [..._names.watch].some((watchName) => name.startsWith(watchName) &&\n            /^\\.\\w+/.test(name.slice(watchName.length))));\n\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n    for (const key of fieldsNames || Object.keys(fields)) {\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                }\n                else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                }\n                else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            }\n            else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\n\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name,\n        };\n    }\n    const names = name.split('.');\n    while (names.length) {\n        const fieldName = names.join('.');\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return { name };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError,\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root,\n            };\n        }\n        names.pop();\n    }\n    return {\n        name,\n    };\n}\n\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return (isEmptyObject(formState) ||\n        Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n        Object.keys(formState).find((key) => _proxyFormState[key] ===\n            (!isRoot || VALIDATION_MODE.all)));\n};\n\nvar shouldSubscribeByName = (name, signalName, exact) => !name ||\n    !signalName ||\n    name === signalName ||\n    convertToArrayPayload(name).some((currentName) => currentName &&\n        (exact\n            ? currentName === signalName\n            : currentName.startsWith(signalName) ||\n                signalName.startsWith(currentName)));\n\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n    if (mode.isOnAll) {\n        return false;\n    }\n    else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    }\n    else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    }\n    else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\n\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\n\nvar updateFieldArrayRootError = (errors, error, name) => {\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, 'root', error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\n\nfunction getValidateError(result, ref, type = 'validate') {\n    if (isString(result) ||\n        (Array.isArray(result) && result.every(isString)) ||\n        (isBoolean(result) && !result)) {\n        return {\n            type,\n            message: isString(result) ? result : '',\n            ref,\n        };\n    }\n}\n\nvar getValueAndMessage = (validationData) => isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n    };\n\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message) => {\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = ((valueAsNumber || isFileInput(ref)) &&\n        isUndefined(ref.value) &&\n        isUndefined(inputValue)) ||\n        (isHTMLElement(ref) && ref.value === '') ||\n        inputValue === '' ||\n        (Array.isArray(inputValue) && !inputValue.length);\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n        };\n    };\n    if (isFieldArray\n        ? !Array.isArray(inputValue) || !inputValue.length\n        : required &&\n            ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n                (isBoolean(inputValue) && !inputValue) ||\n                (isCheckBox && !getCheckboxValue(refs).isValid) ||\n                (isRadio && !getRadioValue(refs).isValid))) {\n        const { value, message } = isString(required)\n            ? { value: !!required, message: required }\n            : getValueAndMessage(required);\n        if (value) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber ||\n                (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        }\n        else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time) => new Date(new Date().toDateString() + ' ' + time);\n            const isTime = ref.type == 'time';\n            const isWeek = ref.type == 'week';\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime\n                    ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n                    : isWeek\n                        ? inputValue > maxOutput.value\n                        : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime\n                    ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n                    : isWeek\n                        ? inputValue < minOutput.value\n                        : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) &&\n        !isEmpty &&\n        (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) &&\n            inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) &&\n            inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message),\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        }\n        else if (isObject(validate)) {\n            let validationResult = {};\n            for (const key in validate) {\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message),\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult,\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\n\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true,\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props,\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false,\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values)\n        ? cloneObject(_options.defaultValues || _options.values) || {}\n        : {};\n    let _formValues = _options.shouldUnregister\n        ? {}\n        : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false,\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set(),\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState,\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject(),\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback) => (wait) => {\n        clearTimeout(timer);\n        timer = setTimeout(callback, wait);\n    };\n    const _setValid = async (shouldUpdateValid) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValid ||\n                _proxySubscribeFormState.isValid ||\n                shouldUpdateValid)) {\n            const isValid = _options.resolver\n                ? isEmptyObject((await _runSchema()).errors)\n                : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid,\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValidating ||\n                _proxyFormState.validatingFields ||\n                _proxySubscribeFormState.isValidating ||\n                _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name) => {\n                if (name) {\n                    isValidating\n                        ? set(_formState.validatingFields, name, isValidating)\n                        : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields),\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields ||\n                _proxySubscribeFormState.touchedFields) &&\n                shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid,\n            });\n        }\n        else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error) => {\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors,\n        });\n    };\n    const _setErrors = (errors) => {\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false,\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n            isUndefined(defaultValue) ||\n                (ref && ref.defaultChecked) ||\n                shouldSkipSetValueAs\n                ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f))\n                : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name,\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine\n                    ? unset(_formState.dirtyFields, name)\n                    : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField =\n                    shouldUpdateField ||\n                        ((_proxyFormState.dirtyFields ||\n                            _proxySubscribeFormState.dirtyFields) &&\n                            isPreviousDirty !== !isCurrentFieldPristine);\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField =\n                        shouldUpdateField ||\n                            ((_proxyFormState.touchedFields ||\n                                _proxySubscribeFormState.touchedFields) &&\n                                isPreviousFieldTouched !== isBlurEvent);\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState) => {\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n            isBoolean(isValid) &&\n            _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(() => updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        }\n        else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error\n                ? set(_formState.errors, name, error)\n                : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n            !isEmptyObject(fieldState) ||\n            shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n                errors: _formState.errors,\n                name,\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState,\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name) => {\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names) => {\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names) {\n                const error = get(errors, name);\n                error\n                    ? set(_formState.errors, name, error)\n                    : unset(_formState.errors, name);\n            }\n        }\n        else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true,\n    }) => {\n        for (const name in fields) {\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([_f.name], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([_f.name]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid &&\n                        (get(fieldError, _f.name)\n                            ? isFieldArrayRoot\n                                ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name)\n                                : set(_formState.errors, _f.name, fieldError[_f.name])\n                            : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) &&\n                    (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = () => {\n        for (const name of _names.unMount) {\n            const field = get(_fields, name);\n            field &&\n                (field._f.refs\n                    ? field._f.refs.every((ref) => !live(ref))\n                    : !live(field._f.ref)) &&\n                unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data) => !_options.disabled &&\n        (name && data && set(_formValues, name, data),\n            !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n        ...(_state.mount\n            ? _formValues\n            : isUndefined(defaultValue)\n                ? _defaultValues\n                : isString(names)\n                    ? { [names]: defaultValue }\n                    : defaultValue),\n    }, isGlobal, defaultValue);\n    const _getFieldArray = (name) => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        let fieldValue = value;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled &&\n                    set(_formValues, name, getFieldValueAs(value, fieldReference));\n                fieldValue =\n                    isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n                        ? ''\n                        : value;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [...fieldReference.ref.options].forEach((optionRef) => (optionRef.selected = fieldValue.includes(optionRef.value)));\n                }\n                else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef) => {\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data) => data === checkboxRef.value);\n                                }\n                                else {\n                                    checkboxRef.checked =\n                                        fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    }\n                    else {\n                        fieldReference.refs.forEach((radioRef) => (radioRef.checked = radioRef.value === fieldValue));\n                    }\n                }\n                else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = '';\n                }\n                else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues),\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) &&\n            updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value, options) => {\n        for (const fieldKey in value) {\n            if (!value.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value[fieldKey];\n            const fieldName = name + '.' + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) ||\n                isObject(fieldValue) ||\n                (field && !field._f)) &&\n                !isDateObject(fieldValue)\n                ? setValues(fieldName, fieldValue, options)\n                : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues),\n            });\n            if ((_proxyFormState.isDirty ||\n                _proxyFormState.dirtyFields ||\n                _proxySubscribeFormState.isDirty ||\n                _proxySubscribeFormState.dirtyFields) &&\n                options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue),\n                });\n            }\n        }\n        else {\n            field && !field._f && !isNullOrUndefined(cloneValue)\n                ? setValues(name, cloneValue, options)\n                : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({ ..._formState, name });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues),\n        });\n    };\n    const onChange = async (event) => {\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue) => {\n            isFieldValueUpdated =\n                Number.isNaN(fieldValue) ||\n                    (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n                    deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type\n                ? getFieldValue(field._f)\n                : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = (!hasValidation(field._f) &&\n                !_options.resolver &&\n                !get(_formState.errors, name) &&\n                !field._f.deps) ||\n                skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                if (!target || !target.readOnly) {\n                    field._f.onBlur && field._f.onBlur(event);\n                    delayErrorCallback && delayErrorCallback(0);\n                }\n            }\n            else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent &&\n                _subjects.state.next({\n                    name,\n                    type: event.type,\n                    values: cloneObject(_formValues),\n                });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === 'onBlur') {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    }\n                    else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return (shouldRender &&\n                    _subjects.state.next({ name, ...(watched ? {} : fieldState) }));\n            }\n            !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            }\n            else {\n                _updateIsValidating([name], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    }\n                    else if (_proxyFormState.isValid ||\n                        _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps &&\n                    (!Array.isArray(field._f.deps) || field._f.deps.length > 0) &&\n                    trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key) => {\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {}) => {\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name\n                ? !fieldNames.some((name) => get(errors, name))\n                : isValid;\n        }\n        else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName) => {\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? { [fieldName]: field } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        }\n        else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...(!isString(name) ||\n                ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n                    isValid !== _formState.isValid)\n                ? {}\n                : { name }),\n            ...(_options.resolver || !name ? { isValid } : {}),\n            errors: _formState.errors,\n        });\n        options.shouldFocus &&\n            !validationResult &&\n            iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames, config) => {\n        let values = {\n            ...(_state.mount ? _formValues : _defaultValues),\n        };\n        if (config) {\n            values = extractFormValues(config.dirtyFields ? _formState.dirtyFields : _formState.touchedFields, values);\n        }\n        return isUndefined(fieldNames)\n            ? values\n            : isString(fieldNames)\n                ? get(values, fieldNames)\n                : fieldNames.map((name) => get(values, name));\n    };\n    const getFieldState = (name, formState) => ({\n        invalid: !!get((formState || _formState).errors, name),\n        isDirty: !!get((formState || _formState).dirtyFields, name),\n        error: get((formState || _formState).errors, name),\n        isValidating: !!get(_formState.validatingFields, name),\n        isTouched: !!get((formState || _formState).touchedFields, name),\n    });\n    const clearErrors = (name) => {\n        name &&\n            convertToArrayPayload(name).forEach((inputName) => unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {},\n        });\n    };\n    const setError = (name, error, options) => {\n        const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref,\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false,\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue) => isFunction(name)\n        ? _subjects.state.subscribe({\n            next: (payload) => 'values' in payload &&\n                name(_getWatch(undefined, defaultValue), payload),\n        })\n        : _getWatch(name, defaultValue, true);\n    const _subscribe = (props) => _subjects.state.subscribe({\n        next: (formState) => {\n            if (shouldSubscribeByName(props.name, formState.name, props.exact) &&\n                shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                props.callback({\n                    values: { ..._formValues },\n                    ..._formState,\n                    ...formState,\n                    defaultValues: _defaultValues,\n                });\n            }\n        },\n    }).unsubscribe;\n    const subscribe = (props) => {\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState,\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState,\n        });\n    };\n    const unregister = (name, options = {}) => {\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating &&\n                unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister &&\n                !options.keepDefaultValue &&\n                unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues),\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name, }) => {\n        if ((isBoolean(disabled) && _state.mount) ||\n            !!disabled ||\n            _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {}) => {\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...(field || {}),\n            _f: {\n                ...(field && field._f ? field._f : { ref: { name } }),\n                name,\n                mount: true,\n                ...options,\n            },\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled)\n                    ? options.disabled\n                    : _options.disabled,\n                name,\n            });\n        }\n        else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...(disabledIsDefined\n                ? { disabled: options.disabled || _options.disabled }\n                : {}),\n            ...(_options.progressive\n                ? {\n                    required: !!options.required,\n                    min: getRuleValue(options.min),\n                    max: getRuleValue(options.max),\n                    minLength: getRuleValue(options.minLength),\n                    maxLength: getRuleValue(options.maxLength),\n                    pattern: getRuleValue(options.pattern),\n                }\n                : {}),\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref) => {\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value)\n                        ? ref.querySelectorAll\n                            ? ref.querySelectorAll('input,select,textarea')[0] || ref\n                            : ref\n                        : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox\n                        ? refs.find((option) => option === fieldRef)\n                        : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...(radioOrCheckbox\n                                ? {\n                                    refs: [\n                                        ...refs.filter(live),\n                                        fieldRef,\n                                        ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                                    ],\n                                    ref: { type: fieldRef.type, name },\n                                }\n                                : { ref: fieldRef }),\n                        },\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                }\n                else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) &&\n                        !(isNameInFieldArray(_names.array, name) && _state.action) &&\n                        _names.unMount.add(name);\n                }\n            },\n        };\n    };\n    const _focusError = () => _options.shouldFocusError &&\n        iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled) => {\n        if (isBoolean(disabled)) {\n            _subjects.state.next({ disabled });\n            iterateFieldsByAction(_fields, (ref, name) => {\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef) => {\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid) => async (e) => {\n        let onValidError = undefined;\n        if (e) {\n            e.preventDefault && e.preventDefault();\n            e.persist &&\n                e.persist();\n        }\n        let fieldValues = cloneObject(_formValues);\n        _subjects.state.next({\n            isSubmitting: true,\n        });\n        if (_options.resolver) {\n            const { errors, values } = await _runSchema();\n            _formState.errors = errors;\n            fieldValues = cloneObject(values);\n        }\n        else {\n            await executeBuiltInValidation(_fields);\n        }\n        if (_names.disabled.size) {\n            for (const name of _names.disabled) {\n                unset(fieldValues, name);\n            }\n        }\n        unset(_formState.errors, 'root');\n        if (isEmptyObject(_formState.errors)) {\n            _subjects.state.next({\n                errors: {},\n            });\n            try {\n                await onValid(fieldValues, e);\n            }\n            catch (error) {\n                onValidError = error;\n            }\n        }\n        else {\n            if (onInvalid) {\n                await onInvalid({ ..._formState.errors }, e);\n            }\n            _focusError();\n            setTimeout(_focusError);\n        }\n        _subjects.state.next({\n            isSubmitted: true,\n            isSubmitting: false,\n            isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n            submitCount: _formState.submitCount + 1,\n            errors: _formState.errors,\n        });\n        if (onValidError) {\n            throw onValidError;\n        }\n    };\n    const resetField = (name, options = {}) => {\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            }\n            else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue\n                    ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n                    : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({ ..._formState });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {}) => {\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)) {\n                    get(_formState.dirtyFields, fieldName)\n                        ? set(values, fieldName, get(_formValues, fieldName))\n                        : setValue(fieldName, get(values, fieldName));\n                }\n            }\n            else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount) {\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs)\n                                ? field._f.refs[0]\n                                : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest('form');\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (keepStateOptions.keepFieldsRef) {\n                    for (const fieldName of _names.mount) {\n                        setValue(fieldName, get(values, fieldName));\n                    }\n                }\n                else {\n                    _fields = {};\n                }\n            }\n            _formValues = _options.shouldUnregister\n                ? keepStateOptions.keepDefaultValues\n                    ? cloneObject(_defaultValues)\n                    : {}\n                : cloneObject(values);\n            _subjects.array.next({\n                values: { ...values },\n            });\n            _subjects.state.next({\n                values: { ...values },\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: '',\n        };\n        _state.mount =\n            !_proxyFormState.isValid ||\n                !!keepStateOptions.keepIsValid ||\n                !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount\n                ? _formState.submitCount\n                : 0,\n            isDirty: isEmptyResetValues\n                ? false\n                : keepStateOptions.keepDirty\n                    ? _formState.isDirty\n                    : !!(keepStateOptions.keepDefaultValues &&\n                        !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted\n                ? _formState.isSubmitted\n                : false,\n            dirtyFields: isEmptyResetValues\n                ? {}\n                : keepStateOptions.keepDirtyValues\n                    ? keepStateOptions.keepDefaultValues && _formValues\n                        ? getDirtyFields(_defaultValues, _formValues)\n                        : _formState.dirtyFields\n                    : keepStateOptions.keepDefaultValues && formValues\n                        ? getDirtyFields(_defaultValues, formValues)\n                        : keepStateOptions.keepDirty\n                            ? _formState.dirtyFields\n                            : {},\n            touchedFields: keepStateOptions.keepTouched\n                ? _formState.touchedFields\n                : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n                ? _formState.isSubmitSuccessful\n                : false,\n            isSubmitting: false,\n            defaultValues: _defaultValues,\n        });\n    };\n    const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues)\n        ? formValues(_formValues)\n        : formValues, keepStateOptions);\n    const setFocus = (name, options = {}) => {\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs\n                ? fieldReference.refs[0]\n                : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect &&\n                    isFunction(fieldRef.select) &&\n                    fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState) => {\n        _formState = {\n            ..._formState,\n            ...updatedFormState,\n        };\n    };\n    const _resetDefaultValues = () => isFunction(_options.defaultValues) &&\n        _options.defaultValues().then((values) => {\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false,\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields() {\n                return _fields;\n            },\n            get _formValues() {\n                return _formValues;\n            },\n            get _state() {\n                return _state;\n            },\n            set _state(value) {\n                _state = value;\n            },\n            get _defaultValues() {\n                return _defaultValues;\n            },\n            get _names() {\n                return _names;\n            },\n            set _names(value) {\n                _names = value;\n            },\n            get _formState() {\n                return _formState;\n            },\n            get _options() {\n                return _options;\n            },\n            set _options(value) {\n                _options = {\n                    ..._options,\n                    ...value,\n                };\n            },\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState,\n    };\n    return {\n        ...methods,\n        formControl: methods,\n    };\n}\n\nvar generateId = () => {\n    if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n        return crypto.randomUUID();\n    }\n    const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n    });\n};\n\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n        `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n\nvar appendAt = (data, value) => [\n    ...data,\n    ...convertToArrayPayload(value),\n];\n\nvar fillEmptyArray = (value) => Array.isArray(value) ? value.map(() => undefined) : undefined;\n\nfunction insert(data, index, value) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value),\n        ...data.slice(index),\n    ];\n}\n\nvar moveArrayAt = (data, from, to) => {\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\n\nvar prependAt = (data, value) => [\n    ...convertToArrayPayload(value),\n    ...convertToArrayPayload(data),\n];\n\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [...data];\n    for (const index of indexes) {\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index)\n    ? []\n    : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\n\nvar swapArrayAt = (data, indexA, indexB) => {\n    [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n\nvar updateAt = (fieldValues, index, value) => {\n    fieldValues[index] = value;\n    return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = 'id', shouldUnregister, rules, } = props;\n    const [fields, setFields] = React.useState(control._getFieldArray(name));\n    const ids = React.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = React.useRef(fields);\n    const _actioned = React.useRef(false);\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    React.useMemo(() => rules &&\n        control.register(name, rules), [control, rules, name]);\n    useIsomorphicLayoutEffect(() => control._subjects.array.subscribe({\n        next: ({ values, name: fieldArrayName, }) => {\n            if (fieldArrayName === name || !fieldArrayName) {\n                const fieldValues = get(values, name);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n    }).unsubscribe, [control, name]);\n    const updateValues = React.useCallback((updatedFieldArrayValues) => {\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [control, name]);\n    const append = (value, options) => {\n        const appendValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const prepend = (value, options) => {\n        const prependValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const remove = (index) => {\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) &&\n            set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index,\n        });\n    };\n    const insert$1 = (index, value, options) => {\n        const insertValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value),\n        });\n    };\n    const swap = (indexA, indexB) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB,\n        }, false);\n    };\n    const move = (from, to) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to,\n        }, false);\n    };\n    const update = (index, value) => {\n        const updateValue = cloneObject(value);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue,\n        }, true, false);\n    };\n    const replace = (value) => {\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([...updatedFieldArrayValues]);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, [...updatedFieldArrayValues], (data) => data, {}, true, false);\n    };\n    React.useEffect(() => {\n        control._state.action = false;\n        isWatched(name, control._names) &&\n            control._subjects.state.next({\n                ...control._formState,\n            });\n        if (_actioned.current &&\n            (!getValidationModes(control._options.mode).isOnSubmit ||\n                control._formState.isSubmitted) &&\n            !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([name]).then((result) => {\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError\n                        ? (!error && existingError.type) ||\n                            (error &&\n                                (existingError.type !== error.type ||\n                                    existingError.message !== error.message))\n                        : error && error.type) {\n                        error\n                            ? set(control._formState.errors, name, error)\n                            : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors,\n                        });\n                    }\n                });\n            }\n            else {\n                const field = get(control._fields, name);\n                if (field &&\n                    field._f &&\n                    !(getValidationModes(control._options.reValidateMode).isOnSubmit &&\n                        getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error) => !isEmptyObject(error) &&\n                        control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name),\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues),\n        });\n        control._names.focus &&\n            iterateFieldsByAction(control._fields, (ref, key) => {\n                if (control._names.focus &&\n                    key.startsWith(control._names.focus) &&\n                    ref.focus) {\n                    ref.focus();\n                    return 1;\n                }\n                return;\n            });\n        control._names.focus = '';\n        control._setValid();\n        _actioned.current = false;\n    }, [fields, name, control]);\n    React.useEffect(() => {\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return () => {\n            const updateMounted = (name, value) => {\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, keyName, shouldUnregister]);\n    return {\n        swap: React.useCallback(swap, [updateValues, name, control]),\n        move: React.useCallback(move, [updateValues, name, control]),\n        prepend: React.useCallback(prepend, [updateValues, name, control]),\n        append: React.useCallback(append, [updateValues, name, control]),\n        remove: React.useCallback(remove, [updateValues, name, control]),\n        insert: React.useCallback(insert$1, [updateValues, name, control]),\n        update: React.useCallback(update, [updateValues, name, control]),\n        replace: React.useCallback(replace, [updateValues, name, control]),\n        fields: React.useMemo(() => fields.map((field, index) => ({\n            ...field,\n            [keyName]: ids.current[index] || generateId(),\n        })), [fields, keyName]),\n    };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n    const _formControl = React.useRef(undefined);\n    const _values = React.useRef(undefined);\n    const [formState, updateFormState] = React.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues)\n            ? undefined\n            : props.defaultValues,\n    });\n    if (!_formControl.current) {\n        if (props.formControl) {\n            _formControl.current = {\n                ...props.formControl,\n                formState,\n            };\n            if (props.defaultValues && !isFunction(props.defaultValues)) {\n                props.formControl.reset(props.defaultValues, props.resetOptions);\n            }\n        }\n        else {\n            const { formControl, ...rest } = createFormControl(props);\n            _formControl.current = {\n                ...rest,\n                formState,\n            };\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(() => {\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: () => updateFormState({ ...control._formState }),\n            reRenderRoot: true,\n        });\n        updateFormState((data) => ({\n            ...data,\n            isReady: true,\n        }));\n        control._formState.isReady = true;\n        return sub;\n    }, [control]);\n    React.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n    React.useEffect(() => {\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [control, props.mode, props.reValidateMode]);\n    React.useEffect(() => {\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [control, props.errors]);\n    React.useEffect(() => {\n        props.shouldUnregister &&\n            control._subjects.state.next({\n                values: control._getWatch(),\n            });\n    }, [control, props.shouldUnregister]);\n    React.useEffect(() => {\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty,\n                });\n            }\n        }\n    }, [control, formState.isDirty]);\n    React.useEffect(() => {\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, {\n                keepFieldsRef: true,\n                ...control._options.resetOptions,\n            });\n            _values.current = props.values;\n            updateFormState((state) => ({ ...state }));\n        }\n        else {\n            control._resetDefaultValues();\n        }\n    }, [control, props.values]);\n    React.useEffect(() => {\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({ ...control._formState });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n\nexport { Controller, Form, FormProvider, appendErrors, createFormControl, get, set, useController, useFieldArray, useForm, useFormContext, useFormState, useWatch };\n//# sourceMappingURL=index.esm.mjs.map\n"], "names": ["isCheckBoxInput", "element", "isDateObject", "value", "isNullOrUndefined", "isObjectType", "isObject", "getEventValue", "event", "getNodeParentName", "name", "isNameInFieldArray", "names", "isPlainObject", "tempObject", "prototypeCopy", "isWeb", "cloneObject", "data", "copy", "isArray", "isFileListInstance", "key", "is<PERSON>ey", "isUndefined", "val", "compact", "stringToPath", "input", "get", "object", "path", "defaultValue", "result", "isBoolean", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "EVENTS", "VALIDATION_MODE", "INPUT_VALIDATION_RULES", "HookFormContext", "React", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "_key", "useIsomorphicLayoutEffect", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "fieldName", "isPrimitive", "deepEqual", "object1", "object2", "_internal_visited", "keys1", "keys2", "val1", "val2", "appendErrors", "validateAllFieldCriteria", "errors", "type", "message", "convertToArrayPayload", "createSubject", "_observers", "observer", "o", "extractFormValues", "fieldsState", "values", "fieldState", "fieldValue", "nestedFieldsState", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "isMultipleSelect", "isRadioInput", "isRadioOrCheckbox", "ref", "live", "baseGet", "updatePath", "isEmptyArray", "obj", "unset", "paths", "childObject", "objectHasFunction", "isTraversable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "getDirty<PERSON>ields", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultResult", "validResult", "getCheckboxValue", "options", "option", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "_f", "getResolverOptions", "fieldsNames", "_fields", "criteriaMode", "shouldUseNativeValidation", "field", "isRegex", "getRuleValue", "rule", "getValidationModes", "mode", "ASYNC_FUNCTION", "hasPromiseValidation", "fieldReference", "validateFunction", "hasValidation", "isWatched", "isBlurEvent", "watchName", "iterateFieldsByAction", "action", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "error", "found<PERSON><PERSON>r", "shouldRenderFormState", "formStateData", "_proxyFormState", "updateFormState", "shouldSubscribeByName", "signalName", "exact", "currentName", "skipValidation", "isTouched", "isSubmitted", "reValidateMode", "unsetEmptyArray", "updateFieldArrayRootError", "fieldArrayErrors", "getValidateError", "getValueAndMessage", "validationData", "validateField", "disabled<PERSON>ieldN<PERSON>s", "isFieldArray", "refs", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "validate", "mount", "inputValue", "inputRef", "setCustomValidity", "isRadio", "isCheckBox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueNumber", "valueDate", "convertTimeToDate", "time", "isTime", "isWeek", "maxLengthOutput", "minLengthOutput", "patternValue", "validateError", "validationResult", "defaultOptions", "createFormControl", "props", "_options", "_formState", "_defaultValues", "_formValues", "_state", "delayError<PERSON><PERSON><PERSON>", "timer", "_proxySubscribeFormState", "_subjects", "shouldDisplayAllAssociatedErrors", "debounce", "callback", "wait", "_setValid", "shouldUpdateValid", "<PERSON><PERSON><PERSON><PERSON>", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "isValidating", "_setFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "field<PERSON><PERSON><PERSON>", "touchedFields", "_getDirty", "updateErrors", "_setErrors", "updateValidAndValue", "shouldSkipSetValueAs", "setFieldValue", "updateTouchAndDirty", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "output", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "updatedFormState", "executeSchemaAndUpdateState", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "context", "isFieldArrayRoot", "isPromiseFunction", "fieldError", "_removeUnmounted", "unregister", "getV<PERSON>ues", "_getWatch", "_getFieldArray", "optionRef", "checkboxRef", "radioRef", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "onChange", "target", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldSkipValidation", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "config", "getFieldState", "clearErrors", "inputName", "setError", "currentError", "currentRef", "restOfErrorTree", "watch", "payload", "_subscribe", "_setFormState", "subscribe", "_setDisabledField", "disabled", "register", "disabledIsDefined", "fieldRef", "radioOrCheckbox", "_focusError", "_disableForm", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "reset<PERSON>ield", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "fieldsToCheck", "form", "reset", "setFocus", "methods", "useForm", "_formControl", "_values", "formControl", "rest", "sub", "isDirty", "state"], "mappings": "yCAEA,IAAIA,GAAmBC,GAAYA,EAAQ,OAAS,WAEhDC,GAAgBC,GAAUA,aAAiB,KAE3CC,EAAqBD,GAAUA,GAAS,KAE5C,MAAME,GAAgBF,GAAU,OAAOA,GAAU,SACjD,IAAIG,EAAYH,GAAU,CAACC,EAAkBD,CAAK,GAC9C,CAAC,MAAM,QAAQA,CAAK,GACpBE,GAAaF,CAAK,GAClB,CAACD,GAAaC,CAAK,EAEnBI,GAAiBC,GAAUF,EAASE,CAAK,GAAKA,EAAM,OAClDR,GAAgBQ,EAAM,MAAM,EACxBA,EAAM,OAAO,QACbA,EAAM,OAAO,MACjBA,EAEFC,GAAqBC,GAASA,EAAK,UAAU,EAAGA,EAAK,OAAO,aAAa,CAAC,GAAKA,EAE/EC,GAAqB,CAACC,EAAOF,IAASE,EAAM,IAAIH,GAAkBC,CAAI,CAAC,EAEvEG,GAAiBC,GAAe,CAChC,MAAMC,EAAgBD,EAAW,aAAeA,EAAW,YAAY,UACvE,OAAQR,EAASS,CAAa,GAAKA,EAAc,eAAe,eAAe,CACnF,EAEIC,GAAQ,OAAO,OAAW,KAC1B,OAAO,OAAO,YAAgB,KAC9B,OAAO,SAAa,IAExB,SAASC,EAAYC,EAAM,CACvB,IAAIC,EACJ,MAAMC,EAAU,MAAM,QAAQF,CAAI,EAC5BG,EAAqB,OAAO,SAAa,IAAcH,aAAgB,SAAW,GACxF,GAAIA,aAAgB,KAChBC,EAAO,IAAI,KAAKD,CAAI,UAEf,EAAEF,KAAUE,aAAgB,MAAQG,MACxCD,GAAWd,EAASY,CAAI,GAEzB,GADAC,EAAOC,EAAU,CAAA,EAAK,OAAO,OAAO,OAAO,eAAeF,CAAI,CAAC,EAC3D,CAACE,GAAW,CAACP,GAAcK,CAAI,EAC/BC,EAAOD,MAGP,WAAWI,KAAOJ,EACVA,EAAK,eAAeI,CAAG,IACvBH,EAAKG,CAAG,EAAIL,EAAYC,EAAKI,CAAG,CAAC,OAM7C,QAAOJ,EAEX,OAAOC,CACX,CAEA,IAAII,GAASpB,GAAU,QAAQ,KAAKA,CAAK,EAErCqB,EAAeC,GAAQA,IAAQ,OAE/BC,GAAWvB,GAAU,MAAM,QAAQA,CAAK,EAAIA,EAAM,OAAO,OAAO,EAAI,CAAA,EAEpEwB,GAAgBC,GAAUF,GAAQE,EAAM,QAAQ,YAAa,EAAE,EAAE,MAAM,OAAO,CAAC,EAE/EC,EAAM,CAACC,EAAQC,EAAMC,IAAiB,CACtC,GAAI,CAACD,GAAQ,CAACzB,EAASwB,CAAM,EACzB,OAAOE,EAEX,MAAMC,GAAUV,GAAMQ,CAAI,EAAI,CAACA,CAAI,EAAIJ,GAAaI,CAAI,GAAG,OAAO,CAACE,EAAQX,IAAQlB,EAAkB6B,CAAM,EAAIA,EAASA,EAAOX,CAAG,EAAGQ,CAAM,EAC3I,OAAON,EAAYS,CAAM,GAAKA,IAAWH,EACnCN,EAAYM,EAAOC,CAAI,CAAC,EACpBC,EACAF,EAAOC,CAAI,EACfE,CACV,EAEIC,EAAa/B,GAAU,OAAOA,GAAU,UAExCgC,EAAM,CAACL,EAAQC,EAAM5B,IAAU,CAC/B,IAAIiC,EAAQ,GACZ,MAAMC,EAAWd,GAAMQ,CAAI,EAAI,CAACA,CAAI,EAAIJ,GAAaI,CAAI,EACnDO,EAASD,EAAS,OAClBE,EAAYD,EAAS,EAC3B,KAAO,EAAEF,EAAQE,GAAQ,CACrB,MAAMhB,EAAMe,EAASD,CAAK,EAC1B,IAAII,EAAWrC,EACf,GAAIiC,IAAUG,EAAW,CACrB,MAAME,EAAWX,EAAOR,CAAG,EAC3BkB,EACIlC,EAASmC,CAAQ,GAAK,MAAM,QAAQA,CAAQ,EACtCA,EACC,MAAM,CAACJ,EAASD,EAAQ,CAAC,CAAC,EAEvB,CAAA,EADA,CAAA,CAElB,CACA,GAAId,IAAQ,aAAeA,IAAQ,eAAiBA,IAAQ,YACxD,OAEJQ,EAAOR,CAAG,EAAIkB,EACdV,EAASA,EAAOR,CAAG,CACvB,CACJ,EAEA,MAAMoB,GAAS,CACX,KAAM,OACN,UAAW,UAEf,EACMC,EAAkB,CACpB,OAAQ,SACR,SAAU,WACV,SAAU,WACV,UAAW,YACX,IAAK,KACT,EACMC,EAAyB,CAC3B,IAAK,MACL,IAAK,MACL,UAAW,YACX,UAAW,YACX,QAAS,UACT,SAAU,WACV,SAAU,UACd,EAEMC,GAAkBC,EAAM,cAAc,IAAI,EAChDD,GAAgB,YAAc,kBAmE9B,IAAIE,GAAoB,CAACC,EAAWC,EAASC,EAAqBC,EAAS,KAAS,CAChF,MAAMlB,EAAS,CACX,cAAegB,EAAQ,cAC/B,EACI,UAAW3B,KAAO0B,EACd,OAAO,eAAef,EAAQX,EAAK,CAC/B,IAAK,IAAM,CACP,MAAM8B,EAAO9B,EACb,OAAI2B,EAAQ,gBAAgBG,CAAI,IAAMT,EAAgB,MAClDM,EAAQ,gBAAgBG,CAAI,EAAI,CAACD,GAAUR,EAAgB,KAGxDK,EAAUI,CAAI,CACzB,CACZ,CAAS,EAEL,OAAOnB,CACX,EAEA,MAAMoB,GAA4B,OAAO,OAAW,IAAcP,EAAM,gBAAkBA,EAAM,UAgEhG,IAAIQ,EAAYnD,GAAU,OAAOA,GAAU,SAEvCoD,GAAsB,CAAC3C,EAAO4C,EAAQC,EAAYC,EAAU1B,IACxDsB,EAAS1C,CAAK,GACd8C,GAAYF,EAAO,MAAM,IAAI5C,CAAK,EAC3BiB,EAAI4B,EAAY7C,EAAOoB,CAAY,GAE1C,MAAM,QAAQpB,CAAK,EACZA,EAAM,IAAK+C,IAAeD,GAAYF,EAAO,MAAM,IAAIG,CAAS,EACnE9B,EAAI4B,EAAYE,CAAS,EAAE,GAEnCD,IAAaF,EAAO,SAAW,IACxBC,GAGPG,GAAezD,GAAUC,EAAkBD,CAAK,GAAK,CAACE,GAAaF,CAAK,EAE5E,SAAS0D,GAAUC,EAASC,EAASC,EAAoB,IAAI,QAAW,CACpE,GAAIJ,GAAYE,CAAO,GAAKF,GAAYG,CAAO,EAC3C,OAAOD,IAAYC,EAEvB,GAAI7D,GAAa4D,CAAO,GAAK5D,GAAa6D,CAAO,EAC7C,OAAOD,EAAQ,YAAcC,EAAQ,QAAO,EAEhD,MAAME,EAAQ,OAAO,KAAKH,CAAO,EAC3BI,EAAQ,OAAO,KAAKH,CAAO,EACjC,GAAIE,EAAM,SAAWC,EAAM,OACvB,MAAO,GAEX,GAAIF,EAAkB,IAAIF,CAAO,GAAKE,EAAkB,IAAID,CAAO,EAC/D,MAAO,GAEXC,EAAkB,IAAIF,CAAO,EAC7BE,EAAkB,IAAID,CAAO,EAC7B,UAAWzC,KAAO2C,EAAO,CACrB,MAAME,EAAOL,EAAQxC,CAAG,EACxB,GAAI,CAAC4C,EAAM,SAAS5C,CAAG,EACnB,MAAO,GAEX,GAAIA,IAAQ,MAAO,CACf,MAAM8C,EAAOL,EAAQzC,CAAG,EACxB,GAAKpB,GAAaiE,CAAI,GAAKjE,GAAakE,CAAI,GACvC9D,EAAS6D,CAAI,GAAK7D,EAAS8D,CAAI,GAC/B,MAAM,QAAQD,CAAI,GAAK,MAAM,QAAQC,CAAI,EACxC,CAACP,GAAUM,EAAMC,EAAMJ,CAAiB,EACxCG,IAASC,EACX,MAAO,EAEf,CACJ,CACA,MAAO,EACX,CAgXA,IAAIC,GAAe,CAAC3D,EAAM4D,EAA0BC,EAAQC,EAAMC,IAAYH,EACxE,CACE,GAAGC,EAAO7D,CAAI,EACd,MAAO,CACH,GAAI6D,EAAO7D,CAAI,GAAK6D,EAAO7D,CAAI,EAAE,MAAQ6D,EAAO7D,CAAI,EAAE,MAAQ,CAAA,EAC9D,CAAC8D,CAAI,EAAGC,GAAW,EAC/B,CACA,EACM,CAAA,EAEFC,GAAyBvE,GAAW,MAAM,QAAQA,CAAK,EAAIA,EAAQ,CAACA,CAAK,EAEzEwE,GAAgB,IAAM,CACtB,IAAIC,EAAa,CAAA,EAiBjB,MAAO,CACH,IAAI,WAAY,CACZ,OAAOA,CACX,EACA,KApBUzE,GAAU,CACpB,UAAW0E,KAAYD,EACnBC,EAAS,MAAQA,EAAS,KAAK1E,CAAK,CAE5C,EAiBI,UAhBe0E,IACfD,EAAW,KAAKC,CAAQ,EACjB,CACH,YAAa,IAAM,CACfD,EAAaA,EAAW,OAAQE,GAAMA,IAAMD,CAAQ,CACxD,CACZ,GAWQ,YATgB,IAAM,CACtBD,EAAa,CAAA,CACjB,CAQJ,CACA,EAEA,SAASG,GAAkBC,EAAavB,EAAY,CAChD,MAAMwB,EAAS,CAAA,EACf,UAAW3D,KAAO0D,EACd,GAAIA,EAAY,eAAe1D,CAAG,EAAG,CACjC,MAAM4D,EAAaF,EAAY1D,CAAG,EAC5B6D,EAAa1B,EAAWnC,CAAG,EACjC,GAAI4D,GAAc5E,EAAS4E,CAAU,GAAKC,EAAY,CAClD,MAAMC,EAAoBL,GAAkBG,EAAYC,CAAU,EAC9D7E,EAAS8E,CAAiB,IAC1BH,EAAO3D,CAAG,EAAI8D,EAEtB,MACSJ,EAAY1D,CAAG,IACpB2D,EAAO3D,CAAG,EAAI6D,EAEtB,CAEJ,OAAOF,CACX,CAEA,IAAII,EAAiBlF,GAAUG,EAASH,CAAK,GAAK,CAAC,OAAO,KAAKA,CAAK,EAAE,OAElEmF,GAAerF,GAAYA,EAAQ,OAAS,OAE5CsF,EAAcpF,GAAU,OAAOA,GAAU,WAEzCqF,GAAiBrF,GAAU,CAC3B,GAAI,CAACa,GACD,MAAO,GAEX,MAAMyE,EAAQtF,EAAQA,EAAM,cAAgB,EAC5C,OAAQA,aACHsF,GAASA,EAAM,YAAcA,EAAM,YAAY,YAAc,YACtE,EAEIC,GAAoBzF,GAAYA,EAAQ,OAAS,kBAEjD0F,GAAgB1F,GAAYA,EAAQ,OAAS,QAE7C2F,GAAqBC,GAAQF,GAAaE,CAAG,GAAK7F,GAAgB6F,CAAG,EAErEC,GAAQD,GAAQL,GAAcK,CAAG,GAAKA,EAAI,YAE9C,SAASE,GAAQjE,EAAQkE,EAAY,CACjC,MAAM1D,EAAS0D,EAAW,MAAM,EAAG,EAAE,EAAE,OACvC,IAAI5D,EAAQ,EACZ,KAAOA,EAAQE,GACXR,EAASN,EAAYM,CAAM,EAAIM,IAAUN,EAAOkE,EAAW5D,GAAO,CAAC,EAEvE,OAAON,CACX,CACA,SAASmE,GAAaC,EAAK,CACvB,UAAW5E,KAAO4E,EACd,GAAIA,EAAI,eAAe5E,CAAG,GAAK,CAACE,EAAY0E,EAAI5E,CAAG,CAAC,EAChD,MAAO,GAGf,MAAO,EACX,CACA,SAAS6E,EAAMrE,EAAQC,EAAM,CACzB,MAAMqE,EAAQ,MAAM,QAAQrE,CAAI,EAC1BA,EACAR,GAAMQ,CAAI,EACN,CAACA,CAAI,EACLJ,GAAaI,CAAI,EACrBsE,EAAcD,EAAM,SAAW,EAAItE,EAASiE,GAAQjE,EAAQsE,CAAK,EACjEhE,EAAQgE,EAAM,OAAS,EACvB9E,EAAM8E,EAAMhE,CAAK,EACvB,OAAIiE,GACA,OAAOA,EAAY/E,CAAG,EAEtBc,IAAU,IACR9B,EAAS+F,CAAW,GAAKhB,EAAcgB,CAAW,GAC/C,MAAM,QAAQA,CAAW,GAAKJ,GAAaI,CAAW,IAC3DF,EAAMrE,EAAQsE,EAAM,MAAM,EAAG,EAAE,CAAC,EAE7BtE,CACX,CAEA,IAAIwE,GAAqBpF,GAAS,CAC9B,UAAWI,KAAOJ,EACd,GAAIqE,EAAWrE,EAAKI,CAAG,CAAC,EACpB,MAAO,GAGf,MAAO,EACX,EAEA,SAASiF,GAAcpG,EAAO,CAC1B,OAAO,MAAM,QAAQA,CAAK,GAAMG,EAASH,CAAK,GAAK,CAACmG,GAAkBnG,CAAK,CAC/E,CACA,SAASqG,GAAgBtF,EAAMuF,EAAS,GAAI,CACxC,UAAWnF,KAAOJ,EACVqF,GAAcrF,EAAKI,CAAG,CAAC,GACvBmF,EAAOnF,CAAG,EAAI,MAAM,QAAQJ,EAAKI,CAAG,CAAC,EAAI,CAAA,EAAK,CAAA,EAC9CkF,GAAgBtF,EAAKI,CAAG,EAAGmF,EAAOnF,CAAG,CAAC,GAEhClB,EAAkBc,EAAKI,CAAG,CAAC,IACjCmF,EAAOnF,CAAG,EAAI,IAGtB,OAAOmF,CACX,CACA,SAASC,GAAexF,EAAMuC,EAAYkD,EAAuB,CACxDA,IACDA,EAAwBH,GAAgB/C,CAAU,GAEtD,UAAWnC,KAAOJ,EACVqF,GAAcrF,EAAKI,CAAG,CAAC,EACnBE,EAAYiC,CAAU,GAAKG,GAAY+C,EAAsBrF,CAAG,CAAC,EACjEqF,EAAsBrF,CAAG,EAAIkF,GAAgBtF,EAAKI,CAAG,EAAG,MAAM,QAAQJ,EAAKI,CAAG,CAAC,EAAI,CAAA,EAAK,CAAA,CAAE,EAG1FoF,GAAexF,EAAKI,CAAG,EAAGlB,EAAkBqD,CAAU,EAAI,CAAA,EAAKA,EAAWnC,CAAG,EAAGqF,EAAsBrF,CAAG,CAAC,EAI9GqF,EAAsBrF,CAAG,EAAI,CAACuC,GAAU3C,EAAKI,CAAG,EAAGmC,EAAWnC,CAAG,CAAC,EAG1E,OAAOqF,CACX,CAEA,MAAMC,GAAgB,CAClB,MAAO,GACP,QAAS,EACb,EACMC,GAAc,CAAE,MAAO,GAAM,QAAS,EAAI,EAChD,IAAIC,GAAoBC,GAAY,CAChC,GAAI,MAAM,QAAQA,CAAO,EAAG,CACxB,GAAIA,EAAQ,OAAS,EAAG,CACpB,MAAM9B,EAAS8B,EACV,OAAQC,GAAWA,GAAUA,EAAO,SAAW,CAACA,EAAO,QAAQ,EAC/D,IAAKA,GAAWA,EAAO,KAAK,EACjC,MAAO,CAAE,MAAO/B,EAAQ,QAAS,CAAC,CAACA,EAAO,MAAM,CACpD,CACA,OAAO8B,EAAQ,CAAC,EAAE,SAAW,CAACA,EAAQ,CAAC,EAAE,SAEjCA,EAAQ,CAAC,EAAE,YAAc,CAACvF,EAAYuF,EAAQ,CAAC,EAAE,WAAW,KAAK,EAC3DvF,EAAYuF,EAAQ,CAAC,EAAE,KAAK,GAAKA,EAAQ,CAAC,EAAE,QAAU,GAClDF,GACA,CAAE,MAAOE,EAAQ,CAAC,EAAE,MAAO,QAAS,EAAI,EAC5CF,GACRD,EACV,CACA,OAAOA,EACX,EAEIK,GAAkB,CAAC9G,EAAO,CAAE,cAAA+G,EAAe,YAAAC,EAAa,WAAAC,CAAU,IAAO5F,EAAYrB,CAAK,EACxFA,EACA+G,EACI/G,IAAU,GACN,IACAA,GACI,CAACA,EAETgH,GAAe7D,EAASnD,CAAK,EACzB,IAAI,KAAKA,CAAK,EACdiH,EACIA,EAAWjH,CAAK,EAChBA,EAElB,MAAMkH,GAAgB,CAClB,QAAS,GACT,MAAO,IACX,EACA,IAAIC,GAAiBP,GAAY,MAAM,QAAQA,CAAO,EAChDA,EAAQ,OAAO,CAACQ,EAAUP,IAAWA,GAAUA,EAAO,SAAW,CAACA,EAAO,SACrE,CACE,QAAS,GACT,MAAOA,EAAO,KAC1B,EACUO,EAAUF,EAAa,EAC3BA,GAEN,SAASG,GAAcC,EAAI,CACvB,MAAM5B,EAAM4B,EAAG,IACf,OAAInC,GAAYO,CAAG,EACRA,EAAI,MAEXF,GAAaE,CAAG,EACTyB,GAAcG,EAAG,IAAI,EAAE,MAE9B/B,GAAiBG,CAAG,EACb,CAAC,GAAGA,EAAI,eAAe,EAAE,IAAI,CAAC,CAAE,MAAA1F,CAAK,IAAOA,CAAK,EAExDH,GAAgB6F,CAAG,EACZiB,GAAiBW,EAAG,IAAI,EAAE,MAE9BR,GAAgBzF,EAAYqE,EAAI,KAAK,EAAI4B,EAAG,IAAI,MAAQ5B,EAAI,MAAO4B,CAAE,CAChF,CAEA,IAAIC,GAAqB,CAACC,EAAaC,EAASC,EAAcC,IAA8B,CACxF,MAAMrB,EAAS,CAAA,EACf,UAAW/F,KAAQiH,EAAa,CAC5B,MAAMI,EAAQlG,EAAI+F,EAASlH,CAAI,EAC/BqH,GAAS5F,EAAIsE,EAAQ/F,EAAMqH,EAAM,EAAE,CACvC,CACA,MAAO,CACH,aAAAF,EACA,MAAO,CAAC,GAAGF,CAAW,EACtB,OAAAlB,EACA,0BAAAqB,CACR,CACA,EAEIE,GAAW7H,GAAUA,aAAiB,OAEtC8H,GAAgBC,GAAS1G,EAAY0G,CAAI,EACvCA,EACAF,GAAQE,CAAI,EACRA,EAAK,OACL5H,EAAS4H,CAAI,EACTF,GAAQE,EAAK,KAAK,EACdA,EAAK,MAAM,OACXA,EAAK,MACTA,EAEVC,GAAsBC,IAAU,CAChC,WAAY,CAACA,GAAQA,IAASzF,EAAgB,SAC9C,SAAUyF,IAASzF,EAAgB,OACnC,WAAYyF,IAASzF,EAAgB,SACrC,QAASyF,IAASzF,EAAgB,IAClC,UAAWyF,IAASzF,EAAgB,SACxC,GAEA,MAAM0F,GAAiB,gBACvB,IAAIC,GAAwBC,GAAmB,CAAC,CAACA,GAC7C,CAAC,CAACA,EAAe,UACjB,CAAC,EAAGhD,EAAWgD,EAAe,QAAQ,GAClCA,EAAe,SAAS,YAAY,OAASF,IAC5C/H,EAASiI,EAAe,QAAQ,GAC7B,OAAO,OAAOA,EAAe,QAAQ,EAAE,KAAMC,GAAqBA,EAAiB,YAAY,OAASH,EAAc,GAE9HI,GAAiB1B,GAAYA,EAAQ,QACpCA,EAAQ,UACLA,EAAQ,KACRA,EAAQ,KACRA,EAAQ,WACRA,EAAQ,WACRA,EAAQ,SACRA,EAAQ,UAEZ2B,GAAY,CAAChI,EAAM8C,EAAQmF,IAAgB,CAACA,IAC3CnF,EAAO,UACJA,EAAO,MAAM,IAAI9C,CAAI,GACrB,CAAC,GAAG8C,EAAO,KAAK,EAAE,KAAMoF,GAAclI,EAAK,WAAWkI,CAAS,GAC3D,SAAS,KAAKlI,EAAK,MAAMkI,EAAU,MAAM,CAAC,CAAC,GAEvD,MAAMC,GAAwB,CAACpC,EAAQqC,EAAQnB,EAAaoB,IAAe,CACvE,UAAWzH,KAAOqG,GAAe,OAAO,KAAKlB,CAAM,EAAG,CAClD,MAAMsB,EAAQlG,EAAI4E,EAAQnF,CAAG,EAC7B,GAAIyG,EAAO,CACP,KAAM,CAAE,GAAAN,EAAI,GAAGuB,CAAY,EAAKjB,EAChC,GAAIN,EAAI,CACJ,GAAIA,EAAG,MAAQA,EAAG,KAAK,CAAC,GAAKqB,EAAOrB,EAAG,KAAK,CAAC,EAAGnG,CAAG,GAAK,CAACyH,EACrD,MAAO,GAEN,GAAItB,EAAG,KAAOqB,EAAOrB,EAAG,IAAKA,EAAG,IAAI,GAAK,CAACsB,EAC3C,MAAO,GAGP,GAAIF,GAAsBG,EAAcF,CAAM,EAC1C,KAGZ,SACSxI,EAAS0I,CAAY,GACtBH,GAAsBG,EAAcF,CAAM,EAC1C,KAGZ,CACJ,CAEJ,EAEA,SAASG,GAAkB1E,EAAQqD,EAASlH,EAAM,CAC9C,MAAMwI,EAAQrH,EAAI0C,EAAQ7D,CAAI,EAC9B,GAAIwI,GAAS3H,GAAMb,CAAI,EACnB,MAAO,CACH,MAAAwI,EACA,KAAAxI,CACZ,EAEI,MAAME,EAAQF,EAAK,MAAM,GAAG,EAC5B,KAAOE,EAAM,QAAQ,CACjB,MAAM+C,EAAY/C,EAAM,KAAK,GAAG,EAC1BmH,EAAQlG,EAAI+F,EAASjE,CAAS,EAC9BwF,EAAatH,EAAI0C,EAAQZ,CAAS,EACxC,GAAIoE,GAAS,CAAC,MAAM,QAAQA,CAAK,GAAKrH,IAASiD,EAC3C,MAAO,CAAE,KAAAjD,CAAI,EAEjB,GAAIyI,GAAcA,EAAW,KACzB,MAAO,CACH,KAAMxF,EACN,MAAOwF,CACvB,EAEQ,GAAIA,GAAcA,EAAW,MAAQA,EAAW,KAAK,KACjD,MAAO,CACH,KAAM,GAAGxF,CAAS,QAClB,MAAOwF,EAAW,IAClC,EAEQvI,EAAM,IAAG,CACb,CACA,MAAO,CACH,KAAAF,CACR,CACA,CAEA,IAAI0I,GAAwB,CAACC,EAAeC,EAAiBC,EAAiBpG,IAAW,CACrFoG,EAAgBF,CAAa,EAC7B,KAAM,CAAE,KAAA3I,EAAM,GAAGsC,CAAS,EAAKqG,EAC/B,OAAQhE,EAAcrC,CAAS,GAC3B,OAAO,KAAKA,CAAS,EAAE,QAAU,OAAO,KAAKsG,CAAe,EAAE,QAC9D,OAAO,KAAKtG,CAAS,EAAE,KAAM1B,GAAQgI,EAAgBhI,CAAG,KACnD,CAAC6B,GAAUR,EAAgB,IAAI,CAC5C,EAEI6G,GAAwB,CAAC9I,EAAM+I,EAAYC,IAAU,CAAChJ,GACtD,CAAC+I,GACD/I,IAAS+I,GACT/E,GAAsBhE,CAAI,EAAE,KAAMiJ,GAAgBA,IAC7CD,EACKC,IAAgBF,EAChBE,EAAY,WAAWF,CAAU,GAC/BA,EAAW,WAAWE,CAAW,EAAE,EAE/CC,GAAiB,CAACjB,EAAakB,EAAWC,EAAaC,EAAgB3B,IACnEA,EAAK,QACE,GAEF,CAAC0B,GAAe1B,EAAK,UACnB,EAAEyB,GAAalB,IAEjBmB,EAAcC,EAAe,SAAW3B,EAAK,UAC3C,CAACO,GAEHmB,EAAcC,EAAe,WAAa3B,EAAK,YAC7CO,EAEJ,GAGPqB,GAAkB,CAACnE,EAAKnF,IAAS,CAACgB,GAAQG,EAAIgE,EAAKnF,CAAI,CAAC,EAAE,QAAUyF,EAAMN,EAAKnF,CAAI,EAEnFuJ,GAA4B,CAAC1F,EAAQ2E,EAAOxI,IAAS,CACrD,MAAMwJ,EAAmBxF,GAAsB7C,EAAI0C,EAAQ7D,CAAI,CAAC,EAChE,OAAAyB,EAAI+H,EAAkB,OAAQhB,EAAMxI,CAAI,CAAC,EACzCyB,EAAIoC,EAAQ7D,EAAMwJ,CAAgB,EAC3B3F,CACX,EAEA,SAAS4F,GAAiBlI,EAAQ4D,EAAKrB,EAAO,WAAY,CACtD,GAAIlB,EAASrB,CAAM,GACd,MAAM,QAAQA,CAAM,GAAKA,EAAO,MAAMqB,CAAQ,GAC9CpB,EAAUD,CAAM,GAAK,CAACA,EACvB,MAAO,CACH,KAAAuC,EACA,QAASlB,EAASrB,CAAM,EAAIA,EAAS,GACrC,IAAA4D,CACZ,CAEA,CAEA,IAAIuE,GAAsBC,GAAmB/J,EAAS+J,CAAc,GAAK,CAACrC,GAAQqC,CAAc,EAC1FA,EACA,CACE,MAAOA,EACP,QAAS,EACjB,EAEIC,GAAgB,MAAOvC,EAAOwC,EAAoB9G,EAAYa,EAA0BwD,EAA2B0C,IAAiB,CACpI,KAAM,CAAE,IAAA3E,EAAK,KAAA4E,EAAM,SAAAC,EAAU,UAAAC,EAAW,UAAAC,EAAW,IAAAC,EAAK,IAAAC,EAAK,QAAAC,GAAS,SAAAC,EAAU,KAAAtK,EAAM,cAAAwG,EAAe,MAAA+D,EAAK,EAAMlD,EAAM,GAChHmD,EAAarJ,EAAI4B,EAAY/C,CAAI,EACvC,GAAI,CAACuK,IAASV,EAAmB,IAAI7J,CAAI,EACrC,MAAO,CAAA,EAEX,MAAMyK,EAAWV,EAAOA,EAAK,CAAC,EAAI5E,EAC5BuF,EAAqB3G,GAAY,CAC/BqD,GAA6BqD,EAAS,iBACtCA,EAAS,kBAAkBjJ,EAAUuC,CAAO,EAAI,GAAKA,GAAW,EAAE,EAClE0G,EAAS,eAAc,EAE/B,EACMjC,EAAQ,CAAA,EACRmC,GAAU1F,GAAaE,CAAG,EAC1ByF,EAAatL,GAAgB6F,CAAG,EAChCD,GAAoByF,IAAWC,EAC/BC,GAAYrE,GAAiB5B,GAAYO,CAAG,IAC9CrE,EAAYqE,EAAI,KAAK,GACrBrE,EAAY0J,CAAU,GACrB1F,GAAcK,CAAG,GAAKA,EAAI,QAAU,IACrCqF,IAAe,IACd,MAAM,QAAQA,CAAU,GAAK,CAACA,EAAW,OACxCM,GAAoBnH,GAAa,KAAK,KAAM3D,EAAM4D,EAA0B4E,CAAK,EACjFuC,EAAmB,CAACC,EAAWC,EAAkBC,EAAkBC,EAAUjJ,EAAuB,UAAWkJ,EAAUlJ,EAAuB,YAAc,CAChK,MAAM6B,EAAUiH,EAAYC,EAAmBC,EAC/C1C,EAAMxI,CAAI,EAAI,CACV,KAAMgL,EAAYG,EAAUC,EAC5B,QAAArH,EACA,IAAAoB,EACA,GAAG2F,GAAkBE,EAAYG,EAAUC,EAASrH,CAAO,CACvE,CACI,EACA,GAAI+F,EACE,CAAC,MAAM,QAAQU,CAAU,GAAK,CAACA,EAAW,OAC1CR,IACI,CAAC9E,KAAsB2F,GAAWnL,EAAkB8K,CAAU,IAC3DhJ,EAAUgJ,CAAU,GAAK,CAACA,GAC1BI,GAAc,CAACxE,GAAiB2D,CAAI,EAAE,SACtCY,IAAW,CAAC/D,GAAcmD,CAAI,EAAE,SAAW,CACpD,KAAM,CAAE,MAAAtK,EAAO,QAAAsE,CAAO,EAAKnB,EAASoH,CAAQ,EACtC,CAAE,MAAO,CAAC,CAACA,EAAU,QAASA,CAAQ,EACtCN,GAAmBM,CAAQ,EACjC,GAAIvK,IACA+I,EAAMxI,CAAI,EAAI,CACV,KAAMkC,EAAuB,SAC7B,QAAA6B,EACA,IAAK0G,EACL,GAAGK,GAAkB5I,EAAuB,SAAU6B,CAAO,CAC7E,EACgB,CAACH,GACD,OAAA8G,EAAkB3G,CAAO,EAClByE,CAGnB,CACA,GAAI,CAACqC,IAAY,CAACnL,EAAkByK,CAAG,GAAK,CAACzK,EAAkB0K,CAAG,GAAI,CAClE,IAAIY,EACAK,EACJ,MAAMC,EAAY5B,GAAmBU,CAAG,EAClCmB,EAAY7B,GAAmBS,CAAG,EACxC,GAAI,CAACzK,EAAkB8K,CAAU,GAAK,CAAC,MAAMA,CAAU,EAAG,CACtD,MAAMgB,EAAcrG,EAAI,eACnBqF,GAAa,CAACA,EACd9K,EAAkB4L,EAAU,KAAK,IAClCN,EAAYQ,EAAcF,EAAU,OAEnC5L,EAAkB6L,EAAU,KAAK,IAClCF,EAAYG,EAAcD,EAAU,MAE5C,KACK,CACD,MAAME,EAAYtG,EAAI,aAAe,IAAI,KAAKqF,CAAU,EAClDkB,EAAqBC,IAAS,IAAI,KAAK,IAAI,KAAI,EAAG,aAAY,EAAK,IAAMA,EAAI,EAC7EC,GAASzG,EAAI,MAAQ,OACrB0G,GAAS1G,EAAI,MAAQ,OACvBvC,EAAS0I,EAAU,KAAK,GAAKd,IAC7BQ,EAAYY,GACNF,EAAkBlB,CAAU,EAAIkB,EAAkBJ,EAAU,KAAK,EACjEO,GACIrB,EAAac,EAAU,MACvBG,EAAY,IAAI,KAAKH,EAAU,KAAK,GAE9C1I,EAAS2I,EAAU,KAAK,GAAKf,IAC7Ba,EAAYO,GACNF,EAAkBlB,CAAU,EAAIkB,EAAkBH,EAAU,KAAK,EACjEM,GACIrB,EAAae,EAAU,MACvBE,EAAY,IAAI,KAAKF,EAAU,KAAK,EAEtD,CACA,IAAIP,GAAaK,KACbN,EAAiB,CAAC,CAACC,EAAWM,EAAU,QAASC,EAAU,QAASrJ,EAAuB,IAAKA,EAAuB,GAAG,EACtH,CAAC0B,GACD,OAAA8G,EAAkBlC,EAAMxI,CAAI,EAAE,OAAO,EAC9BwI,CAGnB,CACA,IAAKyB,GAAaC,IACd,CAACW,IACAjI,EAAS4H,CAAU,GAAMV,GAAgB,MAAM,QAAQU,CAAU,GAAK,CACvE,MAAMsB,EAAkBpC,GAAmBO,CAAS,EAC9C8B,EAAkBrC,GAAmBQ,CAAS,EAC9Cc,EAAY,CAACtL,EAAkBoM,EAAgB,KAAK,GACtDtB,EAAW,OAAS,CAACsB,EAAgB,MACnCT,EAAY,CAAC3L,EAAkBqM,EAAgB,KAAK,GACtDvB,EAAW,OAAS,CAACuB,EAAgB,MACzC,IAAIf,GAAaK,KACbN,EAAiBC,EAAWc,EAAgB,QAASC,EAAgB,OAAO,EACxE,CAACnI,GACD,OAAA8G,EAAkBlC,EAAMxI,CAAI,EAAE,OAAO,EAC9BwI,CAGnB,CACA,GAAI6B,IAAW,CAACQ,GAAWjI,EAAS4H,CAAU,EAAG,CAC7C,KAAM,CAAE,MAAOwB,EAAc,QAAAjI,CAAO,EAAK2F,GAAmBW,EAAO,EACnE,GAAI/C,GAAQ0E,CAAY,GAAK,CAACxB,EAAW,MAAMwB,CAAY,IACvDxD,EAAMxI,CAAI,EAAI,CACV,KAAMkC,EAAuB,QAC7B,QAAA6B,EACA,IAAAoB,EACA,GAAG2F,GAAkB5I,EAAuB,QAAS6B,CAAO,CAC5E,EACgB,CAACH,GACD,OAAA8G,EAAkB3G,CAAO,EAClByE,CAGnB,CACA,GAAI8B,GACA,GAAIzF,EAAWyF,CAAQ,EAAG,CACtB,MAAM/I,EAAS,MAAM+I,EAASE,EAAYzH,CAAU,EAC9CkJ,EAAgBxC,GAAiBlI,EAAQkJ,CAAQ,EACvD,GAAIwB,IACAzD,EAAMxI,CAAI,EAAI,CACV,GAAGiM,EACH,GAAGnB,GAAkB5I,EAAuB,SAAU+J,EAAc,OAAO,CAC/F,EACoB,CAACrI,GACD,OAAA8G,EAAkBuB,EAAc,OAAO,EAChCzD,CAGnB,SACS5I,EAAS0K,CAAQ,EAAG,CACzB,IAAI4B,EAAmB,CAAA,EACvB,UAAWtL,KAAO0J,EAAU,CACxB,GAAI,CAAC3F,EAAcuH,CAAgB,GAAK,CAACtI,EACrC,MAEJ,MAAMqI,EAAgBxC,GAAiB,MAAMa,EAAS1J,CAAG,EAAE4J,EAAYzH,CAAU,EAAG0H,EAAU7J,CAAG,EAC7FqL,IACAC,EAAmB,CACf,GAAGD,EACH,GAAGnB,GAAkBlK,EAAKqL,EAAc,OAAO,CACvE,EACoBvB,EAAkBuB,EAAc,OAAO,EACnCrI,IACA4E,EAAMxI,CAAI,EAAIkM,GAG1B,CACA,GAAI,CAACvH,EAAcuH,CAAgB,IAC/B1D,EAAMxI,CAAI,EAAI,CACV,IAAKyK,EACL,GAAGyB,CACvB,EACoB,CAACtI,GACD,OAAO4E,CAGnB,EAEJ,OAAAkC,EAAkB,EAAI,EACflC,CACX,EAEA,MAAM2D,GAAiB,CACnB,KAAMlK,EAAgB,SACtB,eAAgBA,EAAgB,SAChC,iBAAkB,EACtB,EACA,SAASmK,GAAkBC,EAAQ,GAAI,CACnC,IAAIC,EAAW,CACX,GAAGH,GACH,GAAGE,CACX,EACQE,EAAa,CACb,YAAa,EACb,QAAS,GACT,QAAS,GACT,UAAW1H,EAAWyH,EAAS,aAAa,EAC5C,aAAc,GACd,YAAa,GACb,aAAc,GACd,mBAAoB,GACpB,QAAS,GACT,cAAe,CAAA,EACf,YAAa,CAAA,EACb,iBAAkB,CAAA,EAClB,OAAQA,EAAS,QAAU,CAAA,EAC3B,SAAUA,EAAS,UAAY,EACvC,EACQpF,EAAU,CAAA,EACVsF,EAAiB5M,EAAS0M,EAAS,aAAa,GAAK1M,EAAS0M,EAAS,MAAM,EAC3E/L,EAAY+L,EAAS,eAAiBA,EAAS,MAAM,GAAK,CAAA,EAC1D,CAAA,EACFG,EAAcH,EAAS,iBACrB,CAAA,EACA/L,EAAYiM,CAAc,EAC5BE,EAAS,CACT,OAAQ,GACR,MAAO,GACP,MAAO,EACf,EACQ5J,EAAS,CACT,MAAO,IAAI,IACX,SAAU,IAAI,IACd,QAAS,IAAI,IACb,MAAO,IAAI,IACX,MAAO,IAAI,GACnB,EACQ6J,EACAC,EAAQ,EACZ,MAAMhE,EAAkB,CACpB,QAAS,GACT,YAAa,GACb,iBAAkB,GAClB,cAAe,GACf,aAAc,GACd,QAAS,GACT,OAAQ,EAChB,EACI,IAAIiE,EAA2B,CAC3B,GAAGjE,CACX,EACI,MAAMkE,EAAY,CACd,MAAO7I,GAAa,EACpB,MAAOA,GAAa,CAC5B,EACU8I,GAAmCT,EAAS,eAAiBrK,EAAgB,IAC7E+K,EAAYC,GAAcC,GAAS,CACrC,aAAaN,CAAK,EAClBA,EAAQ,WAAWK,EAAUC,CAAI,CACrC,EACMC,EAAY,MAAOC,GAAsB,CAC3C,GAAI,CAACd,EAAS,WACT1D,EAAgB,SACbiE,EAAyB,SACzBO,GAAoB,CACxB,MAAMC,EAAUf,EAAS,SACnB3H,GAAe,MAAM2I,EAAU,GAAI,MAAM,EACzC,MAAMC,EAAyBrG,EAAS,EAAI,EAC9CmG,IAAYd,EAAW,SACvBO,EAAU,MAAM,KAAK,CACjB,QAAAO,CACpB,CAAiB,CAET,CACJ,EACMG,EAAsB,CAACtN,EAAOuN,IAAiB,CAC7C,CAACnB,EAAS,WACT1D,EAAgB,cACbA,EAAgB,kBAChBiE,EAAyB,cACzBA,EAAyB,qBAC5B3M,GAAS,MAAM,KAAK4C,EAAO,KAAK,GAAG,QAAS9C,GAAS,CAC9CA,IACAyN,EACMhM,EAAI8K,EAAW,iBAAkBvM,EAAMyN,CAAY,EACnDhI,EAAM8G,EAAW,iBAAkBvM,CAAI,EAErD,CAAC,EACD8M,EAAU,MAAM,KAAK,CACjB,iBAAkBP,EAAW,iBAC7B,aAAc,CAAC5H,EAAc4H,EAAW,gBAAgB,CACxE,CAAa,EAET,EACMmB,GAAiB,CAAC1N,EAAMuE,EAAS,CAAA,EAAIoJ,EAAQC,EAAMC,EAAkB,GAAMC,EAA6B,KAAS,CACnH,GAAIF,GAAQD,GAAU,CAACrB,EAAS,SAAU,CAEtC,GADAI,EAAO,OAAS,GACZoB,GAA8B,MAAM,QAAQ3M,EAAI+F,EAASlH,CAAI,CAAC,EAAG,CACjE,MAAM+N,EAAcJ,EAAOxM,EAAI+F,EAASlH,CAAI,EAAG4N,EAAK,KAAMA,EAAK,IAAI,EACnEC,GAAmBpM,EAAIyF,EAASlH,EAAM+N,CAAW,CACrD,CACA,GAAID,GACA,MAAM,QAAQ3M,EAAIoL,EAAW,OAAQvM,CAAI,CAAC,EAAG,CAC7C,MAAM6D,EAAS8J,EAAOxM,EAAIoL,EAAW,OAAQvM,CAAI,EAAG4N,EAAK,KAAMA,EAAK,IAAI,EACxEC,GAAmBpM,EAAI8K,EAAW,OAAQvM,EAAM6D,CAAM,EACtDyF,GAAgBiD,EAAW,OAAQvM,CAAI,CAC3C,CACA,IAAK4I,EAAgB,eACjBiE,EAAyB,gBACzBiB,GACA,MAAM,QAAQ3M,EAAIoL,EAAW,cAAevM,CAAI,CAAC,EAAG,CACpD,MAAMgO,EAAgBL,EAAOxM,EAAIoL,EAAW,cAAevM,CAAI,EAAG4N,EAAK,KAAMA,EAAK,IAAI,EACtFC,GAAmBpM,EAAI8K,EAAW,cAAevM,EAAMgO,CAAa,CACxE,EACIpF,EAAgB,aAAeiE,EAAyB,eACxDN,EAAW,YAAcvG,GAAewG,EAAgBC,CAAW,GAEvEK,EAAU,MAAM,KAAK,CACjB,KAAA9M,EACA,QAASiO,EAAUjO,EAAMuE,CAAM,EAC/B,YAAagI,EAAW,YACxB,OAAQA,EAAW,OACnB,QAASA,EAAW,OACpC,CAAa,CACL,MAEI9K,EAAIgL,EAAazM,EAAMuE,CAAM,CAErC,EACM2J,EAAe,CAAClO,EAAMwI,IAAU,CAClC/G,EAAI8K,EAAW,OAAQvM,EAAMwI,CAAK,EAClCsE,EAAU,MAAM,KAAK,CACjB,OAAQP,EAAW,MAC/B,CAAS,CACL,EACM4B,EAActK,GAAW,CAC3B0I,EAAW,OAAS1I,EACpBiJ,EAAU,MAAM,KAAK,CACjB,OAAQP,EAAW,OACnB,QAAS,EACrB,CAAS,CACL,EACM6B,EAAsB,CAACpO,EAAMqO,EAAsB5O,EAAO0F,IAAQ,CACpE,MAAMkC,EAAQlG,EAAI+F,EAASlH,CAAI,EAC/B,GAAIqH,EAAO,CACP,MAAM/F,EAAeH,EAAIsL,EAAazM,EAAMc,EAAYrB,CAAK,EAAI0B,EAAIqL,EAAgBxM,CAAI,EAAIP,CAAK,EAClGqB,EAAYQ,CAAY,GACnB6D,GAAOA,EAAI,gBACZkJ,EACE5M,EAAIgL,EAAazM,EAAMqO,EAAuB/M,EAAewF,GAAcO,EAAM,EAAE,CAAC,EACpFiH,EAActO,EAAMsB,CAAY,EACtCoL,EAAO,OAASS,EAAS,CAC7B,CACJ,EACMoB,EAAsB,CAACvO,EAAMyE,EAAYwD,EAAauG,EAAaC,IAAiB,CACtF,IAAIC,EAAoB,GACpBC,EAAkB,GACtB,MAAMC,EAAS,CACX,KAAA5O,CACZ,EACQ,GAAI,CAACsM,EAAS,SAAU,CACpB,GAAI,CAACrE,GAAeuG,EAAa,EACzB5F,EAAgB,SAAWiE,EAAyB,WACpD8B,EAAkBpC,EAAW,QAC7BA,EAAW,QAAUqC,EAAO,QAAUX,EAAS,EAC/CS,EAAoBC,IAAoBC,EAAO,SAEnD,MAAMC,EAAyB1L,GAAUhC,EAAIqL,EAAgBxM,CAAI,EAAGyE,CAAU,EAC9EkK,EAAkB,CAAC,CAACxN,EAAIoL,EAAW,YAAavM,CAAI,EACpD6O,EACMpJ,EAAM8G,EAAW,YAAavM,CAAI,EAClCyB,EAAI8K,EAAW,YAAavM,EAAM,EAAI,EAC5C4O,EAAO,YAAcrC,EAAW,YAChCmC,EACIA,IACM9F,EAAgB,aACdiE,EAAyB,cACzB8B,IAAoB,CAACE,CACrC,CACA,GAAI5G,EAAa,CACb,MAAM6G,EAAyB3N,EAAIoL,EAAW,cAAevM,CAAI,EAC5D8O,IACDrN,EAAI8K,EAAW,cAAevM,EAAMiI,CAAW,EAC/C2G,EAAO,cAAgBrC,EAAW,cAClCmC,EACIA,IACM9F,EAAgB,eACdiE,EAAyB,gBACzBiC,IAA2B7G,EAE/C,CACAyG,GAAqBD,GAAgB3B,EAAU,MAAM,KAAK8B,CAAM,CACpE,CACA,OAAOF,EAAoBE,EAAS,CAAA,CACxC,EACMG,GAAsB,CAAC/O,EAAMqN,EAAS7E,EAAOhE,IAAe,CAC9D,MAAMwK,EAAqB7N,EAAIoL,EAAW,OAAQvM,CAAI,EAChDoN,GAAqBxE,EAAgB,SAAWiE,EAAyB,UAC3ErL,EAAU6L,CAAO,GACjBd,EAAW,UAAYc,EAY3B,GAXIf,EAAS,YAAc9D,GACvBmE,EAAqBK,EAAS,IAAMkB,EAAalO,EAAMwI,CAAK,CAAC,EAC7DmE,EAAmBL,EAAS,UAAU,IAGtC,aAAaM,CAAK,EAClBD,EAAqB,KACrBnE,EACM/G,EAAI8K,EAAW,OAAQvM,EAAMwI,CAAK,EAClC/C,EAAM8G,EAAW,OAAQvM,CAAI,IAElCwI,EAAQ,CAACrF,GAAU6L,EAAoBxG,CAAK,EAAIwG,IACjD,CAACrK,EAAcH,CAAU,GACzB4I,EAAmB,CACnB,MAAM6B,EAAmB,CACrB,GAAGzK,EACH,GAAI4I,GAAqB5L,EAAU6L,CAAO,EAAI,CAAE,QAAAA,CAAO,EAAK,GAC5D,OAAQd,EAAW,OACnB,KAAAvM,CAChB,EACYuM,EAAa,CACT,GAAGA,EACH,GAAG0C,CACnB,EACYnC,EAAU,MAAM,KAAKmC,CAAgB,CACzC,CACJ,EACM3B,EAAa,MAAOtN,GAAS,CAC/BwN,EAAoBxN,EAAM,EAAI,EAC9B,MAAMuB,EAAS,MAAM+K,EAAS,SAASG,EAAaH,EAAS,QAAStF,GAAmBhH,GAAQ8C,EAAO,MAAOoE,EAASoF,EAAS,aAAcA,EAAS,yBAAyB,CAAC,EAClL,OAAAkB,EAAoBxN,CAAI,EACjBuB,CACX,EACM2N,GAA8B,MAAOhP,GAAU,CACjD,KAAM,CAAE,OAAA2D,CAAM,EAAK,MAAMyJ,EAAWpN,CAAK,EACzC,GAAIA,EACA,UAAWF,KAAQE,EAAO,CACtB,MAAMsI,EAAQrH,EAAI0C,EAAQ7D,CAAI,EAC9BwI,EACM/G,EAAI8K,EAAW,OAAQvM,EAAMwI,CAAK,EAClC/C,EAAM8G,EAAW,OAAQvM,CAAI,CACvC,MAGAuM,EAAW,OAAS1I,EAExB,OAAOA,CACX,EACM0J,EAA2B,MAAOxH,EAAQoJ,EAAsBC,EAAU,CAC5E,MAAO,EACf,IAAU,CACF,UAAWpP,KAAQ+F,EAAQ,CACvB,MAAMsB,EAAQtB,EAAO/F,CAAI,EACzB,GAAIqH,EAAO,CACP,KAAM,CAAE,GAAAN,EAAI,GAAGtC,CAAU,EAAK4C,EAC9B,GAAIN,EAAI,CACJ,MAAMsI,EAAmBvM,EAAO,MAAM,IAAIiE,EAAG,IAAI,EAC3CuI,EAAoBjI,EAAM,IAAMO,GAAqBP,EAAM,EAAE,EAC/DiI,GAAqB1G,EAAgB,kBACrC4E,EAAoB,CAACzG,EAAG,IAAI,EAAG,EAAI,EAEvC,MAAMwI,EAAa,MAAM3F,GAAcvC,EAAOvE,EAAO,SAAU2J,EAAaM,GAAkCT,EAAS,2BAA6B,CAAC6C,EAAsBE,CAAgB,EAI3L,GAHIC,GAAqB1G,EAAgB,kBACrC4E,EAAoB,CAACzG,EAAG,IAAI,CAAC,EAE7BwI,EAAWxI,EAAG,IAAI,IAClBqI,EAAQ,MAAQ,GACZD,GACA,MAGR,CAACA,IACIhO,EAAIoO,EAAYxI,EAAG,IAAI,EAClBsI,EACI9F,GAA0BgD,EAAW,OAAQgD,EAAYxI,EAAG,IAAI,EAChEtF,EAAI8K,EAAW,OAAQxF,EAAG,KAAMwI,EAAWxI,EAAG,IAAI,CAAC,EACvDtB,EAAM8G,EAAW,OAAQxF,EAAG,IAAI,EAC9C,CACA,CAACpC,EAAcF,CAAU,GACpB,MAAM8I,EAAyB9I,EAAY0K,EAAsBC,CAAO,CACjF,CACJ,CACA,OAAOA,EAAQ,KACnB,EACMI,GAAmB,IAAM,CAC3B,UAAWxP,KAAQ8C,EAAO,QAAS,CAC/B,MAAMuE,EAAQlG,EAAI+F,EAASlH,CAAI,EAC/BqH,IACKA,EAAM,GAAG,KACJA,EAAM,GAAG,KAAK,MAAOlC,GAAQ,CAACC,GAAKD,CAAG,CAAC,EACvC,CAACC,GAAKiC,EAAM,GAAG,GAAG,IACxBoI,GAAWzP,CAAI,CACvB,CACA8C,EAAO,QAAU,IAAI,GACzB,EACMmL,EAAY,CAACjO,EAAMQ,IAAS,CAAC8L,EAAS,WACvCtM,GAAQQ,GAAQiB,EAAIgL,EAAazM,EAAMQ,CAAI,EACxC,CAAC2C,GAAUuM,KAAalD,CAAc,GACxCmD,EAAY,CAACzP,EAAOoB,EAAc0B,IAAaH,GAAoB3C,EAAO4C,EAAQ,CACpF,GAAI4J,EAAO,MACLD,EACA3L,EAAYQ,CAAY,EACpBkL,EACA5J,EAAS1C,CAAK,EACV,CAAE,CAACA,CAAK,EAAGoB,CAAY,EACvBA,CACtB,EAAO0B,EAAU1B,CAAY,EACnBsO,EAAkB5P,GAASgB,GAAQG,EAAIuL,EAAO,MAAQD,EAAcD,EAAgBxM,EAAMsM,EAAS,iBAAmBnL,EAAIqL,EAAgBxM,EAAM,CAAA,CAAE,EAAI,CAAA,CAAE,CAAC,EACzJsO,EAAgB,CAACtO,EAAMP,EAAO4G,EAAU,CAAA,IAAO,CACjD,MAAMgB,EAAQlG,EAAI+F,EAASlH,CAAI,EAC/B,IAAIyE,EAAahF,EACjB,GAAI4H,EAAO,CACP,MAAMQ,EAAiBR,EAAM,GACzBQ,IACA,CAACA,EAAe,UACZpG,EAAIgL,EAAazM,EAAMuG,GAAgB9G,EAAOoI,CAAc,CAAC,EACjEpD,EACIK,GAAc+C,EAAe,GAAG,GAAKnI,EAAkBD,CAAK,EACtD,GACAA,EACNuF,GAAiB6C,EAAe,GAAG,EACnC,CAAC,GAAGA,EAAe,IAAI,OAAO,EAAE,QAASgI,GAAeA,EAAU,SAAWpL,EAAW,SAASoL,EAAU,KAAK,CAAE,EAE7GhI,EAAe,KAChBvI,GAAgBuI,EAAe,GAAG,EAClCA,EAAe,KAAK,QAASiI,GAAgB,EACrC,CAACA,EAAY,gBAAkB,CAACA,EAAY,YACxC,MAAM,QAAQrL,CAAU,EACxBqL,EAAY,QAAU,CAAC,CAACrL,EAAW,KAAMjE,GAASA,IAASsP,EAAY,KAAK,EAG5EA,EAAY,QACRrL,IAAeqL,EAAY,OAAS,CAAC,CAACrL,EAGtD,CAAC,EAGDoD,EAAe,KAAK,QAASkI,GAAcA,EAAS,QAAUA,EAAS,QAAUtL,CAAW,EAG3FG,GAAYiD,EAAe,GAAG,EACnCA,EAAe,IAAI,MAAQ,IAG3BA,EAAe,IAAI,MAAQpD,EACtBoD,EAAe,IAAI,MACpBiF,EAAU,MAAM,KAAK,CACjB,KAAA9M,EACA,OAAQO,EAAYkM,CAAW,CAC3D,CAAyB,GAIjB,EACCpG,EAAQ,aAAeA,EAAQ,cAC5BkI,EAAoBvO,EAAMyE,EAAY4B,EAAQ,YAAaA,EAAQ,YAAa,EAAI,EACxFA,EAAQ,gBAAkB2J,GAAQhQ,CAAI,CAC1C,EACMiQ,EAAY,CAACjQ,EAAMP,EAAO4G,IAAY,CACxC,UAAW6J,KAAYzQ,EAAO,CAC1B,GAAI,CAACA,EAAM,eAAeyQ,CAAQ,EAC9B,OAEJ,MAAMzL,EAAahF,EAAMyQ,CAAQ,EAC3BjN,EAAYjD,EAAO,IAAMkQ,EACzB7I,EAAQlG,EAAI+F,EAASjE,CAAS,GACnCH,EAAO,MAAM,IAAI9C,CAAI,GAClBJ,EAAS6E,CAAU,GAClB4C,GAAS,CAACA,EAAM,KACjB,CAAC7H,GAAaiF,CAAU,EACtBwL,EAAUhN,EAAWwB,EAAY4B,CAAO,EACxCiI,EAAcrL,EAAWwB,EAAY4B,CAAO,CACtD,CACJ,EACM8J,EAAW,CAACnQ,EAAMP,EAAO4G,EAAU,CAAA,IAAO,CAC5C,MAAMgB,EAAQlG,EAAI+F,EAASlH,CAAI,EACzB8J,EAAehH,EAAO,MAAM,IAAI9C,CAAI,EACpCoQ,EAAa7P,EAAYd,CAAK,EACpCgC,EAAIgL,EAAazM,EAAMoQ,CAAU,EAC7BtG,GACAgD,EAAU,MAAM,KAAK,CACjB,KAAA9M,EACA,OAAQO,EAAYkM,CAAW,CAC/C,CAAa,GACI7D,EAAgB,SACjBA,EAAgB,aAChBiE,EAAyB,SACzBA,EAAyB,cACzBxG,EAAQ,aACRyG,EAAU,MAAM,KAAK,CACjB,KAAA9M,EACA,YAAagG,GAAewG,EAAgBC,CAAW,EACvD,QAASwB,EAAUjO,EAAMoQ,CAAU,CACvD,CAAiB,GAIL/I,GAAS,CAACA,EAAM,IAAM,CAAC3H,EAAkB0Q,CAAU,EAC7CH,EAAUjQ,EAAMoQ,EAAY/J,CAAO,EACnCiI,EAActO,EAAMoQ,EAAY/J,CAAO,EAEjD2B,GAAUhI,EAAM8C,CAAM,GAAKgK,EAAU,MAAM,KAAK,CAAE,GAAGP,EAAY,KAAAvM,EAAM,EACvE8M,EAAU,MAAM,KAAK,CACjB,KAAMJ,EAAO,MAAQ1M,EAAO,OAC5B,OAAQO,EAAYkM,CAAW,CAC3C,CAAS,CACL,EACM4D,EAAW,MAAOvQ,GAAU,CAC9B4M,EAAO,MAAQ,GACf,MAAM4D,EAASxQ,EAAM,OACrB,IAAIE,EAAOsQ,EAAO,KACdC,EAAsB,GAC1B,MAAMlJ,EAAQlG,EAAI+F,EAASlH,CAAI,EACzBwQ,EAA8B/L,GAAe,CAC/C8L,EACI,OAAO,MAAM9L,CAAU,GAClBjF,GAAaiF,CAAU,GAAK,MAAMA,EAAW,QAAO,CAAE,GACvDtB,GAAUsB,EAAYtD,EAAIsL,EAAazM,EAAMyE,CAAU,CAAC,CACpE,EACMgM,EAA6BhJ,GAAmB6E,EAAS,IAAI,EAC7DoE,EAA4BjJ,GAAmB6E,EAAS,cAAc,EAC5E,GAAIjF,EAAO,CACP,IAAImB,EACA6E,EACJ,MAAM5I,GAAa6L,EAAO,KACpBxJ,GAAcO,EAAM,EAAE,EACtBxH,GAAcC,CAAK,EACnBmI,EAAcnI,EAAM,OAASkC,GAAO,MAAQlC,EAAM,OAASkC,GAAO,UAClE2O,GAAwB,CAAC5I,GAAcV,EAAM,EAAE,GACjD,CAACiF,EAAS,UACV,CAACnL,EAAIoL,EAAW,OAAQvM,CAAI,GAC5B,CAACqH,EAAM,GAAG,MACV6B,GAAejB,EAAa9G,EAAIoL,EAAW,cAAevM,CAAI,EAAGuM,EAAW,YAAamE,EAA2BD,CAA0B,EAC5IG,GAAU5I,GAAUhI,EAAM8C,EAAQmF,CAAW,EACnDxG,EAAIgL,EAAazM,EAAMyE,EAAU,EAC7BwD,GACI,CAACqI,GAAU,CAACA,EAAO,YACnBjJ,EAAM,GAAG,QAAUA,EAAM,GAAG,OAAOvH,CAAK,EACxC6M,GAAsBA,EAAmB,CAAC,GAGzCtF,EAAM,GAAG,UACdA,EAAM,GAAG,SAASvH,CAAK,EAE3B,MAAM0E,GAAa+J,EAAoBvO,EAAMyE,GAAYwD,CAAW,EAC9DwG,GAAe,CAAC9J,EAAcH,EAAU,GAAKoM,GAOnD,GANA,CAAC3I,GACG6E,EAAU,MAAM,KAAK,CACjB,KAAA9M,EACA,KAAMF,EAAM,KACZ,OAAQS,EAAYkM,CAAW,CACnD,CAAiB,EACDkE,GACA,OAAI/H,EAAgB,SAAWiE,EAAyB,WAChDP,EAAS,OAAS,SACdrE,GACAkF,EAAS,EAGPlF,GACNkF,EAAS,GAGTsB,IACJ3B,EAAU,MAAM,KAAK,CAAE,KAAA9M,EAAM,GAAI4Q,GAAU,CAAA,EAAKpM,GAAa,EAGrE,GADA,CAACyD,GAAe2I,IAAW9D,EAAU,MAAM,KAAK,CAAE,GAAGP,EAAY,EAC7DD,EAAS,SAAU,CACnB,KAAM,CAAE,OAAAzI,EAAM,EAAK,MAAMyJ,EAAW,CAACtN,CAAI,CAAC,EAE1C,GADAwQ,EAA2B/L,EAAU,EACjC8L,EAAqB,CACrB,MAAMM,GAA4BtI,GAAkBgE,EAAW,OAAQrF,EAASlH,CAAI,EAC9E8Q,GAAoBvI,GAAkB1E,GAAQqD,EAAS2J,GAA0B,MAAQ7Q,CAAI,EACnGwI,EAAQsI,GAAkB,MAC1B9Q,EAAO8Q,GAAkB,KACzBzD,EAAU1I,EAAcd,EAAM,CAClC,CACJ,MAEI2J,EAAoB,CAACxN,CAAI,EAAG,EAAI,EAChCwI,GAAS,MAAMoB,GAAcvC,EAAOvE,EAAO,SAAU2J,EAAaM,GAAkCT,EAAS,yBAAyB,GAAGtM,CAAI,EAC7IwN,EAAoB,CAACxN,CAAI,CAAC,EAC1BwQ,EAA2B/L,EAAU,EACjC8L,IACI/H,EACA6E,EAAU,IAELzE,EAAgB,SACrBiE,EAAyB,WACzBQ,EAAU,MAAME,EAAyBrG,EAAS,EAAI,IAI9DqJ,IACAlJ,EAAM,GAAG,OACJ,CAAC,MAAM,QAAQA,EAAM,GAAG,IAAI,GAAKA,EAAM,GAAG,KAAK,OAAS,IACzD2I,GAAQ3I,EAAM,GAAG,IAAI,EACzB0H,GAAoB/O,EAAMqN,EAAS7E,EAAOhE,EAAU,EAE5D,CACJ,EACMuM,GAAc,CAAC5L,EAAKvE,IAAQ,CAC9B,GAAIO,EAAIoL,EAAW,OAAQ3L,CAAG,GAAKuE,EAAI,MACnC,OAAAA,EAAI,MAAK,EACF,CAGf,EACM6K,GAAU,MAAOhQ,EAAMqG,EAAU,CAAA,IAAO,CAC1C,IAAIgH,EACAnB,EACJ,MAAM8E,EAAahN,GAAsBhE,CAAI,EAC7C,GAAIsM,EAAS,SAAU,CACnB,MAAMzI,EAAS,MAAMqL,GAA4BpO,EAAYd,CAAI,EAAIA,EAAOgR,CAAU,EACtF3D,EAAU1I,EAAcd,CAAM,EAC9BqI,EAAmBlM,EACb,CAACgR,EAAW,KAAMhR,GAASmB,EAAI0C,EAAQ7D,CAAI,CAAC,EAC5CqN,CACV,MACSrN,GACLkM,GAAoB,MAAM,QAAQ,IAAI8E,EAAW,IAAI,MAAO/N,GAAc,CACtE,MAAMoE,EAAQlG,EAAI+F,EAASjE,CAAS,EACpC,OAAO,MAAMsK,EAAyBlG,GAASA,EAAM,GAAK,CAAE,CAACpE,CAAS,EAAGoE,CAAK,EAAKA,CAAK,CAC5F,CAAC,CAAC,GAAG,MAAM,OAAO,EAClB,EAAE,CAAC6E,GAAoB,CAACK,EAAW,UAAYY,EAAS,GAGxDjB,EAAmBmB,EAAU,MAAME,EAAyBrG,CAAO,EAEvE,OAAA4F,EAAU,MAAM,KAAK,CACjB,GAAI,CAAClK,EAAS5C,CAAI,IACZ4I,EAAgB,SAAWiE,EAAyB,UAClDQ,IAAYd,EAAW,QACzB,CAAA,EACA,CAAE,KAAAvM,CAAI,EACZ,GAAIsM,EAAS,UAAY,CAACtM,EAAO,CAAE,QAAAqN,CAAO,EAAK,GAC/C,OAAQd,EAAW,MAC/B,CAAS,EACDlG,EAAQ,aACJ,CAAC6F,GACD/D,GAAsBjB,EAAS6J,GAAa/Q,EAAOgR,EAAalO,EAAO,KAAK,EACzEoJ,CACX,EACMwD,GAAY,CAACsB,EAAYC,IAAW,CACtC,IAAI1M,EAAS,CACT,GAAImI,EAAO,MAAQD,EAAcD,CAC7C,EACQ,OAAIyE,IACA1M,EAASF,GAAkB4M,EAAO,YAAc1E,EAAW,YAAcA,EAAW,cAAehI,CAAM,GAEtGzD,EAAYkQ,CAAU,EACvBzM,EACA3B,EAASoO,CAAU,EACf7P,EAAIoD,EAAQyM,CAAU,EACtBA,EAAW,IAAKhR,GAASmB,EAAIoD,EAAQvE,CAAI,CAAC,CACxD,EACMkR,GAAgB,CAAClR,EAAMsC,KAAe,CACxC,QAAS,CAAC,CAACnB,GAAKmB,GAAaiK,GAAY,OAAQvM,CAAI,EACrD,QAAS,CAAC,CAACmB,GAAKmB,GAAaiK,GAAY,YAAavM,CAAI,EAC1D,MAAOmB,GAAKmB,GAAaiK,GAAY,OAAQvM,CAAI,EACjD,aAAc,CAAC,CAACmB,EAAIoL,EAAW,iBAAkBvM,CAAI,EACrD,UAAW,CAAC,CAACmB,GAAKmB,GAAaiK,GAAY,cAAevM,CAAI,CACtE,GACUmR,GAAenR,GAAS,CAC1BA,GACIgE,GAAsBhE,CAAI,EAAE,QAASoR,GAAc3L,EAAM8G,EAAW,OAAQ6E,CAAS,CAAC,EAC1FtE,EAAU,MAAM,KAAK,CACjB,OAAQ9M,EAAOuM,EAAW,OAAS,CAAA,CAC/C,CAAS,CACL,EACM8E,GAAW,CAACrR,EAAMwI,EAAOnC,IAAY,CACvC,MAAMlB,GAAOhE,EAAI+F,EAASlH,EAAM,CAAE,GAAI,EAAE,CAAE,EAAE,IAAM,CAAA,GAAI,IAChDsR,EAAenQ,EAAIoL,EAAW,OAAQvM,CAAI,GAAK,CAAA,EAE/C,CAAE,IAAKuR,EAAY,QAAAxN,EAAS,KAAAD,EAAM,GAAG0N,CAAe,EAAKF,EAC/D7P,EAAI8K,EAAW,OAAQvM,EAAM,CACzB,GAAGwR,EACH,GAAGhJ,EACH,IAAArD,CACZ,CAAS,EACD2H,EAAU,MAAM,KAAK,CACjB,KAAA9M,EACA,OAAQuM,EAAW,OACnB,QAAS,EACrB,CAAS,EACDlG,GAAWA,EAAQ,aAAelB,GAAOA,EAAI,OAASA,EAAI,MAAK,CACnE,EACMsM,GAAQ,CAACzR,EAAMsB,IAAiBuD,EAAW7E,CAAI,EAC/C8M,EAAU,MAAM,UAAU,CACxB,KAAO4E,GAAY,WAAYA,GAC3B1R,EAAK2P,EAAU,OAAWrO,CAAY,EAAGoQ,CAAO,CAChE,CAAS,EACC/B,EAAU3P,EAAMsB,EAAc,EAAI,EAClCqQ,GAActF,GAAUS,EAAU,MAAM,UAAU,CACpD,KAAOxK,GAAc,CACbwG,GAAsBuD,EAAM,KAAM/J,EAAU,KAAM+J,EAAM,KAAK,GAC7D3D,GAAsBpG,EAAW+J,EAAM,WAAazD,EAAiBgJ,GAAevF,EAAM,YAAY,GACtGA,EAAM,SAAS,CACX,OAAQ,CAAE,GAAGI,CAAW,EACxB,GAAGF,EACH,GAAGjK,EACH,cAAekK,CACnC,CAAiB,CAET,CACR,CAAK,EAAE,YACGqF,GAAaxF,IACfK,EAAO,MAAQ,GACfG,EAA2B,CACvB,GAAGA,EACH,GAAGR,EAAM,SACrB,EACesF,GAAW,CACd,GAAGtF,EACH,UAAWQ,CACvB,CAAS,GAEC4C,GAAa,CAACzP,EAAMqG,EAAU,CAAA,IAAO,CACvC,UAAWpD,KAAajD,EAAOgE,GAAsBhE,CAAI,EAAI8C,EAAO,MAChEA,EAAO,MAAM,OAAOG,CAAS,EAC7BH,EAAO,MAAM,OAAOG,CAAS,EACxBoD,EAAQ,YACTZ,EAAMyB,EAASjE,CAAS,EACxBwC,EAAMgH,EAAaxJ,CAAS,GAEhC,CAACoD,EAAQ,WAAaZ,EAAM8G,EAAW,OAAQtJ,CAAS,EACxD,CAACoD,EAAQ,WAAaZ,EAAM8G,EAAW,YAAatJ,CAAS,EAC7D,CAACoD,EAAQ,aAAeZ,EAAM8G,EAAW,cAAetJ,CAAS,EACjE,CAACoD,EAAQ,kBACLZ,EAAM8G,EAAW,iBAAkBtJ,CAAS,EAChD,CAACqJ,EAAS,kBACN,CAACjG,EAAQ,kBACTZ,EAAM+G,EAAgBvJ,CAAS,EAEvC6J,EAAU,MAAM,KAAK,CACjB,OAAQvM,EAAYkM,CAAW,CAC3C,CAAS,EACDK,EAAU,MAAM,KAAK,CACjB,GAAGP,EACH,GAAKlG,EAAQ,UAAiB,CAAE,QAAS4H,EAAS,GAAzB,CAAA,CACrC,CAAS,EACD,CAAC5H,EAAQ,aAAe8G,EAAS,CACrC,EACM2E,GAAoB,CAAC,CAAE,SAAAC,EAAU,KAAA/R,CAAI,IAAQ,EAC1CwB,EAAUuQ,CAAQ,GAAKrF,EAAO,OAC7BqF,GACFjP,EAAO,SAAS,IAAI9C,CAAI,KACxB+R,EAAWjP,EAAO,SAAS,IAAI9C,CAAI,EAAI8C,EAAO,SAAS,OAAO9C,CAAI,EAE1E,EACMgS,GAAW,CAAChS,EAAMqG,EAAU,CAAA,IAAO,CACrC,IAAIgB,EAAQlG,EAAI+F,EAASlH,CAAI,EAC7B,MAAMiS,EAAoBzQ,EAAU6E,EAAQ,QAAQ,GAAK7E,EAAU8K,EAAS,QAAQ,EACpF,OAAA7K,EAAIyF,EAASlH,EAAM,CACf,GAAIqH,GAAS,CAAA,EACb,GAAI,CACA,GAAIA,GAASA,EAAM,GAAKA,EAAM,GAAK,CAAE,IAAK,CAAE,KAAArH,CAAI,GAChD,KAAAA,EACA,MAAO,GACP,GAAGqG,CACnB,CACA,CAAS,EACDvD,EAAO,MAAM,IAAI9C,CAAI,EACjBqH,EACAyK,GAAkB,CACd,SAAUtQ,EAAU6E,EAAQ,QAAQ,EAC9BA,EAAQ,SACRiG,EAAS,SACf,KAAAtM,CAChB,CAAa,EAGDoO,EAAoBpO,EAAM,GAAMqG,EAAQ,KAAK,EAE1C,CACH,GAAI4L,EACE,CAAE,SAAU5L,EAAQ,UAAYiG,EAAS,QAAQ,EACjD,GACN,GAAIA,EAAS,YACP,CACE,SAAU,CAAC,CAACjG,EAAQ,SACpB,IAAKkB,GAAalB,EAAQ,GAAG,EAC7B,IAAKkB,GAAalB,EAAQ,GAAG,EAC7B,UAAWkB,GAAalB,EAAQ,SAAS,EACzC,UAAWkB,GAAalB,EAAQ,SAAS,EACzC,QAASkB,GAAalB,EAAQ,OAAO,CACzD,EACkB,GACN,KAAArG,EACA,SAAAqQ,EACA,OAAQA,EACR,IAAMlL,GAAQ,CACV,GAAIA,EAAK,CACL6M,GAAShS,EAAMqG,CAAO,EACtBgB,EAAQlG,EAAI+F,EAASlH,CAAI,EACzB,MAAMkS,EAAWpR,EAAYqE,EAAI,KAAK,GAChCA,EAAI,kBACAA,EAAI,iBAAiB,uBAAuB,EAAE,CAAC,GAAKA,EAGxDgN,EAAkBjN,GAAkBgN,CAAQ,EAC5CnI,EAAO1C,EAAM,GAAG,MAAQ,CAAA,EAC9B,GAAI8K,EACEpI,EAAK,KAAMzD,GAAWA,IAAW4L,CAAQ,EACzCA,IAAa7K,EAAM,GAAG,IACxB,OAEJ5F,EAAIyF,EAASlH,EAAM,CACf,GAAI,CACA,GAAGqH,EAAM,GACT,GAAI8K,EACE,CACE,KAAM,CACF,GAAGpI,EAAK,OAAO3E,EAAI,EACnB8M,EACA,GAAI,MAAM,QAAQ/Q,EAAIqL,EAAgBxM,CAAI,CAAC,EAAI,CAAC,EAAE,EAAI,EAC9F,EACoC,IAAK,CAAE,KAAMkS,EAAS,KAAM,KAAAlS,CAAI,CACpE,EACkC,CAAE,IAAKkS,EACzC,CACA,CAAqB,EACD9D,EAAoBpO,EAAM,GAAO,OAAWkS,CAAQ,CACxD,MAEI7K,EAAQlG,EAAI+F,EAASlH,EAAM,CAAA,CAAE,EACzBqH,EAAM,KACNA,EAAM,GAAG,MAAQ,KAEpBiF,EAAS,kBAAoBjG,EAAQ,mBAClC,EAAEpG,GAAmB6C,EAAO,MAAO9C,CAAI,GAAK0M,EAAO,SACnD5J,EAAO,QAAQ,IAAI9C,CAAI,CAEnC,CACZ,CACI,EACMoS,GAAc,IAAM9F,EAAS,kBAC/BnE,GAAsBjB,EAAS6J,GAAajO,EAAO,KAAK,EACtDuP,GAAgBN,GAAa,CAC3BvQ,EAAUuQ,CAAQ,IAClBjF,EAAU,MAAM,KAAK,CAAE,SAAAiF,CAAQ,CAAE,EACjC5J,GAAsBjB,EAAS,CAAC/B,EAAKnF,IAAS,CAC1C,MAAMsI,EAAenH,EAAI+F,EAASlH,CAAI,EAClCsI,IACAnD,EAAI,SAAWmD,EAAa,GAAG,UAAYyJ,EACvC,MAAM,QAAQzJ,EAAa,GAAG,IAAI,GAClCA,EAAa,GAAG,KAAK,QAASmC,GAAa,CACvCA,EAAS,SAAWnC,EAAa,GAAG,UAAYyJ,CACpD,CAAC,EAGb,EAAG,EAAG,EAAK,EAEnB,EACMO,GAAe,CAACC,EAASC,IAAc,MAAOC,GAAM,CACtD,IAAIC,EACAD,IACAA,EAAE,gBAAkBA,EAAE,eAAc,EACpCA,EAAE,SACEA,EAAE,QAAO,GAEjB,IAAI1E,EAAcxN,EAAYkM,CAAW,EAIzC,GAHAK,EAAU,MAAM,KAAK,CACjB,aAAc,EAC1B,CAAS,EACGR,EAAS,SAAU,CACnB,KAAM,CAAE,OAAAzI,EAAQ,OAAAU,CAAM,EAAK,MAAM+I,EAAU,EAC3Cf,EAAW,OAAS1I,EACpBkK,EAAcxN,EAAYgE,CAAM,CACpC,MAEI,MAAMgJ,EAAyBrG,CAAO,EAE1C,GAAIpE,EAAO,SAAS,KAChB,UAAW9C,KAAQ8C,EAAO,SACtB2C,EAAMsI,EAAa/N,CAAI,EAI/B,GADAyF,EAAM8G,EAAW,OAAQ,MAAM,EAC3B5H,EAAc4H,EAAW,MAAM,EAAG,CAClCO,EAAU,MAAM,KAAK,CACjB,OAAQ,CAAA,CACxB,CAAa,EACD,GAAI,CACA,MAAMyF,EAAQxE,EAAa0E,CAAC,CAChC,OACOjK,EAAO,CACVkK,EAAelK,CACnB,CACJ,MAEQgK,GACA,MAAMA,EAAU,CAAE,GAAGjG,EAAW,MAAM,EAAIkG,CAAC,EAE/CL,GAAW,EACX,WAAWA,EAAW,EAS1B,GAPAtF,EAAU,MAAM,KAAK,CACjB,YAAa,GACb,aAAc,GACd,mBAAoBnI,EAAc4H,EAAW,MAAM,GAAK,CAACmG,EACzD,YAAanG,EAAW,YAAc,EACtC,OAAQA,EAAW,MAC/B,CAAS,EACGmG,EACA,MAAMA,CAEd,EACMC,GAAa,CAAC3S,EAAMqG,EAAU,CAAA,IAAO,CACnClF,EAAI+F,EAASlH,CAAI,IACbc,EAAYuF,EAAQ,YAAY,EAChC8J,EAASnQ,EAAMO,EAAYY,EAAIqL,EAAgBxM,CAAI,CAAC,CAAC,GAGrDmQ,EAASnQ,EAAMqG,EAAQ,YAAY,EACnC5E,EAAI+K,EAAgBxM,EAAMO,EAAY8F,EAAQ,YAAY,CAAC,GAE1DA,EAAQ,aACTZ,EAAM8G,EAAW,cAAevM,CAAI,EAEnCqG,EAAQ,YACTZ,EAAM8G,EAAW,YAAavM,CAAI,EAClCuM,EAAW,QAAUlG,EAAQ,aACvB4H,EAAUjO,EAAMO,EAAYY,EAAIqL,EAAgBxM,CAAI,CAAC,CAAC,EACtDiO,EAAS,GAEd5H,EAAQ,YACTZ,EAAM8G,EAAW,OAAQvM,CAAI,EAC7B4I,EAAgB,SAAWuE,EAAS,GAExCL,EAAU,MAAM,KAAK,CAAE,GAAGP,CAAU,CAAE,EAE9C,EACMqG,GAAS,CAAC7P,EAAY8P,EAAmB,CAAA,IAAO,CAClD,MAAMC,EAAgB/P,EAAaxC,EAAYwC,CAAU,EAAIyJ,EACvDuG,EAAqBxS,EAAYuS,CAAa,EAC9CE,EAAqBrO,EAAc5B,CAAU,EAC7CwB,EAASyO,EAAqBxG,EAAiBuG,EAIrD,GAHKF,EAAiB,oBAClBrG,EAAiBsG,GAEjB,CAACD,EAAiB,WAAY,CAC9B,GAAIA,EAAiB,gBAAiB,CAClC,MAAMI,EAAgB,IAAI,IAAI,CAC1B,GAAGnQ,EAAO,MACV,GAAG,OAAO,KAAKkD,GAAewG,EAAgBC,CAAW,CAAC,CAC9E,CAAiB,EACD,UAAWxJ,KAAa,MAAM,KAAKgQ,CAAa,EAC5C9R,EAAIoL,EAAW,YAAatJ,CAAS,EAC/BxB,EAAI8C,EAAQtB,EAAW9B,EAAIsL,EAAaxJ,CAAS,CAAC,EAClDkN,EAASlN,EAAW9B,EAAIoD,EAAQtB,CAAS,CAAC,CAExD,KACK,CACD,GAAI3C,IAASQ,EAAYiC,CAAU,EAC/B,UAAW/C,KAAQ8C,EAAO,MAAO,CAC7B,MAAMuE,EAAQlG,EAAI+F,EAASlH,CAAI,EAC/B,GAAIqH,GAASA,EAAM,GAAI,CACnB,MAAMQ,EAAiB,MAAM,QAAQR,EAAM,GAAG,IAAI,EAC5CA,EAAM,GAAG,KAAK,CAAC,EACfA,EAAM,GAAG,IACf,GAAIvC,GAAc+C,CAAc,EAAG,CAC/B,MAAMqL,EAAOrL,EAAe,QAAQ,MAAM,EAC1C,GAAIqL,EAAM,CACNA,EAAK,MAAK,EACV,KACJ,CACJ,CACJ,CACJ,CAEJ,GAAIL,EAAiB,cACjB,UAAW5P,KAAaH,EAAO,MAC3BqN,EAASlN,EAAW9B,EAAIoD,EAAQtB,CAAS,CAAC,OAI9CiE,EAAU,CAAA,CAElB,CACAuF,EAAcH,EAAS,iBACjBuG,EAAiB,kBACbtS,EAAYiM,CAAc,EAC1B,CAAA,EACJjM,EAAYgE,CAAM,EACxBuI,EAAU,MAAM,KAAK,CACjB,OAAQ,CAAE,GAAGvI,CAAM,CACnC,CAAa,EACDuI,EAAU,MAAM,KAAK,CACjB,OAAQ,CAAE,GAAGvI,CAAM,CACnC,CAAa,CACL,CACAzB,EAAS,CACL,MAAO+P,EAAiB,gBAAkB/P,EAAO,MAAQ,IAAI,IAC7D,QAAS,IAAI,IACb,MAAO,IAAI,IACX,SAAU,IAAI,IACd,MAAO,IAAI,IACX,SAAU,GACV,MAAO,EACnB,EACQ4J,EAAO,MACH,CAAC9D,EAAgB,SACb,CAAC,CAACiK,EAAiB,aACnB,CAAC,CAACA,EAAiB,gBAC3BnG,EAAO,MAAQ,CAAC,CAACJ,EAAS,iBAC1BQ,EAAU,MAAM,KAAK,CACjB,YAAa+F,EAAiB,gBACxBtG,EAAW,YACX,EACN,QAASyG,EACH,GACAH,EAAiB,UACbtG,EAAW,QACX,CAAC,EAAEsG,EAAiB,mBAClB,CAAC1P,GAAUJ,EAAYyJ,CAAc,GACjD,YAAaqG,EAAiB,gBACxBtG,EAAW,YACX,GACN,YAAayG,EACP,CAAA,EACAH,EAAiB,gBACbA,EAAiB,mBAAqBpG,EAClCzG,GAAewG,EAAgBC,CAAW,EAC1CF,EAAW,YACfsG,EAAiB,mBAAqB9P,EAClCiD,GAAewG,EAAgBzJ,CAAU,EACzC8P,EAAiB,UACbtG,EAAW,YACX,CAAA,EAClB,cAAesG,EAAiB,YAC1BtG,EAAW,cACX,CAAA,EACN,OAAQsG,EAAiB,WAAatG,EAAW,OAAS,CAAA,EAC1D,mBAAoBsG,EAAiB,uBAC/BtG,EAAW,mBACX,GACN,aAAc,GACd,cAAeC,CAC3B,CAAS,CACL,EACM2G,GAAQ,CAACpQ,EAAY8P,IAAqBD,GAAO/N,EAAW9B,CAAU,EACtEA,EAAW0J,CAAW,EACtB1J,EAAY8P,CAAgB,EAC5BO,GAAW,CAACpT,EAAMqG,EAAU,CAAA,IAAO,CACrC,MAAMgB,EAAQlG,EAAI+F,EAASlH,CAAI,EACzB6H,EAAiBR,GAASA,EAAM,GACtC,GAAIQ,EAAgB,CAChB,MAAMqK,EAAWrK,EAAe,KAC1BA,EAAe,KAAK,CAAC,EACrBA,EAAe,IACjBqK,EAAS,QACTA,EAAS,MAAK,EACd7L,EAAQ,cACJxB,EAAWqN,EAAS,MAAM,GAC1BA,EAAS,OAAM,EAE3B,CACJ,EACMN,GAAiB3C,GAAqB,CACxC1C,EAAa,CACT,GAAGA,EACH,GAAG0C,CACf,CACI,EAQMoE,GAAU,CACZ,QAAS,CACL,SAAArB,GACA,WAAAvC,GACA,cAAAyB,GACA,aAAAoB,GACA,SAAAjB,GACA,WAAAM,GACA,WAAArE,EACA,YAAA8E,GACA,UAAAzC,EACA,UAAA1B,EACA,UAAAd,EACA,eAAAO,GACA,kBAAAoE,GACA,WAAA3D,EACA,eAAAyB,EACA,OAAAgD,GACA,oBAzBoB,IAAM/N,EAAWyH,EAAS,aAAa,GAC/DA,EAAS,cAAa,EAAG,KAAM/H,GAAW,CACtC4O,GAAM5O,EAAQ+H,EAAS,YAAY,EACnCQ,EAAU,MAAM,KAAK,CACjB,UAAW,EAC3B,CAAa,CACL,CAAC,EAoBG,iBAAA0C,GACA,aAAA6C,GACA,UAAAvF,EACA,gBAAAlE,EACA,IAAI,SAAU,CACV,OAAO1B,CACX,EACA,IAAI,aAAc,CACd,OAAOuF,CACX,EACA,IAAI,QAAS,CACT,OAAOC,CACX,EACA,IAAI,OAAOjN,EAAO,CACdiN,EAASjN,CACb,EACA,IAAI,gBAAiB,CACjB,OAAO+M,CACX,EACA,IAAI,QAAS,CACT,OAAO1J,CACX,EACA,IAAI,OAAOrD,EAAO,CACdqD,EAASrD,CACb,EACA,IAAI,YAAa,CACb,OAAO8M,CACX,EACA,IAAI,UAAW,CACX,OAAOD,CACX,EACA,IAAI,SAAS7M,EAAO,CAChB6M,EAAW,CACP,GAAGA,EACH,GAAG7M,CACvB,CACY,CACZ,EACQ,UAAAoS,GACA,QAAA7B,GACA,SAAAgC,GACA,aAAAM,GACA,MAAAb,GACA,SAAAtB,EACA,UAAAT,GACA,MAAAyD,GACA,WAAAR,GACA,YAAAxB,GACA,WAAA1B,GACA,SAAA4B,GACA,SAAA+B,GACA,cAAAlC,EACR,EACI,MAAO,CACH,GAAGmC,GACH,YAAaA,EACrB,CACA,CAkVA,SAASC,GAAQjH,EAAQ,GAAI,CACzB,MAAMkH,EAAenR,EAAM,OAAO,MAAS,EACrCoR,EAAUpR,EAAM,OAAO,MAAS,EAChC,CAACE,EAAWuG,CAAe,EAAIzG,EAAM,SAAS,CAChD,QAAS,GACT,aAAc,GACd,UAAWyC,EAAWwH,EAAM,aAAa,EACzC,YAAa,GACb,aAAc,GACd,mBAAoB,GACpB,QAAS,GACT,YAAa,EACb,YAAa,CAAA,EACb,cAAe,CAAA,EACf,iBAAkB,CAAA,EAClB,OAAQA,EAAM,QAAU,CAAA,EACxB,SAAUA,EAAM,UAAY,GAC5B,QAAS,GACT,cAAexH,EAAWwH,EAAM,aAAa,EACvC,OACAA,EAAM,aACpB,CAAK,EACD,GAAI,CAACkH,EAAa,QACd,GAAIlH,EAAM,YACNkH,EAAa,QAAU,CACnB,GAAGlH,EAAM,YACT,UAAA/J,CAChB,EACgB+J,EAAM,eAAiB,CAACxH,EAAWwH,EAAM,aAAa,GACtDA,EAAM,YAAY,MAAMA,EAAM,cAAeA,EAAM,YAAY,MAGlE,CACD,KAAM,CAAE,YAAAoH,EAAa,GAAGC,CAAI,EAAKtH,GAAkBC,CAAK,EACxDkH,EAAa,QAAU,CACnB,GAAGG,EACH,UAAApR,CAChB,CACQ,CAEJ,MAAMC,EAAUgR,EAAa,QAAQ,QACrC,OAAAhR,EAAQ,SAAW8J,EACnB1J,GAA0B,IAAM,CAC5B,MAAMgR,EAAMpR,EAAQ,WAAW,CAC3B,UAAWA,EAAQ,gBACnB,SAAU,IAAMsG,EAAgB,CAAE,GAAGtG,EAAQ,UAAU,CAAE,EACzD,aAAc,EAC1B,CAAS,EACD,OAAAsG,EAAiBrI,IAAU,CACvB,GAAGA,EACH,QAAS,EACrB,EAAU,EACF+B,EAAQ,WAAW,QAAU,GACtBoR,CACX,EAAG,CAACpR,CAAO,CAAC,EACZH,EAAM,UAAU,IAAMG,EAAQ,aAAa8J,EAAM,QAAQ,EAAG,CAAC9J,EAAS8J,EAAM,QAAQ,CAAC,EACrFjK,EAAM,UAAU,IAAM,CACdiK,EAAM,OACN9J,EAAQ,SAAS,KAAO8J,EAAM,MAE9BA,EAAM,iBACN9J,EAAQ,SAAS,eAAiB8J,EAAM,eAEhD,EAAG,CAAC9J,EAAS8J,EAAM,KAAMA,EAAM,cAAc,CAAC,EAC9CjK,EAAM,UAAU,IAAM,CACdiK,EAAM,SACN9J,EAAQ,WAAW8J,EAAM,MAAM,EAC/B9J,EAAQ,YAAW,EAE3B,EAAG,CAACA,EAAS8J,EAAM,MAAM,CAAC,EAC1BjK,EAAM,UAAU,IAAM,CAClBiK,EAAM,kBACF9J,EAAQ,UAAU,MAAM,KAAK,CACzB,OAAQA,EAAQ,UAAS,CACzC,CAAa,CACT,EAAG,CAACA,EAAS8J,EAAM,gBAAgB,CAAC,EACpCjK,EAAM,UAAU,IAAM,CAClB,GAAIG,EAAQ,gBAAgB,QAAS,CACjC,MAAMqR,EAAUrR,EAAQ,UAAS,EAC7BqR,IAAYtR,EAAU,SACtBC,EAAQ,UAAU,MAAM,KAAK,CACzB,QAAAqR,CACpB,CAAiB,CAET,CACJ,EAAG,CAACrR,EAASD,EAAU,OAAO,CAAC,EAC/BF,EAAM,UAAU,IAAM,CACdiK,EAAM,QAAU,CAAClJ,GAAUkJ,EAAM,OAAQmH,EAAQ,OAAO,GACxDjR,EAAQ,OAAO8J,EAAM,OAAQ,CACzB,cAAe,GACf,GAAG9J,EAAQ,SAAS,YACpC,CAAa,EACDiR,EAAQ,QAAUnH,EAAM,OACxBxD,EAAiBgL,IAAW,CAAE,GAAGA,CAAK,EAAG,GAGzCtR,EAAQ,oBAAmB,CAEnC,EAAG,CAACA,EAAS8J,EAAM,MAAM,CAAC,EAC1BjK,EAAM,UAAU,IAAM,CACbG,EAAQ,OAAO,QAChBA,EAAQ,UAAS,EACjBA,EAAQ,OAAO,MAAQ,IAEvBA,EAAQ,OAAO,QACfA,EAAQ,OAAO,MAAQ,GACvBA,EAAQ,UAAU,MAAM,KAAK,CAAE,GAAGA,EAAQ,WAAY,GAE1DA,EAAQ,iBAAgB,CAC5B,CAAC,EACDgR,EAAa,QAAQ,UAAYlR,GAAkBC,EAAWC,CAAO,EAC9DgR,EAAa,OACxB", "x_google_ignoreList": [0]}