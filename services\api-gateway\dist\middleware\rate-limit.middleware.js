"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadRateLimitMiddleware = exports.authRateLimitMiddleware = exports.premiumRateLimitMiddleware = exports.speedLimitMiddleware = exports.rateLimitMiddleware = void 0;
const express_rate_limit_1 = __importStar(require("express-rate-limit"));
const express_slow_down_1 = __importDefault(require("express-slow-down"));
const logger_1 = require("../utils/logger");
// Create rate limiter
exports.rateLimitMiddleware = (0, express_rate_limit_1.default)({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000'), // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS ?? '100'), // limit each IP to 100 requests per windowMs
    message: {
        success: false,
        message: 'Too many requests from this IP, please try again later.',
        retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000') / 1000),
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    keyGenerator: (req) => {
        // Use user ID if authenticated, otherwise use IP with proper IPv6 handling
        const user = req.user;
        if (user?.id) {
            return user.id;
        }
        // Use the official ipKeyGenerator helper for proper IPv6 support
        const ip = req.ip ?? req.socket.remoteAddress ?? '127.0.0.1';
        return (0, express_rate_limit_1.ipKeyGenerator)(ip);
    },
    handler: (req, res) => {
        const user = req.user;
        logger_1.logger.warn(`Rate limit exceeded for ${user?.id ?? req.ip} on ${req.path}`);
        res.status(429).json({
            success: false,
            message: 'Too many requests from this IP, please try again later.',
            retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000') / 1000),
        });
    },
    skip: (req) => {
        // Skip rate limiting for health checks
        return req.path.startsWith('/health');
    },
});
// Create speed limiter (slow down responses after hitting limit)
exports.speedLimitMiddleware = (0, express_slow_down_1.default)({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000'), // 15 minutes
    delayAfter: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS ?? '100') / 2, // allow first 50 requests per windowMs at full speed
    delayMs: () => 500, // slow down subsequent requests by 500ms per request
    maxDelayMs: 20000, // maximum delay of 20 seconds
    keyGenerator: (req) => {
        const user = req.user;
        if (user?.id) {
            return user.id;
        }
        // Use the official ipKeyGenerator helper for proper IPv6 support
        const ip = req.ip ?? req.socket.remoteAddress ?? '127.0.0.1';
        return (0, express_rate_limit_1.ipKeyGenerator)(ip);
    },
    skip: (req) => {
        return req.path.startsWith('/health');
    },
    validate: {
        delayMs: false, // Disable the warning about delayMs behavior change
    },
});
// Premium user rate limits (higher limits)
exports.premiumRateLimitMiddleware = (0, express_rate_limit_1.default)({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000'),
    max: parseInt(process.env.PREMIUM_RATE_LIMIT_MAX_REQUESTS ?? '1000'), // 10x higher limit
    message: {
        success: false,
        message: 'Rate limit exceeded for premium user.',
        retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000') / 1000),
    },
    keyGenerator: (req) => {
        const user = req.user;
        if (user?.id) {
            return user.id;
        }
        // Use the official ipKeyGenerator helper for proper IPv6 support
        const ip = req.ip ?? req.socket.remoteAddress ?? '127.0.0.1';
        return (0, express_rate_limit_1.ipKeyGenerator)(ip);
    },
    skip: (req) => {
        // Only apply to authenticated premium users
        const user = req.user;
        return !user || !['premium', 'enterprise'].includes(user.subscriptionTier);
    },
});
// API-specific rate limits
exports.authRateLimitMiddleware = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 login requests per windowMs
    message: {
        success: false,
        message: 'Too many authentication attempts, please try again later.',
    },
    skipSuccessfulRequests: true, // Don't count successful requests
    keyGenerator: (req) => {
        // Use the official ipKeyGenerator helper for proper IPv6 support
        const ip = req.ip ?? req.socket.remoteAddress ?? '127.0.0.1';
        return (0, express_rate_limit_1.ipKeyGenerator)(ip);
    },
});
exports.uploadRateLimitMiddleware = (0, express_rate_limit_1.default)({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10, // limit each user to 10 uploads per hour
    message: {
        success: false,
        message: 'Upload limit exceeded, please try again later.',
    },
    keyGenerator: (req) => {
        const user = req.user;
        if (user?.id) {
            return user.id;
        }
        // Use the official ipKeyGenerator helper for proper IPv6 support
        const ip = req.ip ?? req.socket.remoteAddress ?? '127.0.0.1';
        return (0, express_rate_limit_1.ipKeyGenerator)(ip);
    },
});
//# sourceMappingURL=rate-limit.middleware.js.map