import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { MapPin, Clock, DollarSign, Building, Users, Calendar, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { apiService } from '@/services/api';

interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  type: string;
  salary: {
    min?: number;
    max?: number;
    range?: string;
  };
  description: string;
  requirements: string[];
  benefits: string[];
  skills: string[];
  postedBy: string;
  createdAt: string;
  viewCount: number;
}

export const JobDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [job, setJob] = useState<Job | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchJob = async () => {
      try {
        setLoading(true);
        const response = await apiService.get(`/jobs/${id}`) as { data: Job };
        setJob(response.data);  
      } catch (err) {
        setError('Failed to load job details');
        console.error('Error fetching job:', err);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchJob();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <Loader2 className="w-8 h-8 animate-spin text-primary-600" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !job) {
    return (
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="card">
            <div className="card-body text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Job Not Found</h2>
              <p className="text-gray-600">{error || 'The job you are looking for does not exist.'}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="card mb-6">
          <div className="card-body">
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-start gap-4">
                <div className="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Building className="w-8 h-8 text-primary-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    {job.title}
                  </h1>
                  <p className="text-lg text-gray-600 mb-3">{job.company}</p>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      {job.location}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {job.type}
                    </span>
                    <span className="flex items-center gap-1">
                      <DollarSign className="w-4 h-4" />
                      {job.salary.range || `$${job.salary.min?.toLocaleString()} - $${job.salary.max?.toLocaleString()}`}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      Posted {new Date(job.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline">Save Job</Button>
                <Button>Apply Now</Button>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Job description */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-semibold text-gray-900">Job Description</h2>
              </div>
              <div className="card-body">
                <div className="prose prose-sm max-w-none">
                  <p className="mb-4 last:mb-0 whitespace-pre-line">
                    {job.description}
                  </p>
                </div>
              </div>
            </div>

            {/* Requirements */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-semibold text-gray-900">Required Skills</h2>
              </div>
              <div className="card-body">
                <div className="flex flex-wrap gap-2">
                  {job.skills.map((skill) => (
                    <span key={skill} className="badge badge-primary">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Benefits */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-semibold text-gray-900">Benefits</h2>
              </div>
              <div className="card-body">
                <div className="grid grid-cols-2 gap-2">
                  {job.benefits.map((benefit) => (
                    <div key={benefit} className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Company info */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-semibold text-gray-900">About the Company</h2>
              </div>
              <div className="card-body space-y-4">
                <div>
                  <h3 className="font-medium text-gray-900">{job.company}</h3>
                  <p className="text-sm text-gray-600 mt-1">Technology</p>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Users className="w-4 h-4" />
                  {job.viewCount} views
                </div>
                <Button variant="outline" size="sm" className="w-full">
                  Visit Company Page
                </Button>
              </div>
            </div>

            {/* Similar jobs - TODO: Implement similar jobs API */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-semibold text-gray-900">Similar Jobs</h2>
              </div>
              <div className="card-body">
                <p className="text-sm text-gray-500">Similar jobs will be displayed here</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
