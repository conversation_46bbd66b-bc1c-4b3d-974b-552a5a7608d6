import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';
import { ResponseUtil } from '../utils/response';
import { logger } from '../utils/logger';
// import { z } from 'zod';
import { randomUUID } from 'crypto';
import { EncryptionUtils } from '../utils/encryption';
import { CreateUserRequest, LoginRequest } from '../types/auth.types';

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  public register = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const userData = req.body as CreateUserRequest;
      const ipAddress = req.ip ?? req.socket.remoteAddress ?? 'unknown';

      const result = await this.authService.register(userData, ipAddress);

      const response = ResponseUtil.created(
        result,
        'User registered successfully'
      );
      res.status(response.statusCode).json(response);
    } catch (error) {
      next(error);
    }
  };

  public login = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const loginData = req.body as LoginRequest;
      const ipAddress = req.ip ?? req.socket.remoteAddress ?? 'unknown';
      const userAgent = req.headers['user-agent'] ?? 'unknown';

      const result = await this.authService.login(
        loginData,
        ipAddress,
        userAgent
      );

      const response = ResponseUtil.success(result, 'Login successful');
      res.status(response.statusCode).json(response);
    } catch (error) {
      next(error);
    }
  };

  public googleCallback = (
    req: Request,
    res: Response,
    next: NextFunction
  ): void => {
    try {
      // User is available in req.user after passport authentication
      const user = req.user;

      if (!user) {
        // Redirect to frontend with error
        const frontendUrl = process.env.FRONTEND_URL || 'https://jobs-app-ydwim.ondigitalocean.app';
        const errorUrl = `${frontendUrl}/login?error=oauth_failed&message=Google authentication failed`;
        return res.redirect(errorUrl);
      }

      // Create session and generate tokens
      const sessionId = randomUUID();
      const tokens = {
        accessToken: EncryptionUtils.generateAccessToken({
          userId: (user as { id: string }).id,
          email: (user as { email: string }).email,
          role: (user as { role: string }).role,
          sessionId,
        }),
        refreshToken: EncryptionUtils.generateRefreshToken({
          userId: (user as { id: string }).id,
          sessionId,
          tokenVersion: 1,
        }),
      };

      // Redirect to frontend with tokens as URL parameters
      const frontendUrl = process.env.FRONTEND_URL || 'https://jobs-app-ydwim.ondigitalocean.app';
      const successUrl = `${frontendUrl}/auth/callback?` +
        `access_token=${encodeURIComponent(tokens.accessToken)}&` +
        `refresh_token=${encodeURIComponent(tokens.refreshToken)}&` +
        `user=${encodeURIComponent(JSON.stringify({
          id: (user as { id: string }).id,
          email: (user as { email: string }).email,
          firstName: (user as { firstName: string }).firstName,
          lastName: (user as { lastName: string }).lastName,
          role: (user as { role: string }).role,
        }))}`;

      logger.info('Google OAuth successful, redirecting to frontend');
      res.redirect(successUrl);
    } catch (error) {
      logger.error('Google OAuth callback error:', error);
      // Redirect to frontend with error
      const frontendUrl = process.env.FRONTEND_URL || 'https://jobs-app-ydwim.ondigitalocean.app';
      const errorUrl = `${frontendUrl}/login?error=oauth_error&message=Authentication failed`;
      res.redirect(errorUrl);
    }
  };

  public refreshToken = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { refreshToken } = req.body as { refreshToken: string };

      if (!refreshToken) {
        const response = ResponseUtil.error('Refresh token is required', 400);
        res.status(response.statusCode).json(response);
        return;
      }

      const result = await this.authService.refreshToken(refreshToken);

      const response = ResponseUtil.success(
        result,
        'Token refreshed successfully'
      );
      res.status(response.statusCode).json(response);
    } catch (error) {
      next(error);
    }
  };

  public logout = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const sessionId = req.headers['x-session-id'] as string;
      const authHeader = req.headers.authorization;
      const accessToken = authHeader?.startsWith('Bearer ')
        ? authHeader.substring(7)
        : '';

      if (!sessionId || !accessToken) {
        const response = ResponseUtil.error(
          'Session ID and access token are required',
          400
        );
        res.status(response.statusCode).json(response);
        return;
      }

      await this.authService.logout(sessionId, accessToken);

      const response = ResponseUtil.success(null, 'Logout successful');
      res.status(response.statusCode).json(response);
    } catch (error) {
      next(error);
    }
  };

  public verifyEmail = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { email, token } = req.body as { email: string; token: string };

      await this.authService.verifyEmail(email, token);

      const response = ResponseUtil.success(
        null,
        'Email verified successfully'
      );
      res.status(response.statusCode).json(response);
    } catch (error) {
      next(error);
    }
  };

  public forgotPassword = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { email } = req.body as { email: string };

      await this.authService.requestPasswordReset(email);

      const response = ResponseUtil.success(null, 'Password reset email sent');
      res.status(response.statusCode).json(response);
    } catch (error) {
      next(error);
    }
  };

  public resetPassword = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { token, password } = req.body as {
        token: string;
        password: string;
      };

      await this.authService.resetPassword(token, password);

      const response = ResponseUtil.success(
        null,
        'Password reset successfully'
      );
      res.status(response.statusCode).json(response);
    } catch (error) {
      next(error);
    }
  };

  public changePassword = (
    req: Request,
    res: Response,
    next: NextFunction
  ): void => {
    try {
      // This would require authentication middleware
      const userId = (req.user as { id: string }).id;
      if (!userId) {
        const response = ResponseUtil.error('Authentication required', 401);
        res.status(response.statusCode).json(response);
        return;
      }

      // const { currentPassword, newPassword } = req.body as {
      //   currentPassword: string;
      //   newPassword: string;
      // };

      // TODO: Implement change password logic
      const response = ResponseUtil.success(
        null,
        'Password changed successfully'
      );
      res.status(response.statusCode).json(response);
    } catch (error) {
      next(error);
    }
  };

  public resendVerification = (
    req: Request,
    res: Response,
    next: NextFunction
  ): void => {
    try {
      // const { email } = req.body as { email: string };

      // TODO: Implement resend verification logic
      const response = ResponseUtil.success(null, 'Verification email sent');
      res.status(response.statusCode).json(response);
    } catch (error) {
      next(error);
    }
  };
}
