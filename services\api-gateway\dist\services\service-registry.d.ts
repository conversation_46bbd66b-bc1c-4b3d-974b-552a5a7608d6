declare class ServiceRegistry {
    private services;
    private healthCheckInterval;
    constructor();
    private registerDefaultServices;
    getServiceUrl(serviceName: string): string;
    getServiceList(): string[];
    getHealthyServices(): string[];
    getServiceHealth(): Record<string, {
        isHealthy: boolean;
        lastCheck: Date | null;
    }>;
    private checkServiceHealth;
    private startHealthChecks;
    stopHealthChecks(): void;
}
export declare const serviceRegistry: ServiceRegistry;
export {};
//# sourceMappingURL=service-registry.d.ts.map