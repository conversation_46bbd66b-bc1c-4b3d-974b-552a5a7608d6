export declare const env: {
    NODE_ENV: "development" | "production" | "test";
    PORT: string;
    MONGODB_URI: string;
    REDIS_URL: string;
    CORS_ORIGIN: string;
    LOG_LEVEL: string;
    AUTH_SERVICE_URL: string;
    USER_SERVICE_URL: string;
    JOB_SERVICE_URL: string;
    RESUME_SERVICE_URL: string;
};
export declare const appConfig: {
    port: number;
    nodeEnv: "development" | "production" | "test";
    corsOrigin: string;
    logLevel: string;
    isDevelopment: boolean;
    isProduction: boolean;
    apiVersion: string;
};
export declare const databaseConfig: {
    mongoUri: string;
    redisUrl: string;
};
export declare const serviceConfig: {
    authServiceUrl: string;
    userServiceUrl: string;
    jobServiceUrl: string;
    resumeServiceUrl: string;
};
//# sourceMappingURL=environment.d.ts.map