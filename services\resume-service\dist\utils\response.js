"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseUtil = void 0;
class ResponseUtil {
    static success(data, message = 'Success', statusCode = 200) {
        return {
            success: true,
            message,
            data,
            timestamp: new Date().toISOString(),
            statusCode,
        };
    }
    static error(message = 'Error occurred', statusCode = 500, errors, path) {
        return {
            success: false,
            message,
            errors: errors ?? [],
            timestamp: new Date().toISOString(),
            path,
            statusCode,
        };
    }
    static created(data, message = 'Resource created successfully') {
        return this.success(data, message, 201);
    }
    static updated(data, message = 'Resource updated successfully') {
        return this.success(data, message, 200);
    }
    static deleted(message = 'Resource deleted successfully') {
        return this.success(null, message, 204);
    }
}
exports.ResponseUtil = ResponseUtil;
//# sourceMappingURL=response.js.map