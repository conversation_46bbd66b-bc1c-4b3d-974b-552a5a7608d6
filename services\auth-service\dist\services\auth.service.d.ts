import { LoginRequest, LoginResponse, GoogleOAuthUser, CreateUserRequest } from '../types/auth.types';
export declare class AuthService {
    private userService;
    private sessionService;
    private emailService;
    private securityEventService;
    constructor();
    /**
     * User registration
     */
    register(userData: CreateUserRequest, ipAddress: string): Promise<{
        user: Record<string, unknown>;
        tokens: Record<string, unknown>;
    }>;
    /**
     * User login
     */
    login(loginData: LoginRequest, ipAddress: string, userAgent: string): Promise<LoginResponse>;
    /**
     * Handle Google OAuth authentication
     */
    handleGoogleAuth(googleUser: GoogleOAuthUser): Promise<LoginResponse>;
    /**
     * Refresh access token
     */
    refreshToken(refreshToken: string): Promise<{
        accessToken: string;
        expiresIn: number;
    }>;
    /**
     * Logout user
     */
    logout(sessionId: string, accessToken: string): Promise<void>;
    /**
     * Verify email
     */
    verifyEmail(email: string, token: string): Promise<void>;
    /**
     * Request password reset
     */
    requestPasswordReset(email: string): Promise<void>;
    /**
     * Reset password
     */
    resetPassword(token: string, newPassword: string): Promise<void>;
    /**
     * Check if token is blacklisted
     */
    isTokenBlacklisted(token: string): Promise<boolean>;
    private generateTokens;
    private handleFailedLogin;
    private getDeviceType;
    private getBrowser;
    private getOS;
}
//# sourceMappingURL=auth.service.d.ts.map