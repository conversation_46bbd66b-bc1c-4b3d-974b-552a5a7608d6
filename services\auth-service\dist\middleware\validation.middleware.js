"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateParams = exports.validateQuery = exports.validateBody = void 0;
const zod_1 = require("zod");
const response_1 = require("../utils/response");
const validateBody = (schema) => {
    return (req, res, next) => {
        try {
            const result = schema.parse(req.body);
            req.body = result;
            next();
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                const errors = error.issues.map(err => `${err.path.join('.')}: ${err.message}`);
                res
                    .status(400)
                    .json(response_1.ResponseUtil.error('Validation failed', 400, errors, req.path));
                return;
            }
            next(error);
        }
    };
};
exports.validateBody = validateBody;
const validateQuery = (schema) => {
    return (req, res, next) => {
        try {
            const result = schema.parse(req.query);
            // Cast to ParsedQs compatible type - Express ParsedQs allows string, string[], or nested ParsedQs
            req.query = result;
            next();
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                const errors = error.issues.map(err => `${err.path.join('.')}: ${err.message}`);
                res
                    .status(400)
                    .json(response_1.ResponseUtil.error('Query validation failed', 400, errors, req.path));
                return;
            }
            next(error);
        }
    };
};
exports.validateQuery = validateQuery;
const validateParams = (schema) => {
    return (req, res, next) => {
        try {
            const result = schema.parse(req.params);
            req.params = result; // Assert the parsed result to the expected type of req.params
            next();
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                const errors = error.issues.map(err => `${err.path.join('.')}: ${err.message}`);
                res
                    .status(400)
                    .json(response_1.ResponseUtil.error('Parameter validation failed', 400, errors, req.path));
                return;
            }
            next(error);
        }
    };
};
exports.validateParams = validateParams;
//# sourceMappingURL=validation.middleware.js.map