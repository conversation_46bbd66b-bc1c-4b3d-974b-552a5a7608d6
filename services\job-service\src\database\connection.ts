import mongoose from 'mongoose';
import { databaseConfig } from '../config/environment';
import { logger } from '../utils/logger';

class DatabaseConnection {
  private static instance: DatabaseConnection | undefined;
  private isConnected = false;

  private constructor() {}

  public static getInstance(): DatabaseConnection {
    DatabaseConnection.instance ??= new DatabaseConnection();
    return DatabaseConnection.instance;
  }

  public async connect(): Promise<void> {
    if (this.isConnected) {
      logger.info('Database already connected');
      return;
    }

    try {
      const options: mongoose.ConnectOptions = {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        family: 4,
      };

      await mongoose.connect(databaseConfig.mongoUri, options);
      this.isConnected = true;

      logger.info('MongoDB connected successfully');

      mongoose.connection.on('error', error => {
        logger.error('MongoDB connection error:', error);
        this.isConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('MongoDB disconnected');
        this.isConnected = false;
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected');
        this.isConnected = true;
      });
    } catch (error) {
      logger.error('MongoDB connection failed:', error);
      this.isConnected = false;
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      await mongoose.disconnect();
      this.isConnected = false;
      logger.info('MongoDB disconnected successfully');
    } catch (error) {
      logger.error('Error disconnecting from MongoDB:', error);
      throw error;
    }
  }

  public isHealthy(): boolean {
    return this.isConnected && (mongoose.connection.readyState as number) === 1;
  }

  public getConnectionState(): string {
    const states: Record<number, string> = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting',
    };
    return states[mongoose.connection.readyState] ?? 'unknown';
  }
}

export const database = DatabaseConnection.getInstance();
