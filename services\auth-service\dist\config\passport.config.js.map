{"version": 3, "file": "passport.config.js", "sourceRoot": "", "sources": ["../../src/config/passport.config.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAAgC;AAChC,qEAAqE;AACrE,+CAAmE;AACnE,+CAAwD;AACxD,4CAAyC;AACzC,2DAAuD;AACvD,2DAAuD;AAEhD,MAAM,aAAa,GAAG,GAAS,EAAE;IACtC,wBAAwB;IACxB,IAAI,0BAAY,CAAC,QAAQ,IAAI,0BAAY,CAAC,YAAY,EAAE,CAAC;QACvD,kBAAQ,CAAC,GAAG,CACV,IAAI,kCAAc,CAChB;YACE,QAAQ,EAAE,0BAAY,CAAC,QAAQ;YAC/B,YAAY,EAAE,0BAAY,CAAC,YAAY;YACvC,WAAW,EAAE,0BAAY,CAAC,WAAW;YACrC,KAAK,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC;SAC5B,EACD,CAAC,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAQ,EAAE;YACjD,KAAK,CAAC,KAAK,IAAmB,EAAE;gBAC9B,IAAI,CAAC;oBACH,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;wBAC9C,SAAS,EAAE,OAAO,CAAC,EAAE;wBACrB,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK;qBAClC,CAAC,CAAC;oBAEH,MAAM,UAAU,GAAG;wBACjB,QAAQ,EAAE,OAAO,CAAC,EAAE;wBACpB,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;wBACvC,SAAS,EAAE,OAAO,CAAC,IAAI,EAAE,SAAS,IAAI,EAAE;wBACxC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,UAAU,IAAI,EAAE;wBACxC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK;wBAClC,aAAa,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,IAAI,KAAK;qBACtD,CAAC;oBAEF,MAAM,WAAW,GAAG,IAAI,0BAAW,EAAE,CAAC;oBACtC,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;oBAE5D,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC1B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;oBAC3C,OAAO,IAAI,CAAC,KAAc,EAAE,SAAS,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CACF,CACF,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,eAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;IAC1E,CAAC;IAED,eAAe;IACf,kBAAQ,CAAC,GAAG,CACV,IAAI,uBAAW,CACb;QACE,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;QACxD,WAAW,EAAE,uBAAS,CAAC,MAAM;QAC7B,MAAM,EAAE,cAAc;QACtB,QAAQ,EAAE,oBAAoB;KAC/B,EACD,CAAC,OAA2B,EAAE,IAAI,EAAQ,EAAE;QAC1C,KAAK,CAAC,KAAK,IAAmB,EAAE;YAC9B,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,IAAI,0BAAW,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAExD,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,eAAM,CAAC,IAAI,CACT,yCAAyC,EACzC,OAAO,CAAC,MAAM,CACf,CAAC;oBACF,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC3B,CAAC;gBAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACnB,eAAM,CAAC,IAAI,CACT,wCAAwC,EACxC,OAAO,CAAC,MAAM,CACf,CAAC;oBACF,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC3B,CAAC;gBAED,gCAAgC;gBAChC,MAAM,WAAW,GAAG,IAAI,0BAAW,EAAE,CAAC;gBACtC,MAAM,aAAa,GAAG,MAAM,WAAW,CAAC,kBAAkB,CACxD,yBAAU,CAAC,2BAA2B,EAAE,CAAC,IAAe,CAAC,IAAI,EAAE,CAChE,CAAC;gBAEF,IAAI,aAAa,EAAE,CAAC;oBAClB,eAAM,CAAC,IAAI,CACT,4CAA4C,EAC5C,OAAO,CAAC,MAAM,CACf,CAAC;oBACF,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC3B,CAAC;gBAED,OAAO,IAAI,CAAC,IAAI,EAAE;oBAChB,EAAE,EAAG,IAAI,CAAC,GAAkC,CAAC,QAAQ,EAAE;oBACvD,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;oBACvC,WAAW,EAAE,EAAE,EAAE,8BAA8B;iBAChD,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;gBAC7C,OAAO,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CACF,CACF,CAAC;IAEF,6DAA6D;IAC7D,8DAA8D;IAC9D,kBAAQ,CAAC,aAAa,CAAC,CAAC,IAAS,EAAE,IAAI,EAAQ,EAAE;QAC/C,sEAAsE;QACtE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC;IAEH,kBAAQ,CAAC,eAAe,CAAC,CAAC,EAAU,EAAE,IAAI,EAAQ,EAAE;QAClD,KAAK,CAAC,KAAK,IAAmB,EAAE;YAC9B,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,IAAI,0BAAW,EAAE,CAAC;gBACtC,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAC5C,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACnB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,KAAc,EAAE,IAAI,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AA5HW,QAAA,aAAa,iBA4HxB"}