import { Schema } from 'mongoose';
export declare const Job: import("mongoose").Model<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    title: string;
    type: "full_time" | "part_time" | "contract" | "internship" | "freelance";
    company: string;
    description: string;
    location: string;
    requirements: string[];
    benefits: string[];
    skills: string[];
    education: "high_school" | "associate" | "bachelor" | "master" | "phd" | "any";
    remote: boolean;
    status: "active" | "paused" | "closed" | "draft";
    postedBy: string;
    tags: string[];
    isFeatured: boolean;
    applicationCount: number;
    viewCount: number;
    expiresAt?: NativeDate | null;
    applicationDeadline?: NativeDate | null;
    salary?: {
        currency: string;
        min?: number | null;
        max?: number | null;
        range?: string | null;
    } | null;
    experience?: {
        min?: number | null;
        max?: number | null;
    } | null;
}, {}, {}, {}, import("mongoose").Document<unknown, {}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    title: string;
    type: "full_time" | "part_time" | "contract" | "internship" | "freelance";
    company: string;
    description: string;
    location: string;
    requirements: string[];
    benefits: string[];
    skills: string[];
    education: "high_school" | "associate" | "bachelor" | "master" | "phd" | "any";
    remote: boolean;
    status: "active" | "paused" | "closed" | "draft";
    postedBy: string;
    tags: string[];
    isFeatured: boolean;
    applicationCount: number;
    viewCount: number;
    expiresAt?: NativeDate | null;
    applicationDeadline?: NativeDate | null;
    salary?: {
        currency: string;
        min?: number | null;
        max?: number | null;
        range?: string | null;
    } | null;
    experience?: {
        min?: number | null;
        max?: number | null;
    } | null;
}, {}, {
    timestamps: true;
    versionKey: false;
    toJSON: {
        transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown>;
    };
}> & {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    title: string;
    type: "full_time" | "part_time" | "contract" | "internship" | "freelance";
    company: string;
    description: string;
    location: string;
    requirements: string[];
    benefits: string[];
    skills: string[];
    education: "high_school" | "associate" | "bachelor" | "master" | "phd" | "any";
    remote: boolean;
    status: "active" | "paused" | "closed" | "draft";
    postedBy: string;
    tags: string[];
    isFeatured: boolean;
    applicationCount: number;
    viewCount: number;
    expiresAt?: NativeDate | null;
    applicationDeadline?: NativeDate | null;
    salary?: {
        currency: string;
        min?: number | null;
        max?: number | null;
        range?: string | null;
    } | null;
    experience?: {
        min?: number | null;
        max?: number | null;
    } | null;
} & {
    _id: import("mongoose").Types.ObjectId;
}, Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    versionKey: false;
    toJSON: {
        transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown>;
    };
}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    title: string;
    type: "full_time" | "part_time" | "contract" | "internship" | "freelance";
    company: string;
    description: string;
    location: string;
    requirements: string[];
    benefits: string[];
    skills: string[];
    education: "high_school" | "associate" | "bachelor" | "master" | "phd" | "any";
    remote: boolean;
    status: "active" | "paused" | "closed" | "draft";
    postedBy: string;
    tags: string[];
    isFeatured: boolean;
    applicationCount: number;
    viewCount: number;
    expiresAt?: NativeDate | null;
    applicationDeadline?: NativeDate | null;
    salary?: {
        currency: string;
        min?: number | null;
        max?: number | null;
        range?: string | null;
    } | null;
    experience?: {
        min?: number | null;
        max?: number | null;
    } | null;
}, unknown>>;
//# sourceMappingURL=job.model.d.ts.map