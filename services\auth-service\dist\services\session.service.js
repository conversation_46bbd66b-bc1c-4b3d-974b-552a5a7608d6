"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionService = void 0;
const session_model_1 = require("../models/session.model");
const logger_1 = require("../utils/logger");
class SessionService {
    /**
     * Create a new session
     */
    async createSession(sessionData) {
        const session = new session_model_1.SessionModel({
            ...sessionData,
            isActive: true,
        });
        await session.save();
        logger_1.logger.info(`Session created for user ${String(sessionData.userId)}`);
        return session;
    }
    /**
     * Find session by session ID
     */
    async findBySessionId(sessionId) {
        return await session_model_1.SessionModel.findOne({
            sessionId,
            isActive: true,
        });
    }
    /**
     * Find all active sessions for a user
     */
    async findActiveUserSessions(userId) {
        return await session_model_1.SessionModel.find({
            userId,
            isActive: true,
        }).sort({ createdAt: -1 });
    }
    /**
     * Deactivate a session
     */
    async deactivateSession(sessionId) {
        await session_model_1.SessionModel.updateOne({ sessionId }, { isActive: false });
        logger_1.logger.info(`Session ${sessionId} deactivated`);
    }
    /**
     * Deactivate all sessions for a user
     */
    async deactivateAllUserSessions(userId) {
        await session_model_1.SessionModel.updateMany({ userId }, { isActive: false });
        logger_1.logger.info(`All sessions deactivated for user ${String(userId)}`);
    }
    /**
     * Clean up expired sessions
     */
    async cleanupExpiredSessions() {
        const result = await session_model_1.SessionModel.deleteMany({
            expiresAt: { $lt: new Date() },
        });
        logger_1.logger.info(`Cleaned up ${result.deletedCount} expired sessions`);
    }
    /**
     * Get session statistics for a user
     */
    async getUserSessionStats(userId) {
        const [activeCount, totalCount] = await Promise.all([
            session_model_1.SessionModel.countDocuments({ userId, isActive: true }),
            session_model_1.SessionModel.countDocuments({ userId }),
        ]);
        return {
            activeSessionsCount: activeCount,
            totalSessionsCount: totalCount,
        };
    }
}
exports.SessionService = SessionService;
//# sourceMappingURL=session.service.js.map