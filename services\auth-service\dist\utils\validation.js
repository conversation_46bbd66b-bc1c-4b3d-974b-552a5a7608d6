"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sanitizeEmail = exports.sanitizeString = exports.isStrongPassword = exports.isValidEmail = exports.CommonValidationSchemas = exports.UserValidationSchemas = void 0;
const zod_1 = require("zod");
exports.UserValidationSchemas = {
    register: zod_1.z.object({
        email: zod_1.z.email('Invalid email format'),
        password: zod_1.z
            .string()
            .min(8, 'Password must be at least 8 characters')
            .optional(),
        firstName: zod_1.z
            .string()
            .min(1, 'First name is required')
            .max(50, 'First name too long'),
        lastName: zod_1.z
            .string()
            .min(1, 'Last name is required')
            .max(50, 'Last name too long'),
        googleId: zod_1.z.string().optional(),
        avatar: zod_1.z.url().optional(),
    }),
    login: zod_1.z.object({
        email: zod_1.z.email('Invalid email format'),
        password: zod_1.z.string().min(1, 'Password is required'),
        rememberMe: zod_1.z.boolean().optional(),
    }),
    updateProfile: zod_1.z.object({
        firstName: zod_1.z
            .string()
            .min(1, 'First name is required')
            .max(50, 'First name too long')
            .optional(),
        lastName: zod_1.z
            .string()
            .min(1, 'Last name is required')
            .max(50, 'Last name too long')
            .optional(),
        avatar: zod_1.z.url().optional(),
    }),
    changePassword: zod_1.z
        .object({
        currentPassword: zod_1.z.string().min(1, 'Current password is required'),
        newPassword: zod_1.z
            .string()
            .min(8, 'New password must be at least 8 characters'),
        confirmPassword: zod_1.z.string().min(1, 'Password confirmation is required'),
    })
        .refine(data => data.newPassword === data.confirmPassword, {
        message: "Passwords don't match",
        path: ['confirmPassword'],
    }),
    resetPassword: zod_1.z
        .object({
        token: zod_1.z.string().min(1, 'Reset token is required'),
        newPassword: zod_1.z.string().min(8, 'Password must be at least 8 characters'),
        confirmPassword: zod_1.z.string().min(1, 'Password confirmation is required'),
    })
        .refine(data => data.newPassword === data.confirmPassword, {
        message: "Passwords don't match",
        path: ['confirmPassword'],
    }),
    requestPasswordReset: zod_1.z.object({
        email: zod_1.z.email('Invalid email format'),
    }),
    verifyEmail: zod_1.z.object({
        email: zod_1.z.email('Invalid email format'),
        token: zod_1.z.string().min(1, 'Verification token is required'),
    }),
    confirmResetPassword: zod_1.z
        .object({
        token: zod_1.z.string().min(1, 'Reset token is required'),
        newPassword: zod_1.z.string().min(8, 'Password must be at least 8 characters'),
        confirmPassword: zod_1.z.string().min(1, 'Password confirmation is required'),
    })
        .refine(data => data.newPassword === data.confirmPassword, {
        message: "Passwords don't match",
        path: ['confirmPassword'],
    }),
};
exports.CommonValidationSchemas = {
    refreshToken: zod_1.z.object({
        refreshToken: zod_1.z.string().min(1, 'Refresh token is required'),
    }),
    pagination: zod_1.z.object({
        page: zod_1.z
            .string()
            .transform(val => parseInt(val, 10))
            .pipe(zod_1.z.number().min(1))
            .optional(),
        limit: zod_1.z
            .string()
            .transform(val => parseInt(val, 10))
            .pipe(zod_1.z.number().min(1).max(100))
            .optional(),
        sortBy: zod_1.z.string().optional(),
        sortOrder: zod_1.z.enum(['asc', 'desc']).optional(),
    }),
    search: zod_1.z.object({
        q: zod_1.z.string().min(1, 'Search query is required').optional(),
        category: zod_1.z.string().optional(),
        status: zod_1.z.string().optional(),
    }),
    id: zod_1.z.object({
        id: zod_1.z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ID format'),
    }),
};
// Email validation helper
const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
exports.isValidEmail = isValidEmail;
// Password strength validation helper
const isStrongPassword = (password) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    return (password.length >= minLength &&
        hasUpperCase &&
        hasLowerCase &&
        hasNumbers &&
        hasSpecialChar);
};
exports.isStrongPassword = isStrongPassword;
// Sanitization helpers
const sanitizeString = (str) => {
    return str.trim().replace(/[<>]/g, '');
};
exports.sanitizeString = sanitizeString;
const sanitizeEmail = (email) => {
    return email.toLowerCase().trim();
};
exports.sanitizeEmail = sanitizeEmail;
//# sourceMappingURL=validation.js.map