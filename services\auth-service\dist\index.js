"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const morgan_1 = __importDefault(require("morgan"));
const passport_1 = __importDefault(require("passport"));
const environment_1 = require("./config/environment");
const connection_1 = require("./database/connection");
const logger_1 = require("./utils/logger");
const error_middleware_1 = require("./middleware/error.middleware");
const rate_limit_middleware_1 = require("./middleware/rate-limit.middleware");
const passport_config_1 = require("./config/passport.config");
const auth_routes_1 = require("./routes/auth.routes");
const health_routes_1 = require("./routes/health.routes");
// import { RedisClient } from './services/redis.service';
class AuthService {
    app;
    server = null;
    // private redisClient: RedisClient;
    constructor() {
        this.app = (0, express_1.default)();
        // Enable trust proxy for proper IP detection behind reverse proxy
        // Configure trust proxy to only trust specific proxies to avoid rate limiting issues
        this.app.set('trust proxy', 1);
        // this.redisClient = new RedisClient();
        this.setupMiddleware();
        this.setupPassport();
        this.setupRoutes();
        this.setupErrorHandling();
    }
    setupMiddleware() {
        // Security middleware
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", 'data:', 'https:'],
                },
            },
            crossOriginEmbedderPolicy: false,
        }));
        // CORS configuration
        this.app.use((0, cors_1.default)({
            origin: environment_1.appConfig.isDevelopment
                ? true
                : (process.env.CORS_ORIGIN?.split(',') ?? '*'),
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        }));
        // Compression
        this.app.use((0, compression_1.default)());
        // Body parsing
        this.app.use(express_1.default.json({ limit: '1mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '1mb' }));
        // Logging
        this.app.use((0, morgan_1.default)(environment_1.appConfig.isDevelopment ? 'dev' : 'combined', {
            stream: {
                write: (message) => logger_1.logger.http(message.trim()),
            },
        }));
        // Rate limiting
        this.app.use(rate_limit_middleware_1.rateLimitMiddleware);
        // Passport initialization
        this.app.use(passport_1.default.initialize());
        // Request ID middleware
        this.app.use((req, res, next) => {
            req.headers['x-request-id'] =
                req.headers['x-request-id'] ??
                    Math.random().toString(36).substring(2, 15);
            res.setHeader('X-Request-ID', req.headers['x-request-id']);
            next();
        });
    }
    setupPassport() {
        (0, passport_config_1.setupPassport)();
    }
    setupRoutes() {
        // Health check routes
        this.app.use('/health', health_routes_1.healthRoutes);
        // Authentication routes - support both /auth and /api/v1 for API Gateway compatibility
        this.app.use('/auth', auth_routes_1.authRoutes);
        this.app.use('/api/v1', auth_routes_1.authRoutes);
        // Service info
        this.app.get('/api/v1', (req, res) => {
            res.json({
                name: 'Job Platform Authentication Service',
                version: environment_1.appConfig.apiVersion,
                environment: environment_1.appConfig.nodeEnv,
                timestamp: new Date().toISOString(),
                features: [
                    'JWT Authentication',
                    'Google OAuth2',
                    'Session Management',
                    'Two-Factor Authentication',
                    'Password Reset',
                    'Account Security',
                ],
            });
        });
    }
    setupErrorHandling() {
        // 404 handler
        this.app.use((req, res) => {
            res.status(404).json({
                success: false,
                message: `Route ${req.originalUrl} not found`,
                timestamp: new Date().toISOString(),
            });
        });
        // Global error handler
        this.app.use(error_middleware_1.errorHandler);
    }
    async start() {
        try {
            // Start server first to respond to health checks
            const port = environment_1.appConfig.port;
            this.server = this.app.listen(port, '0.0.0.0', () => {
                logger_1.logger.info(`🔐 Auth Service running on port ${port}`);
                logger_1.logger.info(`📝 Environment: ${environment_1.appConfig.nodeEnv}`);
                logger_1.logger.info(`🔗 Health Check: http://localhost:${port}/health`);
            });
            // Connect to database after server is running
            try {
                await connection_1.database.connect();
                logger_1.logger.info('Database connected successfully');
            }
            catch (dbError) {
                logger_1.logger.warn('Database connection failed - service will run with limited functionality:', dbError);
                // Continue running without database - some features will be disabled
            }
            // Connect to Redis (optional - service can run without Redis but with reduced functionality)
            // try {
            //   await this.redisClient.connect();
            //   logger.info('Redis connected successfully');
            // } catch (redisError) {
            //   logger.warn('Redis connection failed - service will run with reduced functionality:', redisError);
            //   // Continue without Redis - some features like rate limiting and caching will be disabled
            // }
            logger_1.logger.info('Redis connection disabled - running without Redis');
            // Graceful shutdown
            process.on('SIGTERM', () => void this.shutdown());
            process.on('SIGINT', () => void this.shutdown());
        }
        catch (error) {
            logger_1.logger.error('Failed to start Auth Service:', error);
            process.exit(1);
        }
    }
    async shutdown() {
        logger_1.logger.info('🔄 Graceful shutdown initiated...');
        if (this.server) {
            await new Promise(resolve => {
                this.server.close(() => {
                    logger_1.logger.info('✅ HTTP server closed');
                    // Close database connection
                    void connection_1.database.disconnect();
                    // Close Redis connection
                    // void this.redisClient.disconnect();  
                    logger_1.logger.info('✅ Graceful shutdown completed');
                    process.exit(0);
                });
                resolve();
            });
        }
    }
}
// Start the Auth Service
const authService = new AuthService();
authService.start().catch(error => {
    logger_1.logger.error('Failed to start Auth Service:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map