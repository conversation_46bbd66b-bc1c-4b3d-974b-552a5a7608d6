import{a as Jt,R as ie}from"./router-CTBu-pz9.js";function Ue(t,e){return Ue=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,n){return r.__proto__=n,r},Ue(t,e)}function Oe(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,Ue(t,e)}var Ee=(function(){function t(){this.listeners=[]}var e=t.prototype;return e.subscribe=function(n){var i=this,s=n||function(){};return this.listeners.push(s),this.onSubscribe(),function(){i.listeners=i.listeners.filter(function(o){return o!==s}),i.onUnsubscribe()}},e.hasListeners=function(){return this.listeners.length>0},e.onSubscribe=function(){},e.onUnsubscribe=function(){},t})();function O(){return O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},O.apply(null,arguments)}var yt=typeof window>"u";function D(){}function Vt(t,e){return typeof t=="function"?t(e):t}function Wt(t){return typeof t=="number"&&t>=0&&t!==1/0}function ve(t){return Array.isArray(t)?t:[t]}function Gt(t,e){return Math.max(t+(e||0)-Date.now(),0)}function Te(t,e,r){return Se(t)?typeof e=="function"?O({},r,{queryKey:t,queryFn:e}):O({},e,{queryKey:t}):t}function H(t,e,r){return Se(t)?[O({},e,{queryKey:t}),r]:[t||{},e]}function Xt(t,e){if(t===!0&&e===!0||t==null&&e==null)return"all";if(t===!1&&e===!1)return"none";var r=t??!e;return r?"active":"inactive"}function Je(t,e){var r=t.active,n=t.exact,i=t.fetching,s=t.inactive,o=t.predicate,a=t.queryKey,l=t.stale;if(Se(a)){if(n){if(e.queryHash!==$e(a,e.options))return!1}else if(!be(e.queryKey,a))return!1}var c=Xt(r,s);if(c==="none")return!1;if(c!=="all"){var f=e.isActive();if(c==="active"&&!f||c==="inactive"&&f)return!1}return!(typeof l=="boolean"&&e.isStale()!==l||typeof i=="boolean"&&e.isFetching()!==i||o&&!o(e))}function Ve(t,e){var r=t.exact,n=t.fetching,i=t.predicate,s=t.mutationKey;if(Se(s)){if(!e.options.mutationKey)return!1;if(r){if(z(e.options.mutationKey)!==z(s))return!1}else if(!be(e.options.mutationKey,s))return!1}return!(typeof n=="boolean"&&e.state.status==="loading"!==n||i&&!i(e))}function $e(t,e){var r=e?.queryKeyHashFn||z;return r(t)}function z(t){var e=ve(t);return Zt(e)}function Zt(t){return JSON.stringify(t,function(e,r){return Me(r)?Object.keys(r).sort().reduce(function(n,i){return n[i]=r[i],n},{}):r})}function be(t,e){return mt(ve(t),ve(e))}function mt(t,e){return t===e?!0:typeof t!=typeof e?!1:t&&e&&typeof t=="object"&&typeof e=="object"?!Object.keys(e).some(function(r){return!mt(t[r],e[r])}):!1}function vt(t,e){if(t===e)return t;var r=Array.isArray(t)&&Array.isArray(e);if(r||Me(t)&&Me(e)){for(var n=r?t.length:Object.keys(t).length,i=r?e:Object.keys(e),s=i.length,o=r?[]:{},a=0,l=0;l<s;l++){var c=r?l:i[l];o[c]=vt(t[c],e[c]),o[c]===t[c]&&a++}return n===s&&a===n?t:o}return e}function Me(t){if(!We(t))return!1;var e=t.constructor;if(typeof e>"u")return!0;var r=e.prototype;return!(!We(r)||!r.hasOwnProperty("isPrototypeOf"))}function We(t){return Object.prototype.toString.call(t)==="[object Object]"}function Se(t){return typeof t=="string"||Array.isArray(t)}function Yt(t){return new Promise(function(e){setTimeout(e,t)})}function Ge(t){Promise.resolve().then(t).catch(function(e){return setTimeout(function(){throw e})})}function bt(){if(typeof AbortController=="function")return new AbortController}var en=(function(t){Oe(e,t);function e(){var n;return n=t.call(this)||this,n.setup=function(i){var s;if(!yt&&((s=window)!=null&&s.addEventListener)){var o=function(){return i()};return window.addEventListener("visibilitychange",o,!1),window.addEventListener("focus",o,!1),function(){window.removeEventListener("visibilitychange",o),window.removeEventListener("focus",o)}}},n}var r=e.prototype;return r.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},r.onUnsubscribe=function(){if(!this.hasListeners()){var i;(i=this.cleanup)==null||i.call(this),this.cleanup=void 0}},r.setEventListener=function(i){var s,o=this;this.setup=i,(s=this.cleanup)==null||s.call(this),this.cleanup=i(function(a){typeof a=="boolean"?o.setFocused(a):o.onFocus()})},r.setFocused=function(i){this.focused=i,i&&this.onFocus()},r.onFocus=function(){this.listeners.forEach(function(i){i()})},r.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},e})(Ee),he=new en,tn=(function(t){Oe(e,t);function e(){var n;return n=t.call(this)||this,n.setup=function(i){var s;if(!yt&&((s=window)!=null&&s.addEventListener)){var o=function(){return i()};return window.addEventListener("online",o,!1),window.addEventListener("offline",o,!1),function(){window.removeEventListener("online",o),window.removeEventListener("offline",o)}}},n}var r=e.prototype;return r.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},r.onUnsubscribe=function(){if(!this.hasListeners()){var i;(i=this.cleanup)==null||i.call(this),this.cleanup=void 0}},r.setEventListener=function(i){var s,o=this;this.setup=i,(s=this.cleanup)==null||s.call(this),this.cleanup=i(function(a){typeof a=="boolean"?o.setOnline(a):o.onOnline()})},r.setOnline=function(i){this.online=i,i&&this.onOnline()},r.onOnline=function(){this.listeners.forEach(function(i){i()})},r.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},e})(Ee),de=new tn;function nn(t){return Math.min(1e3*Math.pow(2,t),3e4)}function ge(t){return typeof t?.cancel=="function"}var gt=function(e){this.revert=e?.revert,this.silent=e?.silent};function xe(t){return t instanceof gt}var wt=function(e){var r=this,n=!1,i,s,o,a;this.abort=e.abort,this.cancel=function(v){return i?.(v)},this.cancelRetry=function(){n=!0},this.continueRetry=function(){n=!1},this.continue=function(){return s?.()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(v,m){o=v,a=m});var l=function(m){r.isResolved||(r.isResolved=!0,e.onSuccess==null||e.onSuccess(m),s?.(),o(m))},c=function(m){r.isResolved||(r.isResolved=!0,e.onError==null||e.onError(m),s?.(),a(m))},f=function(){return new Promise(function(m){s=m,r.isPaused=!0,e.onPause==null||e.onPause()}).then(function(){s=void 0,r.isPaused=!1,e.onContinue==null||e.onContinue()})},d=function v(){if(!r.isResolved){var m;try{m=e.fn()}catch(h){m=Promise.reject(h)}i=function(y){if(!r.isResolved&&(c(new gt(y)),r.abort==null||r.abort(),ge(m)))try{m.cancel()}catch{}},r.isTransportCancelable=ge(m),Promise.resolve(m).then(l).catch(function(h){var y,p;if(!r.isResolved){var g=(y=e.retry)!=null?y:3,E=(p=e.retryDelay)!=null?p:nn,w=typeof E=="function"?E(r.failureCount,h):E,C=g===!0||typeof g=="number"&&r.failureCount<g||typeof g=="function"&&g(r.failureCount,h);if(n||!C){c(h);return}r.failureCount++,e.onFail==null||e.onFail(r.failureCount,h),Yt(w).then(function(){if(!he.isFocused()||!de.isOnline())return f()}).then(function(){n?c(h):v()})}})}};d()},rn=(function(){function t(){this.queue=[],this.transactions=0,this.notifyFn=function(r){r()},this.batchNotifyFn=function(r){r()}}var e=t.prototype;return e.batch=function(n){var i;this.transactions++;try{i=n()}finally{this.transactions--,this.transactions||this.flush()}return i},e.schedule=function(n){var i=this;this.transactions?this.queue.push(n):Ge(function(){i.notifyFn(n)})},e.batchCalls=function(n){var i=this;return function(){for(var s=arguments.length,o=new Array(s),a=0;a<s;a++)o[a]=arguments[a];i.schedule(function(){n.apply(void 0,o)})}},e.flush=function(){var n=this,i=this.queue;this.queue=[],i.length&&Ge(function(){n.batchNotifyFn(function(){i.forEach(function(s){n.notifyFn(s)})})})},e.setNotifyFunction=function(n){this.notifyFn=n},e.setBatchNotifyFunction=function(n){this.batchNotifyFn=n},t})(),_=new rn,Ot=console;function Et(){return Ot}function sn(t){Ot=t}var on=(function(){function t(r){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=r.defaultOptions,this.setOptions(r.options),this.observers=[],this.cache=r.cache,this.queryKey=r.queryKey,this.queryHash=r.queryHash,this.initialState=r.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=r.meta,this.scheduleGc()}var e=t.prototype;return e.setOptions=function(n){var i;this.options=O({},this.defaultOptions,n),this.meta=n?.meta,this.cacheTime=Math.max(this.cacheTime||0,(i=this.options.cacheTime)!=null?i:300*1e3)},e.setDefaultOptions=function(n){this.defaultOptions=n},e.scheduleGc=function(){var n=this;this.clearGcTimeout(),Wt(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){n.optionalRemove()},this.cacheTime))},e.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},e.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},e.setData=function(n,i){var s,o,a=this.state.data,l=Vt(n,a);return(s=(o=this.options).isDataEqual)!=null&&s.call(o,a,l)?l=a:this.options.structuralSharing!==!1&&(l=vt(a,l)),this.dispatch({data:l,type:"success",dataUpdatedAt:i?.updatedAt}),l},e.setState=function(n,i){this.dispatch({type:"setState",state:n,setStateOptions:i})},e.cancel=function(n){var i,s=this.promise;return(i=this.retryer)==null||i.cancel(n),s?s.then(D).catch(D):Promise.resolve()},e.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},e.reset=function(){this.destroy(),this.setState(this.initialState)},e.isActive=function(){return this.observers.some(function(n){return n.options.enabled!==!1})},e.isFetching=function(){return this.state.isFetching},e.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(n){return n.getCurrentResult().isStale})},e.isStaleByTime=function(n){return n===void 0&&(n=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!Gt(this.state.dataUpdatedAt,n)},e.onFocus=function(){var n,i=this.observers.find(function(s){return s.shouldFetchOnWindowFocus()});i&&i.refetch(),(n=this.retryer)==null||n.continue()},e.onOnline=function(){var n,i=this.observers.find(function(s){return s.shouldFetchOnReconnect()});i&&i.refetch(),(n=this.retryer)==null||n.continue()},e.addObserver=function(n){this.observers.indexOf(n)===-1&&(this.observers.push(n),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:n}))},e.removeObserver=function(n){this.observers.indexOf(n)!==-1&&(this.observers=this.observers.filter(function(i){return i!==n}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:n}))},e.getObserversCount=function(){return this.observers.length},e.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},e.fetch=function(n,i){var s=this,o,a,l;if(this.state.isFetching){if(this.state.dataUpdatedAt&&i?.cancelRefetch)this.cancel({silent:!0});else if(this.promise){var c;return(c=this.retryer)==null||c.continueRetry(),this.promise}}if(n&&this.setOptions(n),!this.options.queryFn){var f=this.observers.find(function(E){return E.options.queryFn});f&&this.setOptions(f.options)}var d=ve(this.queryKey),v=bt(),m={queryKey:d,pageParam:void 0,meta:this.meta};Object.defineProperty(m,"signal",{enumerable:!0,get:function(){if(v)return s.abortSignalConsumed=!0,v.signal}});var h=function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(m)):Promise.reject("Missing queryFn")},y={fetchOptions:i,options:this.options,queryKey:d,state:this.state,fetchFn:h,meta:this.meta};if((o=this.options.behavior)!=null&&o.onFetch){var p;(p=this.options.behavior)==null||p.onFetch(y)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((a=y.fetchOptions)==null?void 0:a.meta)){var g;this.dispatch({type:"fetch",meta:(g=y.fetchOptions)==null?void 0:g.meta})}return this.retryer=new wt({fn:y.fetchFn,abort:v==null||(l=v.abort)==null?void 0:l.bind(v),onSuccess:function(w){s.setData(w),s.cache.config.onSuccess==null||s.cache.config.onSuccess(w,s),s.cacheTime===0&&s.optionalRemove()},onError:function(w){xe(w)&&w.silent||s.dispatch({type:"error",error:w}),xe(w)||(s.cache.config.onError==null||s.cache.config.onError(w,s),Et().error(w)),s.cacheTime===0&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:y.options.retry,retryDelay:y.options.retryDelay}),this.promise=this.retryer.promise,this.promise},e.dispatch=function(n){var i=this;this.state=this.reducer(this.state,n),_.batch(function(){i.observers.forEach(function(s){s.onQueryUpdate(n)}),i.cache.notify({query:i,type:"queryUpdated",action:n})})},e.getDefaultState=function(n){var i=typeof n.initialData=="function"?n.initialData():n.initialData,s=typeof n.initialData<"u",o=s?typeof n.initialDataUpdatedAt=="function"?n.initialDataUpdatedAt():n.initialDataUpdatedAt:0,a=typeof i<"u";return{data:i,dataUpdateCount:0,dataUpdatedAt:a?o??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:a?"success":"idle"}},e.reducer=function(n,i){var s,o;switch(i.type){case"failed":return O({},n,{fetchFailureCount:n.fetchFailureCount+1});case"pause":return O({},n,{isPaused:!0});case"continue":return O({},n,{isPaused:!1});case"fetch":return O({},n,{fetchFailureCount:0,fetchMeta:(s=i.meta)!=null?s:null,isFetching:!0,isPaused:!1},!n.dataUpdatedAt&&{error:null,status:"loading"});case"success":return O({},n,{data:i.data,dataUpdateCount:n.dataUpdateCount+1,dataUpdatedAt:(o=i.dataUpdatedAt)!=null?o:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var a=i.error;return xe(a)&&a.revert&&this.revertState?O({},this.revertState):O({},n,{error:a,errorUpdateCount:n.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:n.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return O({},n,{isInvalidated:!0});case"setState":return O({},n,i.state);default:return n}},t})(),an=(function(t){Oe(e,t);function e(n){var i;return i=t.call(this)||this,i.config=n||{},i.queries=[],i.queriesMap={},i}var r=e.prototype;return r.build=function(i,s,o){var a,l=s.queryKey,c=(a=s.queryHash)!=null?a:$e(l,s),f=this.get(c);return f||(f=new on({cache:this,queryKey:l,queryHash:c,options:i.defaultQueryOptions(s),state:o,defaultOptions:i.getQueryDefaults(l),meta:s.meta}),this.add(f)),f},r.add=function(i){this.queriesMap[i.queryHash]||(this.queriesMap[i.queryHash]=i,this.queries.push(i),this.notify({type:"queryAdded",query:i}))},r.remove=function(i){var s=this.queriesMap[i.queryHash];s&&(i.destroy(),this.queries=this.queries.filter(function(o){return o!==i}),s===i&&delete this.queriesMap[i.queryHash],this.notify({type:"queryRemoved",query:i}))},r.clear=function(){var i=this;_.batch(function(){i.queries.forEach(function(s){i.remove(s)})})},r.get=function(i){return this.queriesMap[i]},r.getAll=function(){return this.queries},r.find=function(i,s){var o=H(i,s),a=o[0];return typeof a.exact>"u"&&(a.exact=!0),this.queries.find(function(l){return Je(a,l)})},r.findAll=function(i,s){var o=H(i,s),a=o[0];return Object.keys(a).length>0?this.queries.filter(function(l){return Je(a,l)}):this.queries},r.notify=function(i){var s=this;_.batch(function(){s.listeners.forEach(function(o){o(i)})})},r.onFocus=function(){var i=this;_.batch(function(){i.queries.forEach(function(s){s.onFocus()})})},r.onOnline=function(){var i=this;_.batch(function(){i.queries.forEach(function(s){s.onOnline()})})},e})(Ee),un=(function(){function t(r){this.options=O({},r.defaultOptions,r.options),this.mutationId=r.mutationId,this.mutationCache=r.mutationCache,this.observers=[],this.state=r.state||cn(),this.meta=r.meta}var e=t.prototype;return e.setState=function(n){this.dispatch({type:"setState",state:n})},e.addObserver=function(n){this.observers.indexOf(n)===-1&&this.observers.push(n)},e.removeObserver=function(n){this.observers=this.observers.filter(function(i){return i!==n})},e.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(D).catch(D)):Promise.resolve()},e.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},e.execute=function(){var n=this,i,s=this.state.status==="loading",o=Promise.resolve();return s||(this.dispatch({type:"loading",variables:this.options.variables}),o=o.then(function(){n.mutationCache.config.onMutate==null||n.mutationCache.config.onMutate(n.state.variables,n)}).then(function(){return n.options.onMutate==null?void 0:n.options.onMutate(n.state.variables)}).then(function(a){a!==n.state.context&&n.dispatch({type:"loading",context:a,variables:n.state.variables})})),o.then(function(){return n.executeMutation()}).then(function(a){i=a,n.mutationCache.config.onSuccess==null||n.mutationCache.config.onSuccess(i,n.state.variables,n.state.context,n)}).then(function(){return n.options.onSuccess==null?void 0:n.options.onSuccess(i,n.state.variables,n.state.context)}).then(function(){return n.options.onSettled==null?void 0:n.options.onSettled(i,null,n.state.variables,n.state.context)}).then(function(){return n.dispatch({type:"success",data:i}),i}).catch(function(a){return n.mutationCache.config.onError==null||n.mutationCache.config.onError(a,n.state.variables,n.state.context,n),Et().error(a),Promise.resolve().then(function(){return n.options.onError==null?void 0:n.options.onError(a,n.state.variables,n.state.context)}).then(function(){return n.options.onSettled==null?void 0:n.options.onSettled(void 0,a,n.state.variables,n.state.context)}).then(function(){throw n.dispatch({type:"error",error:a}),a})})},e.executeMutation=function(){var n=this,i;return this.retryer=new wt({fn:function(){return n.options.mutationFn?n.options.mutationFn(n.state.variables):Promise.reject("No mutationFn found")},onFail:function(){n.dispatch({type:"failed"})},onPause:function(){n.dispatch({type:"pause"})},onContinue:function(){n.dispatch({type:"continue"})},retry:(i=this.options.retry)!=null?i:0,retryDelay:this.options.retryDelay}),this.retryer.promise},e.dispatch=function(n){var i=this;this.state=ln(this.state,n),_.batch(function(){i.observers.forEach(function(s){s.onMutationUpdate(n)}),i.mutationCache.notify(i)})},t})();function cn(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function ln(t,e){switch(e.type){case"failed":return O({},t,{failureCount:t.failureCount+1});case"pause":return O({},t,{isPaused:!0});case"continue":return O({},t,{isPaused:!1});case"loading":return O({},t,{context:e.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:e.variables});case"success":return O({},t,{data:e.data,error:null,status:"success",isPaused:!1});case"error":return O({},t,{data:void 0,error:e.error,failureCount:t.failureCount+1,isPaused:!1,status:"error"});case"setState":return O({},t,e.state);default:return t}}var fn=(function(t){Oe(e,t);function e(n){var i;return i=t.call(this)||this,i.config=n||{},i.mutations=[],i.mutationId=0,i}var r=e.prototype;return r.build=function(i,s,o){var a=new un({mutationCache:this,mutationId:++this.mutationId,options:i.defaultMutationOptions(s),state:o,defaultOptions:s.mutationKey?i.getMutationDefaults(s.mutationKey):void 0,meta:s.meta});return this.add(a),a},r.add=function(i){this.mutations.push(i),this.notify(i)},r.remove=function(i){this.mutations=this.mutations.filter(function(s){return s!==i}),i.cancel(),this.notify(i)},r.clear=function(){var i=this;_.batch(function(){i.mutations.forEach(function(s){i.remove(s)})})},r.getAll=function(){return this.mutations},r.find=function(i){return typeof i.exact>"u"&&(i.exact=!0),this.mutations.find(function(s){return Ve(i,s)})},r.findAll=function(i){return this.mutations.filter(function(s){return Ve(i,s)})},r.notify=function(i){var s=this;_.batch(function(){s.listeners.forEach(function(o){o(i)})})},r.onFocus=function(){this.resumePausedMutations()},r.onOnline=function(){this.resumePausedMutations()},r.resumePausedMutations=function(){var i=this.mutations.filter(function(s){return s.state.isPaused});return _.batch(function(){return i.reduce(function(s,o){return s.then(function(){return o.continue().catch(D)})},Promise.resolve())})},e})(Ee);function hn(){return{onFetch:function(e){e.fetchFn=function(){var r,n,i,s,o,a,l=(r=e.fetchOptions)==null||(n=r.meta)==null?void 0:n.refetchPage,c=(i=e.fetchOptions)==null||(s=i.meta)==null?void 0:s.fetchMore,f=c?.pageParam,d=c?.direction==="forward",v=c?.direction==="backward",m=((o=e.state.data)==null?void 0:o.pages)||[],h=((a=e.state.data)==null?void 0:a.pageParams)||[],y=bt(),p=y?.signal,g=h,E=!1,w=e.options.queryFn||function(){return Promise.reject("Missing queryFn")},C=function(T,k,S,$){return g=$?[k].concat(g):[].concat(g,[k]),$?[S].concat(T):[].concat(T,[S])},A=function(T,k,S,$){if(E)return Promise.reject("Cancelled");if(typeof S>"u"&&!k&&T.length)return Promise.resolve(T);var P={queryKey:e.queryKey,signal:p,pageParam:S,meta:e.meta},B=w(P),X=Promise.resolve(B).then(function(I){return C(T,S,I,$)});if(ge(B)){var L=X;L.cancel=B.cancel}return X},F;if(!m.length)F=A([]);else if(d){var q=typeof f<"u",te=q?f:Xe(e.options,m);F=A(m,q,te)}else if(v){var K=typeof f<"u",ce=K?f:dn(e.options,m);F=A(m,K,ce,!0)}else(function(){g=[];var M=typeof e.options.getNextPageParam>"u",T=l&&m[0]?l(m[0],0,m):!0;F=T?A([],M,h[0]):Promise.resolve(C([],h[0],m[0]));for(var k=function(P){F=F.then(function(B){var X=l&&m[P]?l(m[P],P,m):!0;if(X){var L=M?h[P]:Xe(e.options,B);return A(B,M,L)}return Promise.resolve(C(B,h[P],m[P]))})},S=1;S<m.length;S++)k(S)})();var ne=F.then(function(M){return{pages:M,pageParams:g}}),G=ne;return G.cancel=function(){E=!0,y?.abort(),ge(F)&&F.cancel()},ne}}}}function Xe(t,e){return t.getNextPageParam==null?void 0:t.getNextPageParam(e[e.length-1],e)}function dn(t,e){return t.getPreviousPageParam==null?void 0:t.getPreviousPageParam(e[0],e)}var Jr=(function(){function t(r){r===void 0&&(r={}),this.queryCache=r.queryCache||new an,this.mutationCache=r.mutationCache||new fn,this.defaultOptions=r.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var e=t.prototype;return e.mount=function(){var n=this;this.unsubscribeFocus=he.subscribe(function(){he.isFocused()&&de.isOnline()&&(n.mutationCache.onFocus(),n.queryCache.onFocus())}),this.unsubscribeOnline=de.subscribe(function(){he.isFocused()&&de.isOnline()&&(n.mutationCache.onOnline(),n.queryCache.onOnline())})},e.unmount=function(){var n,i;(n=this.unsubscribeFocus)==null||n.call(this),(i=this.unsubscribeOnline)==null||i.call(this)},e.isFetching=function(n,i){var s=H(n,i),o=s[0];return o.fetching=!0,this.queryCache.findAll(o).length},e.isMutating=function(n){return this.mutationCache.findAll(O({},n,{fetching:!0})).length},e.getQueryData=function(n,i){var s;return(s=this.queryCache.find(n,i))==null?void 0:s.state.data},e.getQueriesData=function(n){return this.getQueryCache().findAll(n).map(function(i){var s=i.queryKey,o=i.state,a=o.data;return[s,a]})},e.setQueryData=function(n,i,s){var o=Te(n),a=this.defaultQueryOptions(o);return this.queryCache.build(this,a).setData(i,s)},e.setQueriesData=function(n,i,s){var o=this;return _.batch(function(){return o.getQueryCache().findAll(n).map(function(a){var l=a.queryKey;return[l,o.setQueryData(l,i,s)]})})},e.getQueryState=function(n,i){var s;return(s=this.queryCache.find(n,i))==null?void 0:s.state},e.removeQueries=function(n,i){var s=H(n,i),o=s[0],a=this.queryCache;_.batch(function(){a.findAll(o).forEach(function(l){a.remove(l)})})},e.resetQueries=function(n,i,s){var o=this,a=H(n,i,s),l=a[0],c=a[1],f=this.queryCache,d=O({},l,{active:!0});return _.batch(function(){return f.findAll(l).forEach(function(v){v.reset()}),o.refetchQueries(d,c)})},e.cancelQueries=function(n,i,s){var o=this,a=H(n,i,s),l=a[0],c=a[1],f=c===void 0?{}:c;typeof f.revert>"u"&&(f.revert=!0);var d=_.batch(function(){return o.queryCache.findAll(l).map(function(v){return v.cancel(f)})});return Promise.all(d).then(D).catch(D)},e.invalidateQueries=function(n,i,s){var o,a,l,c=this,f=H(n,i,s),d=f[0],v=f[1],m=O({},d,{active:(o=(a=d.refetchActive)!=null?a:d.active)!=null?o:!0,inactive:(l=d.refetchInactive)!=null?l:!1});return _.batch(function(){return c.queryCache.findAll(d).forEach(function(h){h.invalidate()}),c.refetchQueries(m,v)})},e.refetchQueries=function(n,i,s){var o=this,a=H(n,i,s),l=a[0],c=a[1],f=_.batch(function(){return o.queryCache.findAll(l).map(function(v){return v.fetch(void 0,O({},c,{meta:{refetchPage:l?.refetchPage}}))})}),d=Promise.all(f).then(D);return c?.throwOnError||(d=d.catch(D)),d},e.fetchQuery=function(n,i,s){var o=Te(n,i,s),a=this.defaultQueryOptions(o);typeof a.retry>"u"&&(a.retry=!1);var l=this.queryCache.build(this,a);return l.isStaleByTime(a.staleTime)?l.fetch(a):Promise.resolve(l.state.data)},e.prefetchQuery=function(n,i,s){return this.fetchQuery(n,i,s).then(D).catch(D)},e.fetchInfiniteQuery=function(n,i,s){var o=Te(n,i,s);return o.behavior=hn(),this.fetchQuery(o)},e.prefetchInfiniteQuery=function(n,i,s){return this.fetchInfiniteQuery(n,i,s).then(D).catch(D)},e.cancelMutations=function(){var n=this,i=_.batch(function(){return n.mutationCache.getAll().map(function(s){return s.cancel()})});return Promise.all(i).then(D).catch(D)},e.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},e.executeMutation=function(n){return this.mutationCache.build(this,n).execute()},e.getQueryCache=function(){return this.queryCache},e.getMutationCache=function(){return this.mutationCache},e.getDefaultOptions=function(){return this.defaultOptions},e.setDefaultOptions=function(n){this.defaultOptions=n},e.setQueryDefaults=function(n,i){var s=this.queryDefaults.find(function(o){return z(n)===z(o.queryKey)});s?s.defaultOptions=i:this.queryDefaults.push({queryKey:n,defaultOptions:i})},e.getQueryDefaults=function(n){var i;return n?(i=this.queryDefaults.find(function(s){return be(n,s.queryKey)}))==null?void 0:i.defaultOptions:void 0},e.setMutationDefaults=function(n,i){var s=this.mutationDefaults.find(function(o){return z(n)===z(o.mutationKey)});s?s.defaultOptions=i:this.mutationDefaults.push({mutationKey:n,defaultOptions:i})},e.getMutationDefaults=function(n){var i;return n?(i=this.mutationDefaults.find(function(s){return be(n,s.mutationKey)}))==null?void 0:i.defaultOptions:void 0},e.defaultQueryOptions=function(n){if(n?._defaulted)return n;var i=O({},this.defaultOptions.queries,this.getQueryDefaults(n?.queryKey),n,{_defaulted:!0});return!i.queryHash&&i.queryKey&&(i.queryHash=$e(i.queryKey,i)),i},e.defaultQueryObserverOptions=function(n){return this.defaultQueryOptions(n)},e.defaultMutationOptions=function(n){return n?._defaulted?n:O({},this.defaultOptions.mutations,this.getMutationDefaults(n?.mutationKey),n,{_defaulted:!0})},e.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},t})(),pn=Jt.unstable_batchedUpdates;_.setBatchNotifyFunction(pn);var yn=console;sn(yn);var Ze=ie.createContext(void 0),mn=ie.createContext(!1);function vn(t){return t&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=Ze),window.ReactQueryClientContext):Ze}var Vr=function(e){var r=e.client,n=e.contextSharing,i=n===void 0?!1:n,s=e.children;ie.useEffect(function(){return r.mount(),function(){r.unmount()}},[r]);var o=vn(i);return ie.createElement(mn.Provider,{value:i},ie.createElement(o.Provider,{value:r},s))};function St(t,e){return function(){return t.apply(e,arguments)}}const{toString:bn}=Object.prototype,{getPrototypeOf:Ie}=Object,{iterator:Ce,toStringTag:Ct}=Symbol,Re=(t=>e=>{const r=bn.call(e);return t[r]||(t[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),j=t=>(t=t.toLowerCase(),e=>Re(e)===t),Ae=t=>e=>typeof e===t,{isArray:Y}=Array,Z=Ae("undefined");function se(t){return t!==null&&!Z(t)&&t.constructor!==null&&!Z(t.constructor)&&N(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const Rt=j("ArrayBuffer");function gn(t){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(t):e=t&&t.buffer&&Rt(t.buffer),e}const wn=Ae("string"),N=Ae("function"),At=Ae("number"),oe=t=>t!==null&&typeof t=="object",On=t=>t===!0||t===!1,pe=t=>{if(Re(t)!=="object")return!1;const e=Ie(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Ct in t)&&!(Ce in t)},En=t=>{if(!oe(t)||se(t))return!1;try{return Object.keys(t).length===0&&Object.getPrototypeOf(t)===Object.prototype}catch{return!1}},Sn=j("Date"),Cn=j("File"),Rn=j("Blob"),An=j("FileList"),Fn=t=>oe(t)&&N(t.pipe),Pn=t=>{let e;return t&&(typeof FormData=="function"&&t instanceof FormData||N(t.append)&&((e=Re(t))==="formdata"||e==="object"&&N(t.toString)&&t.toString()==="[object FormData]"))},_n=j("URLSearchParams"),[Tn,xn,qn,Dn]=["ReadableStream","Request","Response","Headers"].map(j),Nn=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ae(t,e,{allOwnKeys:r=!1}={}){if(t===null||typeof t>"u")return;let n,i;if(typeof t!="object"&&(t=[t]),Y(t))for(n=0,i=t.length;n<i;n++)e.call(null,t[n],n,t);else{if(se(t))return;const s=r?Object.getOwnPropertyNames(t):Object.keys(t),o=s.length;let a;for(n=0;n<o;n++)a=s[n],e.call(null,t[a],a,t)}}function Ft(t,e){if(se(t))return null;e=e.toLowerCase();const r=Object.keys(t);let n=r.length,i;for(;n-- >0;)if(i=r[n],e===i.toLowerCase())return i;return null}const J=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Pt=t=>!Z(t)&&t!==J;function Le(){const{caseless:t,skipUndefined:e}=Pt(this)&&this||{},r={},n=(i,s)=>{const o=t&&Ft(r,s)||s;pe(r[o])&&pe(i)?r[o]=Le(r[o],i):pe(i)?r[o]=Le({},i):Y(i)?r[o]=i.slice():(!e||!Z(i))&&(r[o]=i)};for(let i=0,s=arguments.length;i<s;i++)arguments[i]&&ae(arguments[i],n);return r}const Un=(t,e,r,{allOwnKeys:n}={})=>(ae(e,(i,s)=>{r&&N(i)?t[s]=St(i,r):t[s]=i},{allOwnKeys:n}),t),Mn=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),Ln=(t,e,r,n)=>{t.prototype=Object.create(e.prototype,n),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),r&&Object.assign(t.prototype,r)},jn=(t,e,r,n)=>{let i,s,o;const a={};if(e=e||{},t==null)return e;do{for(i=Object.getOwnPropertyNames(t),s=i.length;s-- >0;)o=i[s],(!n||n(o,t,e))&&!a[o]&&(e[o]=t[o],a[o]=!0);t=r!==!1&&Ie(t)}while(t&&(!r||r(t,e))&&t!==Object.prototype);return e},Bn=(t,e,r)=>{t=String(t),(r===void 0||r>t.length)&&(r=t.length),r-=e.length;const n=t.indexOf(e,r);return n!==-1&&n===r},Qn=t=>{if(!t)return null;if(Y(t))return t;let e=t.length;if(!At(e))return null;const r=new Array(e);for(;e-- >0;)r[e]=t[e];return r},kn=(t=>e=>t&&e instanceof t)(typeof Uint8Array<"u"&&Ie(Uint8Array)),$n=(t,e)=>{const n=(t&&t[Ce]).call(t);let i;for(;(i=n.next())&&!i.done;){const s=i.value;e.call(t,s[0],s[1])}},In=(t,e)=>{let r;const n=[];for(;(r=t.exec(e))!==null;)n.push(r);return n},Hn=j("HTMLFormElement"),Kn=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,i){return n.toUpperCase()+i}),Ye=(({hasOwnProperty:t})=>(e,r)=>t.call(e,r))(Object.prototype),zn=j("RegExp"),_t=(t,e)=>{const r=Object.getOwnPropertyDescriptors(t),n={};ae(r,(i,s)=>{let o;(o=e(i,s,t))!==!1&&(n[s]=o||i)}),Object.defineProperties(t,n)},Jn=t=>{_t(t,(e,r)=>{if(N(t)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=t[r];if(N(n)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Vn=(t,e)=>{const r={},n=i=>{i.forEach(s=>{r[s]=!0})};return Y(t)?n(t):n(String(t).split(e)),r},Wn=()=>{},Gn=(t,e)=>t!=null&&Number.isFinite(t=+t)?t:e;function Xn(t){return!!(t&&N(t.append)&&t[Ct]==="FormData"&&t[Ce])}const Zn=t=>{const e=new Array(10),r=(n,i)=>{if(oe(n)){if(e.indexOf(n)>=0)return;if(se(n))return n;if(!("toJSON"in n)){e[i]=n;const s=Y(n)?[]:{};return ae(n,(o,a)=>{const l=r(o,i+1);!Z(l)&&(s[a]=l)}),e[i]=void 0,s}}return n};return r(t,0)},Yn=j("AsyncFunction"),er=t=>t&&(oe(t)||N(t))&&N(t.then)&&N(t.catch),Tt=((t,e)=>t?setImmediate:e?((r,n)=>(J.addEventListener("message",({source:i,data:s})=>{i===J&&s===r&&n.length&&n.shift()()},!1),i=>{n.push(i),J.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",N(J.postMessage)),tr=typeof queueMicrotask<"u"?queueMicrotask.bind(J):typeof process<"u"&&process.nextTick||Tt,nr=t=>t!=null&&N(t[Ce]),u={isArray:Y,isArrayBuffer:Rt,isBuffer:se,isFormData:Pn,isArrayBufferView:gn,isString:wn,isNumber:At,isBoolean:On,isObject:oe,isPlainObject:pe,isEmptyObject:En,isReadableStream:Tn,isRequest:xn,isResponse:qn,isHeaders:Dn,isUndefined:Z,isDate:Sn,isFile:Cn,isBlob:Rn,isRegExp:zn,isFunction:N,isStream:Fn,isURLSearchParams:_n,isTypedArray:kn,isFileList:An,forEach:ae,merge:Le,extend:Un,trim:Nn,stripBOM:Mn,inherits:Ln,toFlatObject:jn,kindOf:Re,kindOfTest:j,endsWith:Bn,toArray:Qn,forEachEntry:$n,matchAll:In,isHTMLForm:Hn,hasOwnProperty:Ye,hasOwnProp:Ye,reduceDescriptors:_t,freezeMethods:Jn,toObjectSet:Vn,toCamelCase:Kn,noop:Wn,toFiniteNumber:Gn,findKey:Ft,global:J,isContextDefined:Pt,isSpecCompliantForm:Xn,toJSONObject:Zn,isAsyncFn:Yn,isThenable:er,setImmediate:Tt,asap:tr,isIterable:nr};function b(t,e,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}u.inherits(b,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:u.toJSONObject(this.config),code:this.code,status:this.status}}});const xt=b.prototype,qt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{qt[t]={value:t}});Object.defineProperties(b,qt);Object.defineProperty(xt,"isAxiosError",{value:!0});b.from=(t,e,r,n,i,s)=>{const o=Object.create(xt);u.toFlatObject(t,o,function(f){return f!==Error.prototype},c=>c!=="isAxiosError");const a=t&&t.message?t.message:"Error",l=e==null&&t?t.code:e;return b.call(o,a,l,r,n,i),t&&o.cause==null&&Object.defineProperty(o,"cause",{value:t,configurable:!0}),o.name=t&&t.name||"Error",s&&Object.assign(o,s),o};const rr=null;function je(t){return u.isPlainObject(t)||u.isArray(t)}function Dt(t){return u.endsWith(t,"[]")?t.slice(0,-2):t}function et(t,e,r){return t?t.concat(e).map(function(i,s){return i=Dt(i),!r&&s?"["+i+"]":i}).join(r?".":""):e}function ir(t){return u.isArray(t)&&!t.some(je)}const sr=u.toFlatObject(u,{},null,function(e){return/^is[A-Z]/.test(e)});function Fe(t,e,r){if(!u.isObject(t))throw new TypeError("target must be an object");e=e||new FormData,r=u.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,p){return!u.isUndefined(p[y])});const n=r.metaTokens,i=r.visitor||f,s=r.dots,o=r.indexes,l=(r.Blob||typeof Blob<"u"&&Blob)&&u.isSpecCompliantForm(e);if(!u.isFunction(i))throw new TypeError("visitor must be a function");function c(h){if(h===null)return"";if(u.isDate(h))return h.toISOString();if(u.isBoolean(h))return h.toString();if(!l&&u.isBlob(h))throw new b("Blob is not supported. Use a Buffer instead.");return u.isArrayBuffer(h)||u.isTypedArray(h)?l&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function f(h,y,p){let g=h;if(h&&!p&&typeof h=="object"){if(u.endsWith(y,"{}"))y=n?y:y.slice(0,-2),h=JSON.stringify(h);else if(u.isArray(h)&&ir(h)||(u.isFileList(h)||u.endsWith(y,"[]"))&&(g=u.toArray(h)))return y=Dt(y),g.forEach(function(w,C){!(u.isUndefined(w)||w===null)&&e.append(o===!0?et([y],C,s):o===null?y:y+"[]",c(w))}),!1}return je(h)?!0:(e.append(et(p,y,s),c(h)),!1)}const d=[],v=Object.assign(sr,{defaultVisitor:f,convertValue:c,isVisitable:je});function m(h,y){if(!u.isUndefined(h)){if(d.indexOf(h)!==-1)throw Error("Circular reference detected in "+y.join("."));d.push(h),u.forEach(h,function(g,E){(!(u.isUndefined(g)||g===null)&&i.call(e,g,u.isString(E)?E.trim():E,y,v))===!0&&m(g,y?y.concat(E):[E])}),d.pop()}}if(!u.isObject(t))throw new TypeError("data must be an object");return m(t),e}function tt(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(n){return e[n]})}function He(t,e){this._pairs=[],t&&Fe(t,this,e)}const Nt=He.prototype;Nt.append=function(e,r){this._pairs.push([e,r])};Nt.toString=function(e){const r=e?function(n){return e.call(this,n,tt)}:tt;return this._pairs.map(function(i){return r(i[0])+"="+r(i[1])},"").join("&")};function or(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+")}function Ut(t,e,r){if(!e)return t;const n=r&&r.encode||or;u.isFunction(r)&&(r={serialize:r});const i=r&&r.serialize;let s;if(i?s=i(e,r):s=u.isURLSearchParams(e)?e.toString():new He(e,r).toString(n),s){const o=t.indexOf("#");o!==-1&&(t=t.slice(0,o)),t+=(t.indexOf("?")===-1?"?":"&")+s}return t}class nt{constructor(){this.handlers=[]}use(e,r,n){return this.handlers.push({fulfilled:e,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){u.forEach(this.handlers,function(n){n!==null&&e(n)})}}const Mt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ar=typeof URLSearchParams<"u"?URLSearchParams:He,ur=typeof FormData<"u"?FormData:null,cr=typeof Blob<"u"?Blob:null,lr={isBrowser:!0,classes:{URLSearchParams:ar,FormData:ur,Blob:cr},protocols:["http","https","file","blob","url","data"]},Ke=typeof window<"u"&&typeof document<"u",Be=typeof navigator=="object"&&navigator||void 0,fr=Ke&&(!Be||["ReactNative","NativeScript","NS"].indexOf(Be.product)<0),hr=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",dr=Ke&&window.location.href||"http://localhost",pr=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ke,hasStandardBrowserEnv:fr,hasStandardBrowserWebWorkerEnv:hr,navigator:Be,origin:dr},Symbol.toStringTag,{value:"Module"})),x={...pr,...lr};function yr(t,e){return Fe(t,new x.classes.URLSearchParams,{visitor:function(r,n,i,s){return x.isNode&&u.isBuffer(r)?(this.append(n,r.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)},...e})}function mr(t){return u.matchAll(/\w+|\[(\w*)]/g,t).map(e=>e[0]==="[]"?"":e[1]||e[0])}function vr(t){const e={},r=Object.keys(t);let n;const i=r.length;let s;for(n=0;n<i;n++)s=r[n],e[s]=t[s];return e}function Lt(t){function e(r,n,i,s){let o=r[s++];if(o==="__proto__")return!0;const a=Number.isFinite(+o),l=s>=r.length;return o=!o&&u.isArray(i)?i.length:o,l?(u.hasOwnProp(i,o)?i[o]=[i[o],n]:i[o]=n,!a):((!i[o]||!u.isObject(i[o]))&&(i[o]=[]),e(r,n,i[o],s)&&u.isArray(i[o])&&(i[o]=vr(i[o])),!a)}if(u.isFormData(t)&&u.isFunction(t.entries)){const r={};return u.forEachEntry(t,(n,i)=>{e(mr(n),i,r,0)}),r}return null}function br(t,e,r){if(u.isString(t))try{return(e||JSON.parse)(t),u.trim(t)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(t)}const ue={transitional:Mt,adapter:["xhr","http","fetch"],transformRequest:[function(e,r){const n=r.getContentType()||"",i=n.indexOf("application/json")>-1,s=u.isObject(e);if(s&&u.isHTMLForm(e)&&(e=new FormData(e)),u.isFormData(e))return i?JSON.stringify(Lt(e)):e;if(u.isArrayBuffer(e)||u.isBuffer(e)||u.isStream(e)||u.isFile(e)||u.isBlob(e)||u.isReadableStream(e))return e;if(u.isArrayBufferView(e))return e.buffer;if(u.isURLSearchParams(e))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1)return yr(e,this.formSerializer).toString();if((a=u.isFileList(e))||n.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Fe(a?{"files[]":e}:e,l&&new l,this.formSerializer)}}return s||i?(r.setContentType("application/json",!1),br(e)):e}],transformResponse:[function(e){const r=this.transitional||ue.transitional,n=r&&r.forcedJSONParsing,i=this.responseType==="json";if(u.isResponse(e)||u.isReadableStream(e))return e;if(e&&u.isString(e)&&(n&&!this.responseType||i)){const o=!(r&&r.silentJSONParsing)&&i;try{return JSON.parse(e,this.parseReviver)}catch(a){if(o)throw a.name==="SyntaxError"?b.from(a,b.ERR_BAD_RESPONSE,this,null,this.response):a}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:x.classes.FormData,Blob:x.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};u.forEach(["delete","get","head","post","put","patch"],t=>{ue.headers[t]={}});const gr=u.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),wr=t=>{const e={};let r,n,i;return t&&t.split(`
`).forEach(function(o){i=o.indexOf(":"),r=o.substring(0,i).trim().toLowerCase(),n=o.substring(i+1).trim(),!(!r||e[r]&&gr[r])&&(r==="set-cookie"?e[r]?e[r].push(n):e[r]=[n]:e[r]=e[r]?e[r]+", "+n:n)}),e},rt=Symbol("internals");function re(t){return t&&String(t).trim().toLowerCase()}function ye(t){return t===!1||t==null?t:u.isArray(t)?t.map(ye):String(t)}function Or(t){const e=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(t);)e[n[1]]=n[2];return e}const Er=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function qe(t,e,r,n,i){if(u.isFunction(n))return n.call(this,e,r);if(i&&(e=r),!!u.isString(e)){if(u.isString(n))return e.indexOf(n)!==-1;if(u.isRegExp(n))return n.test(e)}}function Sr(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,r,n)=>r.toUpperCase()+n)}function Cr(t,e){const r=u.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(t,n+r,{value:function(i,s,o){return this[n].call(this,e,i,s,o)},configurable:!0})})}let U=class{constructor(e){e&&this.set(e)}set(e,r,n){const i=this;function s(a,l,c){const f=re(l);if(!f)throw new Error("header name must be a non-empty string");const d=u.findKey(i,f);(!d||i[d]===void 0||c===!0||c===void 0&&i[d]!==!1)&&(i[d||l]=ye(a))}const o=(a,l)=>u.forEach(a,(c,f)=>s(c,f,l));if(u.isPlainObject(e)||e instanceof this.constructor)o(e,r);else if(u.isString(e)&&(e=e.trim())&&!Er(e))o(wr(e),r);else if(u.isObject(e)&&u.isIterable(e)){let a={},l,c;for(const f of e){if(!u.isArray(f))throw TypeError("Object iterator must return a key-value pair");a[c=f[0]]=(l=a[c])?u.isArray(l)?[...l,f[1]]:[l,f[1]]:f[1]}o(a,r)}else e!=null&&s(r,e,n);return this}get(e,r){if(e=re(e),e){const n=u.findKey(this,e);if(n){const i=this[n];if(!r)return i;if(r===!0)return Or(i);if(u.isFunction(r))return r.call(this,i,n);if(u.isRegExp(r))return r.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,r){if(e=re(e),e){const n=u.findKey(this,e);return!!(n&&this[n]!==void 0&&(!r||qe(this,this[n],n,r)))}return!1}delete(e,r){const n=this;let i=!1;function s(o){if(o=re(o),o){const a=u.findKey(n,o);a&&(!r||qe(n,n[a],a,r))&&(delete n[a],i=!0)}}return u.isArray(e)?e.forEach(s):s(e),i}clear(e){const r=Object.keys(this);let n=r.length,i=!1;for(;n--;){const s=r[n];(!e||qe(this,this[s],s,e,!0))&&(delete this[s],i=!0)}return i}normalize(e){const r=this,n={};return u.forEach(this,(i,s)=>{const o=u.findKey(n,s);if(o){r[o]=ye(i),delete r[s];return}const a=e?Sr(s):String(s).trim();a!==s&&delete r[s],r[a]=ye(i),n[a]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const r=Object.create(null);return u.forEach(this,(n,i)=>{n!=null&&n!==!1&&(r[i]=e&&u.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,r])=>e+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...r){const n=new this(e);return r.forEach(i=>n.set(i)),n}static accessor(e){const n=(this[rt]=this[rt]={accessors:{}}).accessors,i=this.prototype;function s(o){const a=re(o);n[a]||(Cr(i,o),n[a]=!0)}return u.isArray(e)?e.forEach(s):s(e),this}};U.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);u.reduceDescriptors(U.prototype,({value:t},e)=>{let r=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(n){this[r]=n}}});u.freezeMethods(U);function De(t,e){const r=this||ue,n=e||r,i=U.from(n.headers);let s=n.data;return u.forEach(t,function(a){s=a.call(r,s,i.normalize(),e?e.status:void 0)}),i.normalize(),s}function jt(t){return!!(t&&t.__CANCEL__)}function ee(t,e,r){b.call(this,t??"canceled",b.ERR_CANCELED,e,r),this.name="CanceledError"}u.inherits(ee,b,{__CANCEL__:!0});function Bt(t,e,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?t(r):e(new b("Request failed with status code "+r.status,[b.ERR_BAD_REQUEST,b.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Rr(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}function Ar(t,e){t=t||10;const r=new Array(t),n=new Array(t);let i=0,s=0,o;return e=e!==void 0?e:1e3,function(l){const c=Date.now(),f=n[s];o||(o=c),r[i]=l,n[i]=c;let d=s,v=0;for(;d!==i;)v+=r[d++],d=d%t;if(i=(i+1)%t,i===s&&(s=(s+1)%t),c-o<e)return;const m=f&&c-f;return m?Math.round(v*1e3/m):void 0}}function Fr(t,e){let r=0,n=1e3/e,i,s;const o=(c,f=Date.now())=>{r=f,i=null,s&&(clearTimeout(s),s=null),t(...c)};return[(...c)=>{const f=Date.now(),d=f-r;d>=n?o(c,f):(i=c,s||(s=setTimeout(()=>{s=null,o(i)},n-d)))},()=>i&&o(i)]}const we=(t,e,r=3)=>{let n=0;const i=Ar(50,250);return Fr(s=>{const o=s.loaded,a=s.lengthComputable?s.total:void 0,l=o-n,c=i(l),f=o<=a;n=o;const d={loaded:o,total:a,progress:a?o/a:void 0,bytes:l,rate:c||void 0,estimated:c&&a&&f?(a-o)/c:void 0,event:s,lengthComputable:a!=null,[e?"download":"upload"]:!0};t(d)},r)},it=(t,e)=>{const r=t!=null;return[n=>e[0]({lengthComputable:r,total:t,loaded:n}),e[1]]},st=t=>(...e)=>u.asap(()=>t(...e)),Pr=x.hasStandardBrowserEnv?((t,e)=>r=>(r=new URL(r,x.origin),t.protocol===r.protocol&&t.host===r.host&&(e||t.port===r.port)))(new URL(x.origin),x.navigator&&/(msie|trident)/i.test(x.navigator.userAgent)):()=>!0,_r=x.hasStandardBrowserEnv?{write(t,e,r,n,i,s){const o=[t+"="+encodeURIComponent(e)];u.isNumber(r)&&o.push("expires="+new Date(r).toGMTString()),u.isString(n)&&o.push("path="+n),u.isString(i)&&o.push("domain="+i),s===!0&&o.push("secure"),document.cookie=o.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Tr(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function xr(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}function Qt(t,e,r){let n=!Tr(e);return t&&(n||r==!1)?xr(t,e):e}const ot=t=>t instanceof U?{...t}:t;function W(t,e){e=e||{};const r={};function n(c,f,d,v){return u.isPlainObject(c)&&u.isPlainObject(f)?u.merge.call({caseless:v},c,f):u.isPlainObject(f)?u.merge({},f):u.isArray(f)?f.slice():f}function i(c,f,d,v){if(u.isUndefined(f)){if(!u.isUndefined(c))return n(void 0,c,d,v)}else return n(c,f,d,v)}function s(c,f){if(!u.isUndefined(f))return n(void 0,f)}function o(c,f){if(u.isUndefined(f)){if(!u.isUndefined(c))return n(void 0,c)}else return n(void 0,f)}function a(c,f,d){if(d in e)return n(c,f);if(d in t)return n(void 0,c)}const l={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(c,f,d)=>i(ot(c),ot(f),d,!0)};return u.forEach(Object.keys({...t,...e}),function(f){const d=l[f]||i,v=d(t[f],e[f],f);u.isUndefined(v)&&d!==a||(r[f]=v)}),r}const kt=t=>{const e=W({},t);let{data:r,withXSRFToken:n,xsrfHeaderName:i,xsrfCookieName:s,headers:o,auth:a}=e;if(e.headers=o=U.from(o),e.url=Ut(Qt(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),a&&o.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):""))),u.isFormData(r)){if(x.hasStandardBrowserEnv||x.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if(u.isFunction(r.getHeaders)){const l=r.getHeaders(),c=["content-type","content-length"];Object.entries(l).forEach(([f,d])=>{c.includes(f.toLowerCase())&&o.set(f,d)})}}if(x.hasStandardBrowserEnv&&(n&&u.isFunction(n)&&(n=n(e)),n||n!==!1&&Pr(e.url))){const l=i&&s&&_r.read(s);l&&o.set(i,l)}return e},qr=typeof XMLHttpRequest<"u",Dr=qr&&function(t){return new Promise(function(r,n){const i=kt(t);let s=i.data;const o=U.from(i.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:c}=i,f,d,v,m,h;function y(){m&&m(),h&&h(),i.cancelToken&&i.cancelToken.unsubscribe(f),i.signal&&i.signal.removeEventListener("abort",f)}let p=new XMLHttpRequest;p.open(i.method.toUpperCase(),i.url,!0),p.timeout=i.timeout;function g(){if(!p)return;const w=U.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),A={data:!a||a==="text"||a==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:w,config:t,request:p};Bt(function(q){r(q),y()},function(q){n(q),y()},A),p=null}"onloadend"in p?p.onloadend=g:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(g)},p.onabort=function(){p&&(n(new b("Request aborted",b.ECONNABORTED,t,p)),p=null)},p.onerror=function(C){const A=C&&C.message?C.message:"Network Error",F=new b(A,b.ERR_NETWORK,t,p);F.event=C||null,n(F),p=null},p.ontimeout=function(){let C=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const A=i.transitional||Mt;i.timeoutErrorMessage&&(C=i.timeoutErrorMessage),n(new b(C,A.clarifyTimeoutError?b.ETIMEDOUT:b.ECONNABORTED,t,p)),p=null},s===void 0&&o.setContentType(null),"setRequestHeader"in p&&u.forEach(o.toJSON(),function(C,A){p.setRequestHeader(A,C)}),u.isUndefined(i.withCredentials)||(p.withCredentials=!!i.withCredentials),a&&a!=="json"&&(p.responseType=i.responseType),c&&([v,h]=we(c,!0),p.addEventListener("progress",v)),l&&p.upload&&([d,m]=we(l),p.upload.addEventListener("progress",d),p.upload.addEventListener("loadend",m)),(i.cancelToken||i.signal)&&(f=w=>{p&&(n(!w||w.type?new ee(null,t,p):w),p.abort(),p=null)},i.cancelToken&&i.cancelToken.subscribe(f),i.signal&&(i.signal.aborted?f():i.signal.addEventListener("abort",f)));const E=Rr(i.url);if(E&&x.protocols.indexOf(E)===-1){n(new b("Unsupported protocol "+E+":",b.ERR_BAD_REQUEST,t));return}p.send(s||null)})},Nr=(t,e)=>{const{length:r}=t=t?t.filter(Boolean):[];if(e||r){let n=new AbortController,i;const s=function(c){if(!i){i=!0,a();const f=c instanceof Error?c:this.reason;n.abort(f instanceof b?f:new ee(f instanceof Error?f.message:f))}};let o=e&&setTimeout(()=>{o=null,s(new b(`timeout ${e} of ms exceeded`,b.ETIMEDOUT))},e);const a=()=>{t&&(o&&clearTimeout(o),o=null,t.forEach(c=>{c.unsubscribe?c.unsubscribe(s):c.removeEventListener("abort",s)}),t=null)};t.forEach(c=>c.addEventListener("abort",s));const{signal:l}=n;return l.unsubscribe=()=>u.asap(a),l}},Ur=function*(t,e){let r=t.byteLength;if(r<e){yield t;return}let n=0,i;for(;n<r;)i=n+e,yield t.slice(n,i),n=i},Mr=async function*(t,e){for await(const r of Lr(t))yield*Ur(r,e)},Lr=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const e=t.getReader();try{for(;;){const{done:r,value:n}=await e.read();if(r)break;yield n}}finally{await e.cancel()}},at=(t,e,r,n)=>{const i=Mr(t,e);let s=0,o,a=l=>{o||(o=!0,n&&n(l))};return new ReadableStream({async pull(l){try{const{done:c,value:f}=await i.next();if(c){a(),l.close();return}let d=f.byteLength;if(r){let v=s+=d;r(v)}l.enqueue(new Uint8Array(f))}catch(c){throw a(c),c}},cancel(l){return a(l),i.return()}},{highWaterMark:2})},ut=64*1024,{isFunction:fe}=u,jr=(({Request:t,Response:e})=>({Request:t,Response:e}))(u.global),{ReadableStream:ct,TextEncoder:lt}=u.global,ft=(t,...e)=>{try{return!!t(...e)}catch{return!1}},Br=t=>{t=u.merge.call({skipUndefined:!0},jr,t);const{fetch:e,Request:r,Response:n}=t,i=e?fe(e):typeof fetch=="function",s=fe(r),o=fe(n);if(!i)return!1;const a=i&&fe(ct),l=i&&(typeof lt=="function"?(h=>y=>h.encode(y))(new lt):async h=>new Uint8Array(await new r(h).arrayBuffer())),c=s&&a&&ft(()=>{let h=!1;const y=new r(x.origin,{body:new ct,method:"POST",get duplex(){return h=!0,"half"}}).headers.has("Content-Type");return h&&!y}),f=o&&a&&ft(()=>u.isReadableStream(new n("").body)),d={stream:f&&(h=>h.body)};i&&["text","arrayBuffer","blob","formData","stream"].forEach(h=>{!d[h]&&(d[h]=(y,p)=>{let g=y&&y[h];if(g)return g.call(y);throw new b(`Response type '${h}' is not supported`,b.ERR_NOT_SUPPORT,p)})});const v=async h=>{if(h==null)return 0;if(u.isBlob(h))return h.size;if(u.isSpecCompliantForm(h))return(await new r(x.origin,{method:"POST",body:h}).arrayBuffer()).byteLength;if(u.isArrayBufferView(h)||u.isArrayBuffer(h))return h.byteLength;if(u.isURLSearchParams(h)&&(h=h+""),u.isString(h))return(await l(h)).byteLength},m=async(h,y)=>{const p=u.toFiniteNumber(h.getContentLength());return p??v(y)};return async h=>{let{url:y,method:p,data:g,signal:E,cancelToken:w,timeout:C,onDownloadProgress:A,onUploadProgress:F,responseType:q,headers:te,withCredentials:K="same-origin",fetchOptions:ce}=kt(h),ne=e||fetch;q=q?(q+"").toLowerCase():"text";let G=Nr([E,w&&w.toAbortSignal()],C),M=null;const T=G&&G.unsubscribe&&(()=>{G.unsubscribe()});let k;try{if(F&&c&&p!=="get"&&p!=="head"&&(k=await m(te,g))!==0){let L=new r(y,{method:"POST",body:g,duplex:"half"}),I;if(u.isFormData(g)&&(I=L.headers.get("content-type"))&&te.setContentType(I),L.body){const[_e,le]=it(k,we(st(F)));g=at(L.body,ut,_e,le)}}u.isString(K)||(K=K?"include":"omit");const S=s&&"credentials"in r.prototype,$={...ce,signal:G,method:p.toUpperCase(),headers:te.normalize().toJSON(),body:g,duplex:"half",credentials:S?K:void 0};M=s&&new r(y,$);let P=await(s?ne(M,ce):ne(y,$));const B=f&&(q==="stream"||q==="response");if(f&&(A||B&&T)){const L={};["status","statusText","headers"].forEach(ze=>{L[ze]=P[ze]});const I=u.toFiniteNumber(P.headers.get("content-length")),[_e,le]=A&&it(I,we(st(A),!0))||[];P=new n(at(P.body,ut,_e,()=>{le&&le(),T&&T()}),L)}q=q||"text";let X=await d[u.findKey(d,q)||"text"](P,h);return!B&&T&&T(),await new Promise((L,I)=>{Bt(L,I,{data:X,headers:U.from(P.headers),status:P.status,statusText:P.statusText,config:h,request:M})})}catch(S){throw T&&T(),S&&S.name==="TypeError"&&/Load failed|fetch/i.test(S.message)?Object.assign(new b("Network Error",b.ERR_NETWORK,h,M),{cause:S.cause||S}):b.from(S,S&&S.code,h,M)}}},Qr=new Map,$t=t=>{let e=t?t.env:{};const{fetch:r,Request:n,Response:i}=e,s=[n,i,r];let o=s.length,a=o,l,c,f=Qr;for(;a--;)l=s[a],c=f.get(l),c===void 0&&f.set(l,c=a?new Map:Br(e)),f=c;return c};$t();const Qe={http:rr,xhr:Dr,fetch:{get:$t}};u.forEach(Qe,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch{}Object.defineProperty(t,"adapterName",{value:e})}});const ht=t=>`- ${t}`,kr=t=>u.isFunction(t)||t===null||t===!1,It={getAdapter:(t,e)=>{t=u.isArray(t)?t:[t];const{length:r}=t;let n,i;const s={};for(let o=0;o<r;o++){n=t[o];let a;if(i=n,!kr(n)&&(i=Qe[(a=String(n)).toLowerCase()],i===void 0))throw new b(`Unknown adapter '${a}'`);if(i&&(u.isFunction(i)||(i=i.get(e))))break;s[a||"#"+o]=i}if(!i){const o=Object.entries(s).map(([l,c])=>`adapter ${l} `+(c===!1?"is not supported by the environment":"is not available in the build"));let a=r?o.length>1?`since :
`+o.map(ht).join(`
`):" "+ht(o[0]):"as no adapter specified";throw new b("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return i},adapters:Qe};function Ne(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new ee(null,t)}function dt(t){return Ne(t),t.headers=U.from(t.headers),t.data=De.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),It.getAdapter(t.adapter||ue.adapter,t)(t).then(function(n){return Ne(t),n.data=De.call(t,t.transformResponse,n),n.headers=U.from(n.headers),n},function(n){return jt(n)||(Ne(t),n&&n.response&&(n.response.data=De.call(t,t.transformResponse,n.response),n.response.headers=U.from(n.response.headers))),Promise.reject(n)})}const Ht="1.12.2",Pe={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{Pe[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}});const pt={};Pe.transitional=function(e,r,n){function i(s,o){return"[Axios v"+Ht+"] Transitional option '"+s+"'"+o+(n?". "+n:"")}return(s,o,a)=>{if(e===!1)throw new b(i(o," has been removed"+(r?" in "+r:"")),b.ERR_DEPRECATED);return r&&!pt[o]&&(pt[o]=!0,console.warn(i(o," has been deprecated since v"+r+" and will be removed in the near future"))),e?e(s,o,a):!0}};Pe.spelling=function(e){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};function $r(t,e,r){if(typeof t!="object")throw new b("options must be an object",b.ERR_BAD_OPTION_VALUE);const n=Object.keys(t);let i=n.length;for(;i-- >0;){const s=n[i],o=e[s];if(o){const a=t[s],l=a===void 0||o(a,s,t);if(l!==!0)throw new b("option "+s+" must be "+l,b.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new b("Unknown option "+s,b.ERR_BAD_OPTION)}}const me={assertOptions:$r,validators:Pe},Q=me.validators;let V=class{constructor(e){this.defaults=e||{},this.interceptors={request:new nt,response:new nt}}async request(e,r){try{return await this._request(e,r)}catch(n){if(n instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const s=i.stack?i.stack.replace(/^.+\n/,""):"";try{n.stack?s&&!String(n.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+s):n.stack=s}catch{}}throw n}}_request(e,r){typeof e=="string"?(r=r||{},r.url=e):r=e||{},r=W(this.defaults,r);const{transitional:n,paramsSerializer:i,headers:s}=r;n!==void 0&&me.assertOptions(n,{silentJSONParsing:Q.transitional(Q.boolean),forcedJSONParsing:Q.transitional(Q.boolean),clarifyTimeoutError:Q.transitional(Q.boolean)},!1),i!=null&&(u.isFunction(i)?r.paramsSerializer={serialize:i}:me.assertOptions(i,{encode:Q.function,serialize:Q.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),me.assertOptions(r,{baseUrl:Q.spelling("baseURL"),withXsrfToken:Q.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let o=s&&u.merge(s.common,s[r.method]);s&&u.forEach(["delete","get","head","post","put","patch","common"],h=>{delete s[h]}),r.headers=U.concat(o,s);const a=[];let l=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(r)===!1||(l=l&&y.synchronous,a.unshift(y.fulfilled,y.rejected))});const c=[];this.interceptors.response.forEach(function(y){c.push(y.fulfilled,y.rejected)});let f,d=0,v;if(!l){const h=[dt.bind(this),void 0];for(h.unshift(...a),h.push(...c),v=h.length,f=Promise.resolve(r);d<v;)f=f.then(h[d++],h[d++]);return f}v=a.length;let m=r;for(;d<v;){const h=a[d++],y=a[d++];try{m=h(m)}catch(p){y.call(this,p);break}}try{f=dt.call(this,m)}catch(h){return Promise.reject(h)}for(d=0,v=c.length;d<v;)f=f.then(c[d++],c[d++]);return f}getUri(e){e=W(this.defaults,e);const r=Qt(e.baseURL,e.url,e.allowAbsoluteUrls);return Ut(r,e.params,e.paramsSerializer)}};u.forEach(["delete","get","head","options"],function(e){V.prototype[e]=function(r,n){return this.request(W(n||{},{method:e,url:r,data:(n||{}).data}))}});u.forEach(["post","put","patch"],function(e){function r(n){return function(s,o,a){return this.request(W(a||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:s,data:o}))}}V.prototype[e]=r(),V.prototype[e+"Form"]=r(!0)});let Ir=class Kt{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(s){r=s});const n=this;this.promise.then(i=>{if(!n._listeners)return;let s=n._listeners.length;for(;s-- >0;)n._listeners[s](i);n._listeners=null}),this.promise.then=i=>{let s;const o=new Promise(a=>{n.subscribe(a),s=a}).then(i);return o.cancel=function(){n.unsubscribe(s)},o},e(function(s,o,a){n.reason||(n.reason=new ee(s,o,a),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const r=this._listeners.indexOf(e);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const e=new AbortController,r=n=>{e.abort(n)};return this.subscribe(r),e.signal.unsubscribe=()=>this.unsubscribe(r),e.signal}static source(){let e;return{token:new Kt(function(i){e=i}),cancel:e}}};function Hr(t){return function(r){return t.apply(null,r)}}function Kr(t){return u.isObject(t)&&t.isAxiosError===!0}const ke={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ke).forEach(([t,e])=>{ke[e]=t});function zt(t){const e=new V(t),r=St(V.prototype.request,e);return u.extend(r,V.prototype,e,{allOwnKeys:!0}),u.extend(r,e,null,{allOwnKeys:!0}),r.create=function(i){return zt(W(t,i))},r}const R=zt(ue);R.Axios=V;R.CanceledError=ee;R.CancelToken=Ir;R.isCancel=jt;R.VERSION=Ht;R.toFormData=Fe;R.AxiosError=b;R.Cancel=R.CanceledError;R.all=function(e){return Promise.all(e)};R.spread=Hr;R.isAxiosError=Kr;R.mergeConfig=W;R.AxiosHeaders=U;R.formToJSON=t=>Lt(u.isHTMLForm(t)?new FormData(t):t);R.getAdapter=It.getAdapter;R.HttpStatusCode=ke;R.default=R;const{Axios:Xr,AxiosError:Zr,CanceledError:Yr,isCancel:ei,CancelToken:ti,VERSION:ni,all:ri,Cancel:ii,isAxiosError:si,spread:oi,toFormData:ai,AxiosHeaders:ui,HttpStatusCode:ci,formToJSON:li,getAdapter:fi,mergeConfig:hi}=R;export{Jr as Q,R as a,Vr as b};
//# sourceMappingURL=http-B_AKd_mx.js.map
