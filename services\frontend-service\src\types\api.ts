// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  statusCode: number;
}

export interface ApiError {
  success: false;
  message: string;
  error: string;
  statusCode: number;
  details?: any;
}

// Pagination Types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'user' | 'premium' | 'enterprise';
  subscriptionTier: 'free' | 'basic' | 'premium' | 'enterprise';
  isVerified: boolean;
  isActive: boolean;
  isSuspended: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  avatar?: string;
  googleId?: string;
  analytics?: {
    jobApplications: number;
    resumesCreated: number;
    profileViews: number;
    lastActivityAt: string;
  };
}

export interface UserProfile {
  id: string;
  userId: string;
  bio?: string;
  phoneNumber?: string;
  location?: {
    country?: string;
    state?: string;
    city?: string;
    zipCode?: string;
    coordinates?: { lat: number; lng: number };
    remote?: boolean;
  };
  website?: string;
  linkedin?: string;
  github?: string;
  portfolio?: string;
  currentPosition?: string;
  currentCompany?: string;
  yearsOfExperience?: number;
  expectedSalary?: { min?: number; max?: number; currency?: string };
  dateOfBirth?: string;
  nationality?: string;
  languages?: Array<{ language: string; proficiency: 'basic' | 'conversational' | 'fluent' | 'native' }>;
  skills?: Array<{ name: string; level: 'beginner' | 'intermediate' | 'advanced' | 'expert'; verified?: boolean; yearsOfExperience?: number }>;
  education?: Array<{ institution: string; degree: string; field: string; startDate?: string; endDate?: string; gpa?: number; description?: string }>;
  experience?: Array<{ company: string; position: string; startDate?: string; endDate?: string; current?: boolean; description?: string; skills?: string[]; achievements?: string[] }>;
  profileVisibility?: 'public' | 'private' | 'connections';
  searchable?: boolean;
  createdAt: string;
  updatedAt: string;
}

// Job Types
export interface Job {
  id: string;
  title: string;
  company: string;
  description: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship' | 'remote';
  salary?: {
    min?: number;
    max?: number;
    currency?: string;
    period?: 'hourly' | 'monthly' | 'yearly';
  };
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  skills: string[];
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  remote: boolean;
  postedBy: string;
  postedAt: string;
  expiresAt?: string;
  status: 'active' | 'paused' | 'closed' | 'draft';
  applicationsCount: number;
  viewsCount: number;
  tags: string[];
  isUrgent: boolean;
  isFeatured: boolean;
  companyLogo?: string;
  companyWebsite?: string;
  companySize?: string;
  industry?: string;
}

export interface CreateJobRequest {
  title: string;
  company: string;
  description: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship' | 'remote';
  salary?: {
    min?: number;
    max?: number;
    currency?: string;
    period?: 'hourly' | 'monthly' | 'yearly';
  };
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  skills: string[];
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  remote: boolean;
  expiresAt?: string;
  tags: string[];
  isUrgent?: boolean;
  isFeatured?: boolean;
  companyWebsite?: string;
  companySize?: string;
  industry?: string;
}

export interface JobSearchParams {
  query?: string;
  location?: string;
  type?: string;
  experienceLevel?: string;
  remote?: boolean;
  salaryMin?: number;
  salaryMax?: number;
  skills?: string[];
  tags?: string[];
  company?: string;
  industry?: string;
  postedAfter?: string;
  postedBefore?: string;
  page?: number;
  limit?: number;
  sortBy?: 'relevance' | 'date' | 'salary' | 'company';
  sortOrder?: 'asc' | 'desc';
}

// Resume Types
export interface Resume {
  id: string;
  userId: string;
  title: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
  content: ResumeContent;
  analysis?: ResumeAnalysis;
  isPublic: boolean;
  isDefault: boolean;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  lastUsedAt?: string;
  downloadCount: number;
  viewCount: number;
}

export interface ResumeContent {
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    location?: string;
    website?: string;
    linkedin?: string;
    github?: string;
  };
  summary?: string;
  experience: Array<{
    company: string;
    position: string;
    startDate: string;
    endDate?: string;
    current: boolean;
    description: string;
    achievements: string[];
    skills: string[];
  }>;
  education: Array<{
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate?: string;
    gpa?: number;
    description?: string;
  }>;
  skills: Array<{
    name: string;
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    category: string;
  }>;
  certifications?: Array<{
    name: string;
    issuer: string;
    date: string;
    expiryDate?: string;
  }>;
  languages?: Array<{
    language: string;
    proficiency: 'basic' | 'conversational' | 'fluent' | 'native';
  }>;
  projects?: Array<{
    name: string;
    description: string;
    technologies: string[];
    startDate: string;
    endDate?: string;
    url?: string;
  }>;
}

export interface ResumeAnalysis {
  overallScore: number;
  atsScore: number;
  readabilityScore: number;
  keywordMatch: number;
  suggestions: string[];
  strengths: string[];
  weaknesses: string[];
  missingKeywords: string[];
  recommendedImprovements: string[];
  lastAnalyzed: string;
}

// Application Types
export interface Application {
  id: string;
  jobId: string;
  userId: string;
  resumeId: string;
  status: 'pending' | 'reviewed' | 'shortlisted' | 'interviewed' | 'rejected' | 'accepted' | 'withdrawn';
  appliedAt: string;
  updatedAt: string;
  notes?: string;
  coverLetter?: string;
  customAnswers?: Record<string, string>;
  interviewScheduledAt?: string;
  interviewNotes?: string;
  rejectionReason?: string;
  salaryExpectation?: number;
  availabilityDate?: string;
  isReferred: boolean;
  referredBy?: string;
  source: 'direct' | 'job-board' | 'referral' | 'recruiter';
  trackingData?: {
    views: number;
    downloads: number;
    lastViewedAt?: string;
  };
}

export interface CreateApplicationRequest {
  jobId: string;
  resumeId: string;
  coverLetter?: string;
  customAnswers?: Record<string, string>;
  salaryExpectation?: number;
  availabilityDate?: string;
  isReferred?: boolean;
  referredBy?: string;
}

// Auth Types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  agreeToTerms: boolean;
  subscribeToNewsletter?: boolean;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: User;
  accessToken?: string;
  refreshToken?: string;
  tokens?: AuthTokens;
  expiresIn?: number;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  confirmPassword: string;
}

// Dashboard Types
export interface DashboardStats {
  totalApplications: number;
  pendingApplications: number;
  interviewsScheduled: number;
  offersReceived: number;
  resumesCreated: number;
  profileViews: number;
  jobsSaved: number;
  applicationsThisWeek: number;
  applicationsThisMonth: number;
  averageResponseTime: number;
  successRate: number;
}

export interface RecentActivity {
  id: string;
  type: 'application' | 'interview' | 'offer' | 'resume' | 'profile';
  title: string;
  description: string;
  timestamp: string;
  status?: string;
  relatedId?: string;
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  type: 'application' | 'interview' | 'offer' | 'system' | 'marketing';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  readAt?: string;
  actionUrl?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  metadata?: Record<string, any>;
}

// Settings Types
export interface UserSettings {
  notifications: {
    email: {
      jobAlerts: boolean;
      applicationUpdates: boolean;
      marketingEmails: boolean;
      weeklyDigest: boolean;
    };
    push: {
      jobAlerts: boolean;
      applicationUpdates: boolean;
      messages: boolean;
    };
    sms: {
      criticalUpdates: boolean;
      jobAlerts: boolean;
    };
  };
  privacy: {
    showProfile: boolean;
    showSalaryExpectations: boolean;
    allowRecruiterContact: boolean;
    showApplicationHistory: boolean;
  };
  interface: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
    dateFormat: string;
  };
}

// Search Types
export interface SearchFilters {
  query?: string;
  location?: string;
  type?: string[];
  experienceLevel?: string[];
  remote?: boolean;
  salaryRange?: {
    min?: number;
    max?: number;
  };
  skills?: string[];
  tags?: string[];
  company?: string;
  industry?: string;
  postedAfter?: string;
  postedBefore?: string;
}

export interface SearchResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  filters: SearchFilters;
  suggestions?: string[];
  relatedSearches?: string[];
}

// Additional Types
export interface ApplicationSearchParams {
  jobId?: string;
  userId?: string;
  status?: string;
  appliedAfter?: string;
  appliedBefore?: string;
  page?: number;
  limit?: number;
  sortBy?: 'appliedAt' | 'status' | 'jobTitle';
  sortOrder?: 'asc' | 'desc';
}

export interface UserAnalytics {
  profileViews: number;
  searchAppearances: number;
  applicationsSent: number;
  interviewsScheduled: number;
  offersReceived: number;
  loginStreak: number;
  totalLogins: number;
  averageSessionDuration: number;
  lastActiveAt?: string;
  featuresUsed?: string[];
  premiumFeaturesUsed?: string[];
  responseRate: number;
  interviewRate: number;
  offerRate: number;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// Extended Application interface
export interface ApplicationWithTimeline extends Application {
  timeline: Array<{
    id: string;
    status: string;
    timestamp: string;
    note?: string;
    updatedBy?: string;
  }>;
}