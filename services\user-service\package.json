{"name": "@job-platform/user-service", "version": "1.0.0", "description": "User management service for job application platform", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "prestart": "npm install --production", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "setup": "npm install"}, "dependencies": {"compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "helmet": "^8.1.0", "ioredis": "^5.7.0", "mongoose": "^8.18.1", "morgan": "^1.10.1", "winston": "^3.17.0", "zod": "^4.1.11"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/morgan": "^1.9.10", "@types/node": "^24.5.2", "jest": "^30.1.3", "ts-jest": "^29.4.4", "tsx": "^4.20.5", "typescript": "^5.9.2"}, "engines": {"node": ">=22.18.0", "npm": ">=10.0.0"}}