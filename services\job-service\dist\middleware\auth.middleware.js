"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.authenticateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const environment_1 = require("../config/environment");
const response_1 = require("../utils/response");
const logger_1 = require("../utils/logger");
/**
 * JWT Authentication Middleware
 * Extracts and verifies JWT token from Authorization header
 * Sets req.user with authenticated user information
 */
const authenticateToken = (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
        if (!token) {
            const response = response_1.ResponseUtil.error('Access token required', 401);
            res.status(response.statusCode).json(response);
            return;
        }
        // Verify the token
        const decoded = jsonwebtoken_1.default.verify(token, environment_1.jwtConfig.secret);
        // Check if token has required fields
        if (!decoded.userId || !decoded.email) {
            const response = response_1.ResponseUtil.error('Invalid token payload', 401);
            res.status(response.statusCode).json(response);
            return;
        }
        // Set user information in request
        req.user = {
            id: decoded.userId,
            email: decoded.email,
            role: decoded.role,
            sessionId: decoded.sessionId,
        };
        logger_1.logger.debug('User authenticated successfully:', {
            userId: decoded.userId,
            email: decoded.email,
        });
        next();
    }
    catch (error) {
        logger_1.logger.warn('JWT authentication failed:', error);
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            const response = response_1.ResponseUtil.error('Token expired', 401);
            res.status(response.statusCode).json(response);
            return;
        }
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            const response = response_1.ResponseUtil.error('Invalid token', 401);
            res.status(response.statusCode).json(response);
            return;
        }
        const response = response_1.ResponseUtil.error('Authentication failed', 401);
        res.status(response.statusCode).json(response);
    }
};
exports.authenticateToken = authenticateToken;
/**
 * Optional Authentication Middleware
 * Similar to authenticateToken but doesn't fail if no token is provided
 * Useful for endpoints that work for both authenticated and unauthenticated users
 */
const optionalAuth = (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        if (!token) {
            // No token provided, continue without authentication
            next();
            return;
        }
        // Verify the token
        const decoded = jsonwebtoken_1.default.verify(token, environment_1.jwtConfig.secret);
        if (decoded.userId && decoded.email) {
            req.user = {
                id: decoded.userId,
                email: decoded.email,
                role: decoded.role,
                sessionId: decoded.sessionId,
            };
        }
        next();
    }
    catch (error) {
        // Token is invalid, but we continue without authentication
        logger_1.logger.debug('Optional auth failed, continuing without authentication:', error);
        next();
    }
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.middleware.js.map