"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRateLimitMiddleware = exports.rateLimitMiddleware = void 0;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const response_1 = require("../utils/response");
exports.rateLimitMiddleware = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: response_1.ResponseUtil.error('Too many requests from this IP, please try again later', 429, ['Rate limit exceeded']),
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        res
            .status(429)
            .json(response_1.ResponseUtil.error('Too many requests from this IP, please try again later', 429, ['Rate limit exceeded'], req.path));
    },
});
exports.authRateLimitMiddleware = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // Limit each IP to 10 auth requests per windowMs
    message: response_1.ResponseUtil.error('Too many authentication attempts, please try again later', 429, ['Auth rate limit exceeded']),
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: true,
    handler: (req, res) => {
        res
            .status(429)
            .json(response_1.ResponseUtil.error('Too many authentication attempts, please try again later', 429, ['Auth rate limit exceeded'], req.path));
    },
});
//# sourceMappingURL=rate-limit.middleware.js.map