export declare class RedisClient {
    private client;
    private isConnected;
    constructor();
    private setupEventHandlers;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    isHealthy(): boolean;
    private safeExecute;
    blacklistToken(token: string, expiresIn: number): Promise<void>;
    isTokenBlacklisted(token: string): Promise<boolean>;
    setSession(sessionId: string, data: Record<string, unknown>, ttl: number): Promise<void>;
    getSession(sessionId: string): Promise<unknown>;
    deleteSession(sessionId: string): Promise<void>;
    updateSessionExpiry(sessionId: string, ttl: number): Promise<void>;
    incrementCounter(key: string, window: number): Promise<number>;
    incrementFailedAttempts(identifier: string, window: number): Promise<number>;
    resetFailedAttempts(identifier: string): Promise<void>;
    lockAccount(identifier: string, lockDuration: number): Promise<void>;
    isAccountLocked(identifier: string): Promise<boolean>;
    setPasswordResetToken(userId: string, token: string, ttl: number): Promise<void>;
    getPasswordResetToken(userId: string): Promise<string | null>;
    deletePasswordResetToken(userId: string): Promise<void>;
    setEmailVerificationToken(email: string, token: string, ttl: number): Promise<void>;
    getEmailVerificationToken(email: string): Promise<string | null>;
    deleteEmailVerificationToken(email: string): Promise<void>;
    set(key: string, value: unknown, ttl?: number): Promise<void>;
    get(key: string): Promise<unknown>;
    del(key: string): Promise<void>;
    exists(key: string): Promise<boolean>;
}
//# sourceMappingURL=redis.service.d.ts.map