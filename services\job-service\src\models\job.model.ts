import { Schema, model } from 'mongoose';

const jobSchema = new Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    company: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
    },
    location: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: ['full_time', 'part_time', 'contract', 'internship', 'freelance'],
      required: true,
    },
    salary: {
      min: Number,
      max: Number,
      currency: {
        type: String,
        default: 'USD',
      },
      range: String, // e.g., "$100k - $150k"
    },
    requirements: [String],
    benefits: [String],
    skills: [String],
    experience: {
      min: Number,
      max: Number,
    },
    education: {
      type: String,
      enum: ['high_school', 'associate', 'bachelor', 'master', 'phd', 'any'],
      default: 'any',
    },
    remote: {
      type: Boolean,
      default: false,
    },
    status: {
      type: String,
      enum: ['active', 'paused', 'closed', 'draft'],
      default: 'active',
    },
    postedBy: {
      type: String,
      required: true,
    },
    expiresAt: Date,
    applicationDeadline: Date,
    tags: [String],
    isFeatured: {
      type: Boolean,
      default: false,
    },
    applicationCount: {
      type: Number,
      default: 0,
    },
    viewCount: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown> {
        ret.id = (ret._id as { toString: () => string }).toString();
        delete ret._id;
        return ret;
      },
    },
  }
);

// Indexes
jobSchema.index({ status: 1, createdAt: -1 });
jobSchema.index({ company: 1 });
jobSchema.index({ location: 1 });
jobSchema.index({ type: 1 });
jobSchema.index({ skills: 1 });
jobSchema.index({ postedBy: 1 });

export const Job = model('Job', jobSchema);
