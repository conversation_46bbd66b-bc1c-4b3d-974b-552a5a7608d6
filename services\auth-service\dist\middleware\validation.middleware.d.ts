import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
export declare const validateBody: (schema: z.ZodType<unknown>) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateQuery: (schema: z.ZodType<unknown>) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateParams: (schema: z.ZodType<unknown>) => (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=validation.middleware.d.ts.map