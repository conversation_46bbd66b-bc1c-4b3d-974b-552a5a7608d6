import { z } from 'zod';
import dotenv from 'dotenv';

dotenv.config();

const envSchema = z.object({
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),
  PORT: z.string().default('3001'),
  MONGODB_URI: z.string().default('mongodb://localhost:27017/job_platform'),
  REDIS_URL: z.string().default('redis://localhost:6379'),
  JWT_SECRET: z.string().default('your-super-secret-jwt-key'),
  JWT_REFRESH_SECRET: z.string().default('your-super-secret-refresh-key'),
  JWT_ACCESS_EXPIRES_IN: z.string().default('15m'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),
  BCRYPT_ROUNDS: z.string().default('12'),
  EMAIL_SERVICE_URL: z.string().default('http://notification-service:8080'),
  USER_SERVICE_URL: z.string().default('http://user-service:8080'),
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),
  CORS_ORIGIN: z.string().default('*'),
  LOG_LEVEL: z.string().default('info'),
  GOOGLE_CALLBACK_URL: z
    .string()
    .default('https://auth-service.ondigitalocean.app/api/v1/auth/google/callback'),
  FRONTEND_URL: z
    .string()
    .default('https://jobs-app-ydwim.ondigitalocean.app'),
});

export const env = envSchema.parse(process.env);

export const appConfig = {
  port: parseInt(env.PORT),
  nodeEnv: env.NODE_ENV,
  corsOrigin: env.CORS_ORIGIN,
  apiVersion: '1.0.0',
  logLevel: env.LOG_LEVEL,
  isDevelopment: env.NODE_ENV === 'development',
  isProduction: env.NODE_ENV === 'production',
};

export const databaseConfig = {
  mongoUri: env.MONGODB_URI,
  redisUrl: env.REDIS_URL,
};

export const jwtConfig = {
  secret: env.JWT_SECRET,
  refreshSecret: env.JWT_REFRESH_SECRET,
  accessExpiresIn: env.JWT_ACCESS_EXPIRES_IN,
  refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
};

export const serviceConfig = {
  emailServiceUrl: env.EMAIL_SERVICE_URL,
  userServiceUrl: env.USER_SERVICE_URL,
};

export const googleConfig = {
  clientId: env.GOOGLE_CLIENT_ID,
  clientSecret: env.GOOGLE_CLIENT_SECRET,
  callbackURL: env.GOOGLE_CALLBACK_URL,
};
