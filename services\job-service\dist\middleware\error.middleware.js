"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = void 0;
const errors_1 = require("../utils/errors");
const response_1 = require("../utils/response");
const logger_1 = require("../utils/logger");
const errorHandler = (error, req, res) => {
    logger_1.logger.error('Error occurred:', {
        message: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
    });
    // Handle known operational errors
    if (error instanceof errors_1.BaseError) {
        res
            .status(error.statusCode)
            .json(response_1.ResponseUtil.error(error.message, error.statusCode, [error.message], req.path));
        return;
    }
    // Handle Mongoose validation errors
    if (error.name === 'ValidationError') {
        const validationError = error;
        const validationErrors = Object.values(validationError.errors).map((err) => err.message);
        res
            .status(400)
            .json(response_1.ResponseUtil.error('Validation failed', 400, validationErrors, req.path));
        return;
    }
    // Handle Mongoose cast errors
    if (error.name === 'CastError') {
        res
            .status(400)
            .json(response_1.ResponseUtil.error('Invalid data format', 400, ['Invalid ID format'], req.path));
        return;
    }
    // Handle JWT errors
    if (error.name === 'JsonWebTokenError') {
        res
            .status(401)
            .json(response_1.ResponseUtil.error('Invalid token', 401, ['Token is invalid'], req.path));
        return;
    }
    if (error.name === 'TokenExpiredError') {
        res
            .status(401)
            .json(response_1.ResponseUtil.error('Token expired', 401, ['Token has expired'], req.path));
        return;
    }
    // Handle MongoDB duplicate key errors
    const mongoError = error;
    if (mongoError.code === 11000) {
        const field = Object.keys(mongoError.keyValue ?? {})[0];
        res
            .status(409)
            .json(response_1.ResponseUtil.error('Resource already exists', 409, [`${field} already exists`], req.path));
        return;
    }
    // Handle unknown errors
    res
        .status(500)
        .json(response_1.ResponseUtil.error('Internal server error', 500, process.env.NODE_ENV === 'development'
        ? [error.message]
        : ['Something went wrong'], req.path));
};
exports.errorHandler = errorHandler;
//# sourceMappingURL=error.middleware.js.map