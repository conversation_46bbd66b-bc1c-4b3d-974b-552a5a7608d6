"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRoutes = void 0;
const express_1 = require("express");
const passport_1 = __importDefault(require("passport"));
const zod_1 = require("zod");
const validation_middleware_1 = require("../middleware/validation.middleware");
const validation_1 = require("../utils/validation");
// import { ResponseUtil } from '../utils/response';
const auth_controller_1 = require("../controllers/auth.controller");
const router = (0, express_1.Router)();
exports.authRoutes = router;
const authController = new auth_controller_1.AuthController();
// Registration
router.post('/register', (0, validation_middleware_1.validateBody)(validation_1.UserValidationSchemas.register), authController.register);
// Login
router.post('/login', (0, validation_middleware_1.validateBody)(validation_1.UserValidationSchemas.login), authController.login);
// Google OAuth
router.get('/google', passport_1.default.authenticate('google', {
    scope: ['profile', 'email'],
}));
router.get('/google/callback', passport_1.default.authenticate('google', { session: false }), authController.googleCallback);
// Token refresh
router.post('/refresh', (0, validation_middleware_1.validateBody)(validation_1.CommonValidationSchemas.refreshToken), authController.refreshToken);
// Logout
router.post('/logout', authController.logout);
// Email verification
router.post('/verify-email', (0, validation_middleware_1.validateBody)(zod_1.z.object({
    email: zod_1.z.email(),
    token: zod_1.z.string().min(1),
})), authController.verifyEmail);
// Password reset
router.post('/forgot-password', (0, validation_middleware_1.validateBody)(validation_1.UserValidationSchemas.resetPassword), authController.forgotPassword);
router.post('/reset-password', (0, validation_middleware_1.validateBody)(validation_1.UserValidationSchemas.confirmResetPassword), authController.resetPassword);
// Change password (authenticated)
router.post('/change-password', (0, validation_middleware_1.validateBody)(validation_1.UserValidationSchemas.changePassword), authController.changePassword);
// Resend verification email
router.post('/resend-verification', (0, validation_middleware_1.validateBody)(zod_1.z.object({
    email: zod_1.z.email(),
})), authController.resendVerification);
//# sourceMappingURL=auth.routes.js.map