import{r as Zs,a as Ys,g as Xs}from"./vendor-D3F3s8fL.js";import{r as g,u as Qe,N as Ue,R as ze,b as es,L as E,c as Ks,B as Qs,d as et,e as $}from"./router-CTBu-pz9.js";import{a as st,Q as tt,b as at}from"./http-B_AKd_mx.js";import{u as ss}from"./forms-BcM1ev41.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const l of i)if(l.type==="childList")for(const o of l.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function a(i){const l={};return i.integrity&&(l.integrity=i.integrity),i.referrerPolicy&&(l.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?l.credentials="include":i.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(i){if(i.ep)return;i.ep=!0;const l=a(i);fetch(i.href,l)}})();var Le={exports:{}},me={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hs;function rt(){if(hs)return me;hs=1;var s=Zs(),t=Symbol.for("react.element"),a=Symbol.for("react.fragment"),r=Object.prototype.hasOwnProperty,i=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function o(c,n,h){var d,p={},N=null,u=null;h!==void 0&&(N=""+h),n.key!==void 0&&(N=""+n.key),n.ref!==void 0&&(u=n.ref);for(d in n)r.call(n,d)&&!l.hasOwnProperty(d)&&(p[d]=n[d]);if(c&&c.defaultProps)for(d in n=c.defaultProps,n)p[d]===void 0&&(p[d]=n[d]);return{$$typeof:t,type:c,key:N,ref:u,props:p,_owner:i.current}}return me.Fragment=a,me.jsx=o,me.jsxs=o,me}var ps;function it(){return ps||(ps=1,Le.exports=rt()),Le.exports}var e=it(),be={},us;function lt(){if(us)return be;us=1;var s=Ys();return be.createRoot=s.createRoot,be.hydrateRoot=s.hydrateRoot,be}var ot=lt();const nt=Xs(ot);let ct={data:""},dt=s=>typeof window=="object"?((s?s.querySelector("#_goober"):window._goober)||Object.assign((s||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:s||ct,mt=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,xt=/\/\*[^]*?\*\/|  +/g,gs=/\n+/g,Y=(s,t)=>{let a="",r="",i="";for(let l in s){let o=s[l];l[0]=="@"?l[1]=="i"?a=l+" "+o+";":r+=l[1]=="f"?Y(o,l):l+"{"+Y(o,l[1]=="k"?"":t)+"}":typeof o=="object"?r+=Y(o,t?t.replace(/([^,])+/g,c=>l.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,n=>/&/.test(n)?n.replace(/&/g,c):c?c+" "+n:n)):l):o!=null&&(l=/^--/.test(l)?l:l.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=Y.p?Y.p(l,o):l+":"+o+";")}return a+(t&&i?t+"{"+i+"}":i)+r},G={},Ns=s=>{if(typeof s=="object"){let t="";for(let a in s)t+=a+Ns(s[a]);return t}return s},ht=(s,t,a,r,i)=>{let l=Ns(s),o=G[l]||(G[l]=(n=>{let h=0,d=11;for(;h<n.length;)d=101*d+n.charCodeAt(h++)>>>0;return"go"+d})(l));if(!G[o]){let n=l!==s?s:(h=>{let d,p,N=[{}];for(;d=mt.exec(h.replace(xt,""));)d[4]?N.shift():d[3]?(p=d[3].replace(gs," ").trim(),N.unshift(N[0][p]=N[0][p]||{})):N[0][d[1]]=d[2].replace(gs," ").trim();return N[0]})(s);G[o]=Y(i?{["@keyframes "+o]:n}:n,a?"":"."+o)}let c=a&&G.g?G.g:null;return a&&(G.g=G[o]),((n,h,d,p)=>{p?h.data=h.data.replace(p,n):h.data.indexOf(n)===-1&&(h.data=d?n+h.data:h.data+n)})(G[o],t,r,c),o},pt=(s,t,a)=>s.reduce((r,i,l)=>{let o=t[l];if(o&&o.call){let c=o(a),n=c&&c.props&&c.props.className||/^go/.test(c)&&c;o=n?"."+n:c&&typeof c=="object"?c.props?"":Y(c,""):c===!1?"":c}return r+i+(o??"")},"");function Ee(s){let t=this||{},a=s.call?s(t.p):s;return ht(a.unshift?a.raw?pt(a,[].slice.call(arguments,1),t.p):a.reduce((r,i)=>Object.assign(r,i&&i.call?i(t.p):i),{}):a,dt(t.target),t.g,t.o,t.k)}let ws,Ve,qe;Ee.bind({g:1});let W=Ee.bind({k:1});function ut(s,t,a,r){Y.p=t,ws=s,Ve=a,qe=r}function K(s,t){let a=this||{};return function(){let r=arguments;function i(l,o){let c=Object.assign({},l),n=c.className||i.className;a.p=Object.assign({theme:Ve&&Ve()},c),a.o=/ *go\d+/.test(n),c.className=Ee.apply(a,r)+(n?" "+n:"");let h=s;return s[0]&&(h=c.as||s,delete c.as),qe&&h[0]&&qe(c),ws(h,c)}return i}}var gt=s=>typeof s=="function",ke=(s,t)=>gt(s)?s(t):s,ft=(()=>{let s=0;return()=>(++s).toString()})(),ks=(()=>{let s;return()=>{if(s===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");s=!t||t.matches}return s}})(),yt=20,ts="default",Ss=(s,t)=>{let{toastLimit:a}=s.settings;switch(t.type){case 0:return{...s,toasts:[t.toast,...s.toasts].slice(0,a)};case 1:return{...s,toasts:s.toasts.map(o=>o.id===t.toast.id?{...o,...t.toast}:o)};case 2:let{toast:r}=t;return Ss(s,{type:s.toasts.find(o=>o.id===r.id)?1:0,toast:r});case 3:let{toastId:i}=t;return{...s,toasts:s.toasts.map(o=>o.id===i||i===void 0?{...o,dismissed:!0,visible:!1}:o)};case 4:return t.toastId===void 0?{...s,toasts:[]}:{...s,toasts:s.toasts.filter(o=>o.id!==t.toastId)};case 5:return{...s,pausedAt:t.time};case 6:let l=t.time-(s.pausedAt||0);return{...s,pausedAt:void 0,toasts:s.toasts.map(o=>({...o,pauseDuration:o.pauseDuration+l}))}}},we=[],As={toasts:[],pausedAt:void 0,settings:{toastLimit:yt}},U={},Cs=(s,t=ts)=>{U[t]=Ss(U[t]||As,s),we.forEach(([a,r])=>{a===t&&r(U[t])})},Ms=s=>Object.keys(U).forEach(t=>Cs(s,t)),bt=s=>Object.keys(U).find(t=>U[t].toasts.some(a=>a.id===s)),_e=(s=ts)=>t=>{Cs(t,s)},jt={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},vt=(s={},t=ts)=>{let[a,r]=g.useState(U[t]||As),i=g.useRef(U[t]);g.useEffect(()=>(i.current!==U[t]&&r(U[t]),we.push([t,r]),()=>{let o=we.findIndex(([c])=>c===t);o>-1&&we.splice(o,1)}),[t]);let l=a.toasts.map(o=>{var c,n,h;return{...s,...s[o.type],...o,removeDelay:o.removeDelay||((c=s[o.type])==null?void 0:c.removeDelay)||s?.removeDelay,duration:o.duration||((n=s[o.type])==null?void 0:n.duration)||s?.duration||jt[o.type],style:{...s.style,...(h=s[o.type])==null?void 0:h.style,...o.style}}});return{...a,toasts:l}},Nt=(s,t="blank",a)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:s,pauseDuration:0,...a,id:a?.id||ft()}),he=s=>(t,a)=>{let r=Nt(t,s,a);return _e(r.toasterId||bt(r.id))({type:2,toast:r}),r.id},S=(s,t)=>he("blank")(s,t);S.error=he("error");S.success=he("success");S.loading=he("loading");S.custom=he("custom");S.dismiss=(s,t)=>{let a={type:3,toastId:s};t?_e(t)(a):Ms(a)};S.dismissAll=s=>S.dismiss(void 0,s);S.remove=(s,t)=>{let a={type:4,toastId:s};t?_e(t)(a):Ms(a)};S.removeAll=s=>S.remove(void 0,s);S.promise=(s,t,a)=>{let r=S.loading(t.loading,{...a,...a?.loading});return typeof s=="function"&&(s=s()),s.then(i=>{let l=t.success?ke(t.success,i):void 0;return l?S.success(l,{id:r,...a,...a?.success}):S.dismiss(r),i}).catch(i=>{let l=t.error?ke(t.error,i):void 0;l?S.error(l,{id:r,...a,...a?.error}):S.dismiss(r)}),s};var wt=1e3,kt=(s,t="default")=>{let{toasts:a,pausedAt:r}=vt(s,t),i=g.useRef(new Map).current,l=g.useCallback((p,N=wt)=>{if(i.has(p))return;let u=setTimeout(()=>{i.delete(p),o({type:4,toastId:p})},N);i.set(p,u)},[]);g.useEffect(()=>{if(r)return;let p=Date.now(),N=a.map(u=>{if(u.duration===1/0)return;let k=(u.duration||0)+u.pauseDuration-(p-u.createdAt);if(k<0){u.visible&&S.dismiss(u.id);return}return setTimeout(()=>S.dismiss(u.id,t),k)});return()=>{N.forEach(u=>u&&clearTimeout(u))}},[a,r,t]);let o=g.useCallback(_e(t),[t]),c=g.useCallback(()=>{o({type:5,time:Date.now()})},[o]),n=g.useCallback((p,N)=>{o({type:1,toast:{id:p,height:N}})},[o]),h=g.useCallback(()=>{r&&o({type:6,time:Date.now()})},[r,o]),d=g.useCallback((p,N)=>{let{reverseOrder:u=!1,gutter:k=8,defaultPosition:A}=N||{},C=a.filter(w=>(w.position||A)===(p.position||A)&&w.height),O=C.findIndex(w=>w.id===p.id),B=C.filter((w,P)=>P<O&&w.visible).length;return C.filter(w=>w.visible).slice(...u?[B+1]:[0,B]).reduce((w,P)=>w+(P.height||0)+k,0)},[a]);return g.useEffect(()=>{a.forEach(p=>{if(p.dismissed)l(p.id,p.removeDelay);else{let N=i.get(p.id);N&&(clearTimeout(N),i.delete(p.id))}})},[a,l]),{toasts:a,handlers:{updateHeight:n,startPause:c,endPause:h,calculateOffset:d}}},St=W`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,At=W`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Ct=W`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Mt=K("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${s=>s.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${St} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${At} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${s=>s.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Ct} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Pt=W`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,zt=K("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${s=>s.secondary||"#e0e0e0"};
  border-right-color: ${s=>s.primary||"#616161"};
  animation: ${Pt} 1s linear infinite;
`,Et=W`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,_t=W`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Tt=K("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${s=>s.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Et} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${_t} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${s=>s.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Rt=K("div")`
  position: absolute;
`,It=K("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,$t=W`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Lt=K("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${$t} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Dt=({toast:s})=>{let{icon:t,type:a,iconTheme:r}=s;return t!==void 0?typeof t=="string"?g.createElement(Lt,null,t):t:a==="blank"?null:g.createElement(It,null,g.createElement(zt,{...r}),a!=="loading"&&g.createElement(Rt,null,a==="error"?g.createElement(Mt,{...r}):g.createElement(Tt,{...r})))},Ot=s=>`
0% {transform: translate3d(0,${s*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Ft=s=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${s*-150}%,-1px) scale(.6); opacity:0;}
`,Bt="0%{opacity:0;} 100%{opacity:1;}",Ut="0%{opacity:1;} 100%{opacity:0;}",Vt=K("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,qt=K("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Gt=(s,t)=>{let a=s.includes("top")?1:-1,[r,i]=ks()?[Bt,Ut]:[Ot(a),Ft(a)];return{animation:t?`${W(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${W(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},Jt=g.memo(({toast:s,position:t,style:a,children:r})=>{let i=s.height?Gt(s.position||t||"top-center",s.visible):{opacity:0},l=g.createElement(Dt,{toast:s}),o=g.createElement(qt,{...s.ariaProps},ke(s.message,s));return g.createElement(Vt,{className:s.className,style:{...i,...a,...s.style}},typeof r=="function"?r({icon:l,message:o}):g.createElement(g.Fragment,null,l,o))});ut(g.createElement);var Ht=({id:s,className:t,style:a,onHeightUpdate:r,children:i})=>{let l=g.useCallback(o=>{if(o){let c=()=>{let n=o.getBoundingClientRect().height;r(s,n)};c(),new MutationObserver(c).observe(o,{subtree:!0,childList:!0,characterData:!0})}},[s,r]);return g.createElement("div",{ref:l,className:t,style:a},i)},Wt=(s,t)=>{let a=s.includes("top"),r=a?{top:0}:{bottom:0},i=s.includes("center")?{justifyContent:"center"}:s.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:ks()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(a?1:-1)}px)`,...r,...i}},Zt=Ee`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,je=16,Yt=({reverseOrder:s,position:t="top-center",toastOptions:a,gutter:r,children:i,toasterId:l,containerStyle:o,containerClassName:c})=>{let{toasts:n,handlers:h}=kt(a,l);return g.createElement("div",{"data-rht-toaster":l||"",style:{position:"fixed",zIndex:9999,top:je,left:je,right:je,bottom:je,pointerEvents:"none",...o},className:c,onMouseEnter:h.startPause,onMouseLeave:h.endPause},n.map(d=>{let p=d.position||t,N=h.calculateOffset(d,{reverseOrder:s,gutter:r,defaultPosition:t}),u=Wt(p,N);return g.createElement(Ht,{id:d.id,key:d.id,onHeightUpdate:h.updateHeight,className:d.visible?Zt:"",style:u},d.type==="custom"?ke(d.message,d):i?i(d):g.createElement(Jt,{toast:d,position:p}))}))};const Xt={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_API_BASE_URL:"https://stingray-app-7geup.ondigitalocean.app/api/v1",VITE_APP_ENV:"production",VITE_APP_NAME:"Job Application Platform",VITE_APP_VERSION:"1.0.0",VITE_ENABLE_ANALYTICS:"true",VITE_ENABLE_DEBUG:"false"},Kt={apiBaseUrl:Qt(),appName:le("VITE_APP_NAME","Job Application Platform"),appVersion:le("VITE_APP_VERSION","1.0.0"),appEnv:le("VITE_APP_ENV","development"),enableAnalytics:le("VITE_ENABLE_ANALYTICS","false")==="true",enableDebug:le("VITE_ENABLE_DEBUG","false")==="true"};function le(s,t){try{const a=Xt?.[s];if(a&&a!=="undefined")return a}catch{}if(typeof window<"u"&&window.ENV){const a=window.ENV[s];if(a&&a!=="undefined")return a}return t}function Qt(){const s=le("VITE_API_BASE_URL","");return s&&s!=="/api/v1"?s:typeof window<"u"&&window.location.hostname.includes("ondigitalocean.app")?`${window.location.origin}/api/v1`:"/api/v1"}class ea{client;constructor(){this.client=st.create({baseURL:Kt.apiBaseUrl,timeout:1e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors()}setupInterceptors(){this.client.interceptors.request.use(t=>{const a=localStorage.getItem("accessToken"),r=localStorage.getItem("sessionId");return a&&(t.headers.Authorization=`Bearer ${a}`),r&&(t.headers["X-Session-ID"]=r),t.headers["X-Request-ID"]=Math.random().toString(36).substring(2,15),t},t=>Promise.reject(t)),this.client.interceptors.response.use(t=>t,async t=>{const a=t.config;if(t.response?.status===401&&!a._retry){a._retry=!0;try{const r=localStorage.getItem("refreshToken");if(r){const i=await this.client.post("/auth/refresh",{refreshToken:r}),{accessToken:l,refreshToken:o}=i.data.data;return localStorage.setItem("accessToken",l),localStorage.setItem("refreshToken",o),a.headers.Authorization=`Bearer ${l}`,this.client(a)}}catch(r){return this.handleAuthError(),Promise.reject(r)}}return this.handleApiError(t),Promise.reject(t)})}handleApiError(t){const a=t.response?.data;a?.message?S.error(a.message):t.code==="NETWORK_ERROR"?S.error("Network error. Please check your connection."):t.code==="TIMEOUT"?S.error("Request timeout. Please try again."):S.error("An unexpected error occurred."),console.error("API Error:",t)}handleAuthError(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("sessionId"),localStorage.removeItem("user"),window.location.href="/login"}async get(t,a){const r=await this.client.get(t,a);return r.data.data||r.data}async post(t,a,r){const i=await this.client.post(t,a,r);return i.data.data||i.data}async put(t,a,r){const i=await this.client.put(t,a,r);return i.data.data||i.data}async patch(t,a,r){const i=await this.client.patch(t,a,r);return i.data.data||i.data}async delete(t,a){const r=await this.client.delete(t,a);return r.data.data||r.data}async upload(t,a,r){const i=await this.client.post(t,a,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:l=>{if(r&&l.total){const o=Math.round(l.loaded*100/l.total);r(o)}}});return i.data.data||i.data}async download(t,a,r){const i=await this.client.get(t,{responseType:"blob",...r}),l=new Blob([i.data]),o=window.URL.createObjectURL(l),c=document.createElement("a");c.href=o,c.download=a||"download",document.body.appendChild(c),c.click(),document.body.removeChild(c),window.URL.revokeObjectURL(o)}async healthCheck(){return(await this.client.get("/health")).data}}const J=new ea;class sa{async login(t){const a=await J.post("/auth/login",t);return this.storeAuthData(a),a}async register(t){const a=await J.post("/auth/register",t);return this.storeAuthData(a),a}async logout(){try{await J.post("/auth/logout")}catch(t){console.error("Logout error:",t)}finally{this.clearAuthData()}}async refreshToken(){const t=localStorage.getItem("refreshToken");if(!t)throw new Error("No refresh token available");const a=await J.post("/auth/refresh",{refreshToken:t});return localStorage.setItem("accessToken",a.accessToken),localStorage.setItem("refreshToken",a.refreshToken),a}async forgotPassword(t){await J.post("/auth/forgot-password",{email:t})}async resetPassword(t,a,r){await J.post("/auth/reset-password",{token:t,password:a,confirmPassword:r})}async changePassword(t,a,r){await J.post("/auth/change-password",{currentPassword:t,newPassword:a,confirmPassword:r})}async verifyEmail(t,a){await J.post("/auth/verify-email",{email:t,token:a})}async resendVerification(t){await J.post("/auth/resend-verification",{email:t})}getGoogleAuthUrl(){return"/api/v1/auth/google"}storeAuthData(t){const{user:a,tokens:r,accessToken:i,refreshToken:l,expiresIn:o}=t,c=r||{accessToken:i||"",refreshToken:l||""};localStorage.setItem("accessToken",c.accessToken),localStorage.setItem("refreshToken",c.refreshToken),localStorage.setItem("user",JSON.stringify(a));const n=Math.random().toString(36).substring(2,15);localStorage.setItem("sessionId",n)}clearAuthData(){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("sessionId"),localStorage.removeItem("user")}isAuthenticated(){const t=localStorage.getItem("accessToken"),a=localStorage.getItem("user");return!!(t&&a)}getCurrentUser(){const t=localStorage.getItem("user");if(t)try{return JSON.parse(t)}catch(a){console.error("Error parsing user data:",a),this.clearAuthData()}return null}getAccessToken(){return localStorage.getItem("accessToken")}getRefreshToken(){return localStorage.getItem("refreshToken")}updateCurrentUser(t){localStorage.setItem("user",JSON.stringify(t))}isTokenExpired(t){const a=t||this.getAccessToken();if(!a)return!0;try{const r=JSON.parse(atob(a.split(".")[1])),i=Date.now()/1e3;return r.exp<i}catch{return!0}}hasRole(t){return this.getCurrentUser()?.role===t}hasAnyRole(t){const a=this.getCurrentUser();return a?t.includes(a.role):!1}isAdmin(){return this.hasRole("admin")}isEmployer(){return this.hasRole("employer")}isPremiumUser(){const t=this.getCurrentUser();return t?.subscriptionTier==="premium"||t?.subscriptionTier==="enterprise"}}const L=new sa,Ps=g.createContext(void 0),ta=({children:s})=>{const[t,a]=g.useState(null),[r,i]=g.useState(!0),l=!!t;g.useEffect(()=>{o()},[]);const o=async()=>{try{const u=L.getCurrentUser(),k=!L.isTokenExpired();if(u&&k)a(u);else if(u&&L.getRefreshToken())try{await L.refreshToken(),a(u)}catch{L.logout(),a(null)}else a(null)}catch(u){console.error("Auth initialization error:",u),a(null)}finally{i(!1)}},c=async(u,k,A=!1)=>{try{i(!0);const C=await L.login({email:u,password:k,rememberMe:A});a(C.user||null),S.success("Login successful!")}catch(C){const O=C.response?.data?.message||"Login failed";throw S.error(O),C}finally{i(!1)}},n=async u=>{try{i(!0);const k=await L.register({...u,agreeToTerms:!0});a(k.user||null),S.success("Registration successful! Welcome aboard!")}catch(k){const A=k.response?.data?.message||"Registration failed";throw S.error(A),k}finally{i(!1)}},h=async()=>{try{i(!0),await L.logout(),a(null),S.success("Logged out successfully")}catch(u){console.error("Logout error:",u),a(null)}finally{i(!1)}},N={user:t,isAuthenticated:l,isLoading:r,login:c,register:n,logout:h,updateUser:u=>{a(u),L.updateCurrentUser(u)},refreshAuth:async()=>{try{if(!L.getRefreshToken())throw new Error("No refresh token available");await L.refreshToken();const k=L.getCurrentUser();k&&a(k)}catch(u){throw console.error("Auth refresh error:",u),await h(),u}}};return e.jsx(Ps.Provider,{value:N,children:s})},aa=()=>{const s=g.useContext(Ps);if(s===void 0)throw new Error("useAuth must be used within an AuthProvider");return s},ne=aa;function zs(s){var t,a,r="";if(typeof s=="string"||typeof s=="number")r+=s;else if(typeof s=="object")if(Array.isArray(s)){var i=s.length;for(t=0;t<i;t++)s[t]&&(a=zs(s[t]))&&(r&&(r+=" "),r+=a)}else for(a in s)s[a]&&(r&&(r+=" "),r+=a);return r}function ra(){for(var s,t,a=0,r="",i=arguments.length;a<i;a++)(s=arguments[a])&&(t=zs(s))&&(r&&(r+=" "),r+=t);return r}const as="-",ia=s=>{const t=oa(s),{conflictingClassGroups:a,conflictingClassGroupModifiers:r}=s;return{getClassGroupId:o=>{const c=o.split(as);return c[0]===""&&c.length!==1&&c.shift(),Es(c,t)||la(o)},getConflictingClassGroupIds:(o,c)=>{const n=a[o]||[];return c&&r[o]?[...n,...r[o]]:n}}},Es=(s,t)=>{if(s.length===0)return t.classGroupId;const a=s[0],r=t.nextPart.get(a),i=r?Es(s.slice(1),r):void 0;if(i)return i;if(t.validators.length===0)return;const l=s.join(as);return t.validators.find(({validator:o})=>o(l))?.classGroupId},fs=/^\[(.+)\]$/,la=s=>{if(fs.test(s)){const t=fs.exec(s)[1],a=t?.substring(0,t.indexOf(":"));if(a)return"arbitrary.."+a}},oa=s=>{const{theme:t,classGroups:a}=s,r={nextPart:new Map,validators:[]};for(const i in a)Ge(a[i],r,i,t);return r},Ge=(s,t,a,r)=>{s.forEach(i=>{if(typeof i=="string"){const l=i===""?t:ys(t,i);l.classGroupId=a;return}if(typeof i=="function"){if(na(i)){Ge(i(r),t,a,r);return}t.validators.push({validator:i,classGroupId:a});return}Object.entries(i).forEach(([l,o])=>{Ge(o,ys(t,l),a,r)})})},ys=(s,t)=>{let a=s;return t.split(as).forEach(r=>{a.nextPart.has(r)||a.nextPart.set(r,{nextPart:new Map,validators:[]}),a=a.nextPart.get(r)}),a},na=s=>s.isThemeGetter,ca=s=>{if(s<1)return{get:()=>{},set:()=>{}};let t=0,a=new Map,r=new Map;const i=(l,o)=>{a.set(l,o),t++,t>s&&(t=0,r=a,a=new Map)};return{get(l){let o=a.get(l);if(o!==void 0)return o;if((o=r.get(l))!==void 0)return i(l,o),o},set(l,o){a.has(l)?a.set(l,o):i(l,o)}}},Je="!",He=":",da=He.length,ma=s=>{const{prefix:t,experimentalParseClassName:a}=s;let r=i=>{const l=[];let o=0,c=0,n=0,h;for(let k=0;k<i.length;k++){let A=i[k];if(o===0&&c===0){if(A===He){l.push(i.slice(n,k)),n=k+da;continue}if(A==="/"){h=k;continue}}A==="["?o++:A==="]"?o--:A==="("?c++:A===")"&&c--}const d=l.length===0?i:i.substring(n),p=xa(d),N=p!==d,u=h&&h>n?h-n:void 0;return{modifiers:l,hasImportantModifier:N,baseClassName:p,maybePostfixModifierPosition:u}};if(t){const i=t+He,l=r;r=o=>o.startsWith(i)?l(o.substring(i.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:o,maybePostfixModifierPosition:void 0}}if(a){const i=r;r=l=>a({className:l,parseClassName:i})}return r},xa=s=>s.endsWith(Je)?s.substring(0,s.length-1):s.startsWith(Je)?s.substring(1):s,ha=s=>{const t=Object.fromEntries(s.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const i=[];let l=[];return r.forEach(o=>{o[0]==="["||t[o]?(i.push(...l.sort(),o),l=[]):l.push(o)}),i.push(...l.sort()),i}},pa=s=>({cache:ca(s.cacheSize),parseClassName:ma(s),sortModifiers:ha(s),...ia(s)}),ua=/\s+/,ga=(s,t)=>{const{parseClassName:a,getClassGroupId:r,getConflictingClassGroupIds:i,sortModifiers:l}=t,o=[],c=s.trim().split(ua);let n="";for(let h=c.length-1;h>=0;h-=1){const d=c[h],{isExternal:p,modifiers:N,hasImportantModifier:u,baseClassName:k,maybePostfixModifierPosition:A}=a(d);if(p){n=d+(n.length>0?" "+n:n);continue}let C=!!A,O=r(C?k.substring(0,A):k);if(!O){if(!C){n=d+(n.length>0?" "+n:n);continue}if(O=r(k),!O){n=d+(n.length>0?" "+n:n);continue}C=!1}const B=l(N).join(":"),w=u?B+Je:B,P=w+O;if(o.includes(P))continue;o.push(P);const F=i(O,C);for(let R=0;R<F.length;++R){const Q=F[R];o.push(w+Q)}n=d+(n.length>0?" "+n:n)}return n};function fa(){let s=0,t,a,r="";for(;s<arguments.length;)(t=arguments[s++])&&(a=_s(t))&&(r&&(r+=" "),r+=a);return r}const _s=s=>{if(typeof s=="string")return s;let t,a="";for(let r=0;r<s.length;r++)s[r]&&(t=_s(s[r]))&&(a&&(a+=" "),a+=t);return a};function ya(s,...t){let a,r,i,l=o;function o(n){const h=t.reduce((d,p)=>p(d),s());return a=pa(h),r=a.cache.get,i=a.cache.set,l=c,c(n)}function c(n){const h=r(n);if(h)return h;const d=ga(n,a);return i(n,d),d}return function(){return l(fa.apply(null,arguments))}}const M=s=>{const t=a=>a[s]||[];return t.isThemeGetter=!0,t},Ts=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Rs=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ba=/^\d+\/\d+$/,ja=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,va=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Na=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,wa=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ka=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ie=s=>ba.test(s),v=s=>!!s&&!Number.isNaN(Number(s)),Z=s=>!!s&&Number.isInteger(Number(s)),De=s=>s.endsWith("%")&&v(s.slice(0,-1)),H=s=>ja.test(s),Sa=()=>!0,Aa=s=>va.test(s)&&!Na.test(s),Is=()=>!1,Ca=s=>wa.test(s),Ma=s=>ka.test(s),Pa=s=>!m(s)&&!x(s),za=s=>ce(s,Ds,Is),m=s=>Ts.test(s),se=s=>ce(s,Os,Aa),Oe=s=>ce(s,Ia,v),bs=s=>ce(s,$s,Is),Ea=s=>ce(s,Ls,Ma),ve=s=>ce(s,Fs,Ca),x=s=>Rs.test(s),xe=s=>de(s,Os),_a=s=>de(s,$a),js=s=>de(s,$s),Ta=s=>de(s,Ds),Ra=s=>de(s,Ls),Ne=s=>de(s,Fs,!0),ce=(s,t,a)=>{const r=Ts.exec(s);return r?r[1]?t(r[1]):a(r[2]):!1},de=(s,t,a=!1)=>{const r=Rs.exec(s);return r?r[1]?t(r[1]):a:!1},$s=s=>s==="position"||s==="percentage",Ls=s=>s==="image"||s==="url",Ds=s=>s==="length"||s==="size"||s==="bg-size",Os=s=>s==="length",Ia=s=>s==="number",$a=s=>s==="family-name",Fs=s=>s==="shadow",La=()=>{const s=M("color"),t=M("font"),a=M("text"),r=M("font-weight"),i=M("tracking"),l=M("leading"),o=M("breakpoint"),c=M("container"),n=M("spacing"),h=M("radius"),d=M("shadow"),p=M("inset-shadow"),N=M("text-shadow"),u=M("drop-shadow"),k=M("blur"),A=M("perspective"),C=M("aspect"),O=M("ease"),B=M("animate"),w=()=>["auto","avoid","all","avoid-page","page","left","right","column"],P=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],F=()=>[...P(),x,m],R=()=>["auto","hidden","clip","visible","scroll"],Q=()=>["auto","contain","none"],b=()=>[x,m,n],V=()=>[ie,"full","auto",...b()],is=()=>[Z,"none","subgrid",x,m],ls=()=>["auto",{span:["full",Z,x,m]},Z,x,m],pe=()=>[Z,"auto",x,m],os=()=>["auto","min","max","fr",x,m],Re=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],re=()=>["start","end","center","stretch","center-safe","end-safe"],q=()=>["auto",...b()],ee=()=>[ie,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...b()],f=()=>[s,x,m],ns=()=>[...P(),js,bs,{position:[x,m]}],cs=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ds=()=>["auto","cover","contain",Ta,za,{size:[x,m]}],Ie=()=>[De,xe,se],_=()=>["","none","full",h,x,m],I=()=>["",v,xe,se],ue=()=>["solid","dashed","dotted","double"],ms=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],z=()=>[v,De,js,bs],xs=()=>["","none",k,x,m],ge=()=>["none",v,x,m],fe=()=>["none",v,x,m],$e=()=>[v,x,m],ye=()=>[ie,"full",...b()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[H],breakpoint:[H],color:[Sa],container:[H],"drop-shadow":[H],ease:["in","out","in-out"],font:[Pa],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[H],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[H],shadow:[H],spacing:["px",v],text:[H],"text-shadow":[H],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",ie,m,x,C]}],container:["container"],columns:[{columns:[v,m,x,c]}],"break-after":[{"break-after":w()}],"break-before":[{"break-before":w()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:F()}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:Q()}],"overscroll-x":[{"overscroll-x":Q()}],"overscroll-y":[{"overscroll-y":Q()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:V()}],"inset-x":[{"inset-x":V()}],"inset-y":[{"inset-y":V()}],start:[{start:V()}],end:[{end:V()}],top:[{top:V()}],right:[{right:V()}],bottom:[{bottom:V()}],left:[{left:V()}],visibility:["visible","invisible","collapse"],z:[{z:[Z,"auto",x,m]}],basis:[{basis:[ie,"full","auto",c,...b()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[v,ie,"auto","initial","none",m]}],grow:[{grow:["",v,x,m]}],shrink:[{shrink:["",v,x,m]}],order:[{order:[Z,"first","last","none",x,m]}],"grid-cols":[{"grid-cols":is()}],"col-start-end":[{col:ls()}],"col-start":[{"col-start":pe()}],"col-end":[{"col-end":pe()}],"grid-rows":[{"grid-rows":is()}],"row-start-end":[{row:ls()}],"row-start":[{"row-start":pe()}],"row-end":[{"row-end":pe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":os()}],"auto-rows":[{"auto-rows":os()}],gap:[{gap:b()}],"gap-x":[{"gap-x":b()}],"gap-y":[{"gap-y":b()}],"justify-content":[{justify:[...Re(),"normal"]}],"justify-items":[{"justify-items":[...re(),"normal"]}],"justify-self":[{"justify-self":["auto",...re()]}],"align-content":[{content:["normal",...Re()]}],"align-items":[{items:[...re(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...re(),{baseline:["","last"]}]}],"place-content":[{"place-content":Re()}],"place-items":[{"place-items":[...re(),"baseline"]}],"place-self":[{"place-self":["auto",...re()]}],p:[{p:b()}],px:[{px:b()}],py:[{py:b()}],ps:[{ps:b()}],pe:[{pe:b()}],pt:[{pt:b()}],pr:[{pr:b()}],pb:[{pb:b()}],pl:[{pl:b()}],m:[{m:q()}],mx:[{mx:q()}],my:[{my:q()}],ms:[{ms:q()}],me:[{me:q()}],mt:[{mt:q()}],mr:[{mr:q()}],mb:[{mb:q()}],ml:[{ml:q()}],"space-x":[{"space-x":b()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":b()}],"space-y-reverse":["space-y-reverse"],size:[{size:ee()}],w:[{w:[c,"screen",...ee()]}],"min-w":[{"min-w":[c,"screen","none",...ee()]}],"max-w":[{"max-w":[c,"screen","none","prose",{screen:[o]},...ee()]}],h:[{h:["screen","lh",...ee()]}],"min-h":[{"min-h":["screen","lh","none",...ee()]}],"max-h":[{"max-h":["screen","lh",...ee()]}],"font-size":[{text:["base",a,xe,se]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,x,Oe]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",De,m]}],"font-family":[{font:[_a,m,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,x,m]}],"line-clamp":[{"line-clamp":[v,"none",x,Oe]}],leading:[{leading:[l,...b()]}],"list-image":[{"list-image":["none",x,m]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",x,m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:f()}],"text-color":[{text:f()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ue(),"wavy"]}],"text-decoration-thickness":[{decoration:[v,"from-font","auto",x,se]}],"text-decoration-color":[{decoration:f()}],"underline-offset":[{"underline-offset":[v,"auto",x,m]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:b()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",x,m]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",x,m]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ns()}],"bg-repeat":[{bg:cs()}],"bg-size":[{bg:ds()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Z,x,m],radial:["",x,m],conic:[Z,x,m]},Ra,Ea]}],"bg-color":[{bg:f()}],"gradient-from-pos":[{from:Ie()}],"gradient-via-pos":[{via:Ie()}],"gradient-to-pos":[{to:Ie()}],"gradient-from":[{from:f()}],"gradient-via":[{via:f()}],"gradient-to":[{to:f()}],rounded:[{rounded:_()}],"rounded-s":[{"rounded-s":_()}],"rounded-e":[{"rounded-e":_()}],"rounded-t":[{"rounded-t":_()}],"rounded-r":[{"rounded-r":_()}],"rounded-b":[{"rounded-b":_()}],"rounded-l":[{"rounded-l":_()}],"rounded-ss":[{"rounded-ss":_()}],"rounded-se":[{"rounded-se":_()}],"rounded-ee":[{"rounded-ee":_()}],"rounded-es":[{"rounded-es":_()}],"rounded-tl":[{"rounded-tl":_()}],"rounded-tr":[{"rounded-tr":_()}],"rounded-br":[{"rounded-br":_()}],"rounded-bl":[{"rounded-bl":_()}],"border-w":[{border:I()}],"border-w-x":[{"border-x":I()}],"border-w-y":[{"border-y":I()}],"border-w-s":[{"border-s":I()}],"border-w-e":[{"border-e":I()}],"border-w-t":[{"border-t":I()}],"border-w-r":[{"border-r":I()}],"border-w-b":[{"border-b":I()}],"border-w-l":[{"border-l":I()}],"divide-x":[{"divide-x":I()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":I()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ue(),"hidden","none"]}],"divide-style":[{divide:[...ue(),"hidden","none"]}],"border-color":[{border:f()}],"border-color-x":[{"border-x":f()}],"border-color-y":[{"border-y":f()}],"border-color-s":[{"border-s":f()}],"border-color-e":[{"border-e":f()}],"border-color-t":[{"border-t":f()}],"border-color-r":[{"border-r":f()}],"border-color-b":[{"border-b":f()}],"border-color-l":[{"border-l":f()}],"divide-color":[{divide:f()}],"outline-style":[{outline:[...ue(),"none","hidden"]}],"outline-offset":[{"outline-offset":[v,x,m]}],"outline-w":[{outline:["",v,xe,se]}],"outline-color":[{outline:f()}],shadow:[{shadow:["","none",d,Ne,ve]}],"shadow-color":[{shadow:f()}],"inset-shadow":[{"inset-shadow":["none",p,Ne,ve]}],"inset-shadow-color":[{"inset-shadow":f()}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:f()}],"ring-offset-w":[{"ring-offset":[v,se]}],"ring-offset-color":[{"ring-offset":f()}],"inset-ring-w":[{"inset-ring":I()}],"inset-ring-color":[{"inset-ring":f()}],"text-shadow":[{"text-shadow":["none",N,Ne,ve]}],"text-shadow-color":[{"text-shadow":f()}],opacity:[{opacity:[v,x,m]}],"mix-blend":[{"mix-blend":[...ms(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ms()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[v]}],"mask-image-linear-from-pos":[{"mask-linear-from":z()}],"mask-image-linear-to-pos":[{"mask-linear-to":z()}],"mask-image-linear-from-color":[{"mask-linear-from":f()}],"mask-image-linear-to-color":[{"mask-linear-to":f()}],"mask-image-t-from-pos":[{"mask-t-from":z()}],"mask-image-t-to-pos":[{"mask-t-to":z()}],"mask-image-t-from-color":[{"mask-t-from":f()}],"mask-image-t-to-color":[{"mask-t-to":f()}],"mask-image-r-from-pos":[{"mask-r-from":z()}],"mask-image-r-to-pos":[{"mask-r-to":z()}],"mask-image-r-from-color":[{"mask-r-from":f()}],"mask-image-r-to-color":[{"mask-r-to":f()}],"mask-image-b-from-pos":[{"mask-b-from":z()}],"mask-image-b-to-pos":[{"mask-b-to":z()}],"mask-image-b-from-color":[{"mask-b-from":f()}],"mask-image-b-to-color":[{"mask-b-to":f()}],"mask-image-l-from-pos":[{"mask-l-from":z()}],"mask-image-l-to-pos":[{"mask-l-to":z()}],"mask-image-l-from-color":[{"mask-l-from":f()}],"mask-image-l-to-color":[{"mask-l-to":f()}],"mask-image-x-from-pos":[{"mask-x-from":z()}],"mask-image-x-to-pos":[{"mask-x-to":z()}],"mask-image-x-from-color":[{"mask-x-from":f()}],"mask-image-x-to-color":[{"mask-x-to":f()}],"mask-image-y-from-pos":[{"mask-y-from":z()}],"mask-image-y-to-pos":[{"mask-y-to":z()}],"mask-image-y-from-color":[{"mask-y-from":f()}],"mask-image-y-to-color":[{"mask-y-to":f()}],"mask-image-radial":[{"mask-radial":[x,m]}],"mask-image-radial-from-pos":[{"mask-radial-from":z()}],"mask-image-radial-to-pos":[{"mask-radial-to":z()}],"mask-image-radial-from-color":[{"mask-radial-from":f()}],"mask-image-radial-to-color":[{"mask-radial-to":f()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":P()}],"mask-image-conic-pos":[{"mask-conic":[v]}],"mask-image-conic-from-pos":[{"mask-conic-from":z()}],"mask-image-conic-to-pos":[{"mask-conic-to":z()}],"mask-image-conic-from-color":[{"mask-conic-from":f()}],"mask-image-conic-to-color":[{"mask-conic-to":f()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ns()}],"mask-repeat":[{mask:cs()}],"mask-size":[{mask:ds()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",x,m]}],filter:[{filter:["","none",x,m]}],blur:[{blur:xs()}],brightness:[{brightness:[v,x,m]}],contrast:[{contrast:[v,x,m]}],"drop-shadow":[{"drop-shadow":["","none",u,Ne,ve]}],"drop-shadow-color":[{"drop-shadow":f()}],grayscale:[{grayscale:["",v,x,m]}],"hue-rotate":[{"hue-rotate":[v,x,m]}],invert:[{invert:["",v,x,m]}],saturate:[{saturate:[v,x,m]}],sepia:[{sepia:["",v,x,m]}],"backdrop-filter":[{"backdrop-filter":["","none",x,m]}],"backdrop-blur":[{"backdrop-blur":xs()}],"backdrop-brightness":[{"backdrop-brightness":[v,x,m]}],"backdrop-contrast":[{"backdrop-contrast":[v,x,m]}],"backdrop-grayscale":[{"backdrop-grayscale":["",v,x,m]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[v,x,m]}],"backdrop-invert":[{"backdrop-invert":["",v,x,m]}],"backdrop-opacity":[{"backdrop-opacity":[v,x,m]}],"backdrop-saturate":[{"backdrop-saturate":[v,x,m]}],"backdrop-sepia":[{"backdrop-sepia":["",v,x,m]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":b()}],"border-spacing-x":[{"border-spacing-x":b()}],"border-spacing-y":[{"border-spacing-y":b()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",x,m]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[v,"initial",x,m]}],ease:[{ease:["linear","initial",O,x,m]}],delay:[{delay:[v,x,m]}],animate:[{animate:["none",B,x,m]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[A,x,m]}],"perspective-origin":[{"perspective-origin":F()}],rotate:[{rotate:ge()}],"rotate-x":[{"rotate-x":ge()}],"rotate-y":[{"rotate-y":ge()}],"rotate-z":[{"rotate-z":ge()}],scale:[{scale:fe()}],"scale-x":[{"scale-x":fe()}],"scale-y":[{"scale-y":fe()}],"scale-z":[{"scale-z":fe()}],"scale-3d":["scale-3d"],skew:[{skew:$e()}],"skew-x":[{"skew-x":$e()}],"skew-y":[{"skew-y":$e()}],transform:[{transform:[x,m,"","none","gpu","cpu"]}],"transform-origin":[{origin:F()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ye()}],"translate-x":[{"translate-x":ye()}],"translate-y":[{"translate-y":ye()}],"translate-z":[{"translate-z":ye()}],"translate-none":["translate-none"],accent:[{accent:f()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:f()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",x,m]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":b()}],"scroll-mx":[{"scroll-mx":b()}],"scroll-my":[{"scroll-my":b()}],"scroll-ms":[{"scroll-ms":b()}],"scroll-me":[{"scroll-me":b()}],"scroll-mt":[{"scroll-mt":b()}],"scroll-mr":[{"scroll-mr":b()}],"scroll-mb":[{"scroll-mb":b()}],"scroll-ml":[{"scroll-ml":b()}],"scroll-p":[{"scroll-p":b()}],"scroll-px":[{"scroll-px":b()}],"scroll-py":[{"scroll-py":b()}],"scroll-ps":[{"scroll-ps":b()}],"scroll-pe":[{"scroll-pe":b()}],"scroll-pt":[{"scroll-pt":b()}],"scroll-pr":[{"scroll-pr":b()}],"scroll-pb":[{"scroll-pb":b()}],"scroll-pl":[{"scroll-pl":b()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",x,m]}],fill:[{fill:["none",...f()]}],"stroke-w":[{stroke:[v,xe,se,Oe]}],stroke:[{stroke:["none",...f()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Da=ya(La);function X(...s){return Da(ra(s))}const Oa=({size:s="md",className:t,text:a})=>{const r={sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"};return e.jsxs("div",{className:X("flex flex-col items-center justify-center",t),children:[e.jsx("div",{className:X("animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",r[s])}),a&&e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:a})]})},te=({children:s,requireRoles:t=[],fallback:a})=>{const{isAuthenticated:r,isLoading:i,user:l}=ne(),o=Qe();return i?a||e.jsx(Oa,{}):r?t.length>0&&l&&!t.includes(l.role)?e.jsx(Ue,{to:"/unauthorized",replace:!0}):e.jsx(e.Fragment,{children:s}):e.jsx(Ue,{to:"/login",state:{from:o},replace:!0})};/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fa=s=>s.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Ba=s=>s.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,a,r)=>r?r.toUpperCase():a.toLowerCase()),vs=s=>{const t=Ba(s);return t.charAt(0).toUpperCase()+t.slice(1)},Bs=(...s)=>s.filter((t,a,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===a).join(" ").trim(),Ua=s=>{for(const t in s)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Va={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qa=g.forwardRef(({color:s="currentColor",size:t=24,strokeWidth:a=2,absoluteStrokeWidth:r,className:i="",children:l,iconNode:o,...c},n)=>g.createElement("svg",{ref:n,...Va,width:t,height:t,stroke:s,strokeWidth:r?Number(a)*24/Number(t):a,className:Bs("lucide",i),...!l&&!Ua(c)&&{"aria-hidden":"true"},...c},[...o.map(([h,d])=>g.createElement(h,d)),...Array.isArray(l)?l:[l]]));/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j=(s,t)=>{const a=g.forwardRef(({className:r,...i},l)=>g.createElement(qa,{ref:l,iconNode:t,className:Bs(`lucide-${Fa(vs(s))}`,`lucide-${s}`,r),...i}));return a.displayName=vs(s),a};/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ga=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],Se=j("arrow-left",Ga);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ja=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],Fe=j("arrow-right",Ja);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ha=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],Us=j("bell",Ha);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wa=[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]],Ae=j("briefcase",Wa);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Za=[["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M9 22v-3a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3",key:"cabbwy"}],["rect",{x:"4",y:"2",width:"16",height:"20",rx:"2",key:"1uxh74"}]],Ya=j("building",Za);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xa=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],Ka=j("calendar",Xa);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qa=[["path",{d:"M13.997 4a2 2 0 0 1 1.76 1.05l.486.9A2 2 0 0 0 18.003 7H20a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h1.997a2 2 0 0 0 1.759-1.048l.489-.904A2 2 0 0 1 10.004 4z",key:"18u6gg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]],er=j("camera",Qa);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sr=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],tr=j("chevron-down",sr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ar=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],Vs=j("circle-alert",ar);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rr=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],qs=j("circle-check-big",rr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ir=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],lr=j("circle-x",ir);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const or=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],We=j("clock",or);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nr=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],cr=j("credit-card",nr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dr=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],mr=j("dollar-sign",dr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xr=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],Ce=j("download",xr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hr=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],Ze=j("eye-off",hr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pr=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Ye=j("eye",pr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ur=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],oe=j("file-text",ur);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gr=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],Gs=j("funnel",gr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fr=[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]],yr=j("github",fr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const br=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],jr=j("globe",br);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vr=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-6a2 2 0 0 1 2.582 0l7 6A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"r6nss1"}]],rs=j("house",vr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nr=[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]],wr=j("linkedin",Nr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kr=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],Xe=j("lock",kr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sr=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],Te=j("mail",Sr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ar=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],Me=j("map-pin",Ar);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cr=[["path",{d:"M4 5h16",key:"1tepv9"}],["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 19h16",key:"1djgab"}]],Mr=j("menu",Cr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pr=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],zr=j("phone",Pr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Er=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Js=j("plus",Er);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _r=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Ke=j("search",_r);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tr=[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Rr=j("settings",Tr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ir=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],Hs=j("shield",Ir);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $r=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],Lr=j("square-pen",$r);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dr=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Or=j("star",Dr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fr=[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]],Ws=j("trash-2",Fr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Br=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],Ur=j("trending-up",Br);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vr=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]],qr=j("upload",Vr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gr=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],Pe=j("user",Gr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jr=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],Hr=j("users",Jr);/**
 * @license lucide-react v0.544.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wr=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Zr=j("x",Wr),y=ze.forwardRef(({className:s,variant:t="default",size:a="md",loading:r=!1,children:i,...l},o)=>{const c="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",n={default:"bg-primary-600 text-white hover:bg-primary-700",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50",ghost:"text-gray-700 hover:bg-gray-100",destructive:"bg-red-600 text-white hover:bg-red-700",link:"text-primary-600 underline-offset-4 hover:underline"},h={sm:"h-8 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-12 px-6 text-lg"};return e.jsxs("button",{className:X(c,n[t],h[a],s),ref:o,disabled:r||l.disabled,...l,children:[r&&e.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]})});y.displayName="Button";const T=ze.forwardRef(({className:s,type:t="text",label:a,error:r,helperText:i,id:l,...o},c)=>{const n=l||`input-${Math.random().toString(36).substring(2,11)}`;return e.jsxs("div",{className:"space-y-1",children:[a&&e.jsx("label",{htmlFor:n,className:X("block text-sm font-medium text-gray-700",r&&"text-red-700"),children:a}),e.jsx("input",{id:n,type:t,className:X("block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm",r&&"border-red-300 focus:ring-red-500 focus:border-red-500",s),ref:c,...o}),r&&e.jsx("p",{className:"text-red-600 text-sm",children:typeof r=="string"?r:"This field is required"}),i&&!r&&e.jsx("p",{className:"text-gray-500 text-sm",children:i})]})});T.displayName="Input";const D=ze.forwardRef(({className:s,label:t,description:a,error:r,id:i,...l},o)=>{const c=i||`checkbox-${Math.random().toString(36).substring(2,11)}`;return e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex items-center h-5",children:e.jsx("input",{id:c,type:"checkbox",className:X("h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",r&&"border-red-300 focus:ring-red-500",s),ref:o,...l})}),e.jsxs("div",{className:"text-sm",children:[t&&e.jsx("label",{htmlFor:c,className:X("font-medium text-gray-900 cursor-pointer",r&&"text-red-700"),children:t}),a&&e.jsx("p",{className:X("text-gray-500",r&&"text-red-600"),children:a}),r&&e.jsx("p",{className:"text-red-600 text-sm mt-1",children:r})]})]})});D.displayName="Checkbox";const Yr=()=>{const[s,t]=g.useState(!1),[a,r]=g.useState(!1),{login:i}=ne(),l=es(),c=Qe().state?.from?.pathname||"/dashboard",{register:n,handleSubmit:h,formState:{errors:d},setError:p}=ss({defaultValues:{email:"",password:"",rememberMe:!1}}),N=async k=>{try{r(!0),await i(k.email,k.password,k.rememberMe),l(c,{replace:!0})}catch(A){const C=A.response?.data?.message||"Login failed";p("root",{message:C})}finally{r(!1)}},u=()=>{window.location.href="/api/v1/auth/google"};return e.jsxs("div",{className:"w-full max-w-md mx-auto",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Welcome back"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Sign in to your account"})]}),e.jsxs("form",{onSubmit:h(N),className:"space-y-6",children:[d.root&&e.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-md p-3 flex items-center gap-2 text-error-700",children:[e.jsx(Vs,{className:"w-4 h-4 flex-shrink-0"}),e.jsx("span",{className:"text-sm",children:d.root.message})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),e.jsxs("div",{className:"relative",children:[e.jsx(T,{id:"email",type:"email",placeholder:"Enter your email",className:"pl-10",...n("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}}),error:!!d.email}),e.jsx(Te,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})]}),d.email&&e.jsx("p",{className:"text-sm text-error-600 mt-1",children:d.email.message})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(T,{id:"password",type:s?"text":"password",placeholder:"Enter your password",className:"pl-10 pr-10",...n("password",{required:"Password is required",minLength:{value:6,message:"Password must be at least 6 characters"}}),error:!!d.password}),e.jsx(Xe,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("button",{type:"button",onClick:()=>t(!s),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:s?e.jsx(Ze,{className:"w-4 h-4"}):e.jsx(Ye,{className:"w-4 h-4"})})]}),d.password&&e.jsx("p",{className:"text-sm text-error-600 mt-1",children:d.password.message})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(D,{id:"rememberMe",label:"Remember me",...n("rememberMe")}),e.jsx(E,{to:"/forgot-password",className:"text-sm text-primary-600 hover:text-primary-500",children:"Forgot password?"})]}),e.jsx(y,{type:"submit",className:"w-full",loading:a,disabled:a,children:"Sign in"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-300"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),e.jsxs(y,{type:"button",variant:"outline",className:"w-full",onClick:u,children:[e.jsxs("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[e.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),e.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),e.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),e.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Sign in with Google"]})]}),e.jsx("div",{className:"text-center mt-6",children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",e.jsx(E,{to:"/register",className:"font-medium text-primary-600 hover:text-primary-500",children:"Sign up"})]})})]})},Xr=()=>e.jsx(Yr,{}),Kr=()=>{const[s,t]=g.useState(!1),[a,r]=g.useState(!1),[i,l]=g.useState(!1),{register:o}=ne(),c=es(),{register:n,handleSubmit:h,formState:{errors:d},setError:p,watch:N}=ss({defaultValues:{firstName:"",lastName:"",email:"",password:"",confirmPassword:"",acceptTerms:!1,acceptMarketing:!1}}),u=N("password"),k=w=>{const F=[{test:w.length>=8,message:"At least 8 characters"},{test:/[A-Z]/.test(w),message:"One uppercase letter"},{test:/[a-z]/.test(w),message:"One lowercase letter"},{test:/\d/.test(w),message:"One number"},{test:/[!@#$%^&*(),.?":{}|<>]/.test(w),message:"One special character"}].filter(R=>!R.test);return F.length>0?F.map(R=>R.message).join(", "):!0},A=async w=>{try{l(!0),await o({firstName:w.firstName,lastName:w.lastName,email:w.email,password:w.password}),c("/dashboard")}catch(P){const F=P.response?.data?.message||"Registration failed";p("root",{message:F})}finally{l(!1)}},C=()=>{window.location.href="/api/v1/auth/google"},B=(w=>{if(!w)return{strength:0,label:""};let P=0;P=[w.length>=8,/[A-Z]/.test(w),/[a-z]/.test(w),/\d/.test(w),/[!@#$%^&*(),.?":{}|<>]/.test(w)].filter(Boolean).length;const R=["Very Weak","Weak","Fair","Good","Strong"],Q=["bg-error-500","bg-warning-500","bg-yellow-500","bg-primary-500","bg-success-500"];return{strength:P,label:R[P-1]||"",color:Q[P-1]||"bg-gray-300"}})(u);return e.jsxs("div",{className:"w-full max-w-md mx-auto",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Create your account"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Join thousands of job seekers"})]}),e.jsxs("form",{onSubmit:h(A),className:"space-y-6",children:[d.root&&e.jsxs("div",{className:"bg-error-50 border border-error-200 rounded-md p-3 flex items-center gap-2 text-error-700",children:[e.jsx(Vs,{className:"w-4 h-4 flex-shrink-0"}),e.jsx("span",{className:"text-sm",children:d.root.message})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"First name"}),e.jsxs("div",{className:"relative",children:[e.jsx(T,{id:"firstName",type:"text",placeholder:"John",className:"pl-10",...n("firstName",{required:"First name is required",minLength:{value:2,message:"First name must be at least 2 characters"}}),error:!!d.firstName}),e.jsx(Pe,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})]}),d.firstName&&e.jsx("p",{className:"text-sm text-error-600 mt-1",children:d.firstName.message})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Last name"}),e.jsxs("div",{className:"relative",children:[e.jsx(T,{id:"lastName",type:"text",placeholder:"Doe",className:"pl-10",...n("lastName",{required:"Last name is required",minLength:{value:2,message:"Last name must be at least 2 characters"}}),error:!!d.lastName}),e.jsx(Pe,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})]}),d.lastName&&e.jsx("p",{className:"text-sm text-error-600 mt-1",children:d.lastName.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),e.jsxs("div",{className:"relative",children:[e.jsx(T,{id:"email",type:"email",placeholder:"<EMAIL>",className:"pl-10",...n("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}}),error:!!d.email}),e.jsx(Te,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})]}),d.email&&e.jsx("p",{className:"text-sm text-error-600 mt-1",children:d.email.message})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(T,{id:"password",type:s?"text":"password",placeholder:"Create a strong password",className:"pl-10 pr-10",...n("password",{required:"Password is required",validate:k}),error:!!d.password}),e.jsx(Xe,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("button",{type:"button",onClick:()=>t(!s),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:s?e.jsx(Ze,{className:"w-4 h-4"}):e.jsx(Ye,{className:"w-4 h-4"})})]}),u&&e.jsx("div",{className:"mt-2",children:e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${B.color}`,style:{width:`${B.strength/5*100}%`}})}),e.jsx("span",{className:"text-xs text-gray-600",children:B.label})]})}),d.password&&e.jsx("p",{className:"text-sm text-error-600 mt-1",children:d.password.message})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm password"}),e.jsxs("div",{className:"relative",children:[e.jsx(T,{id:"confirmPassword",type:a?"text":"password",placeholder:"Confirm your password",className:"pl-10 pr-10",...n("confirmPassword",{required:"Please confirm your password",validate:w=>w===u||"Passwords do not match"}),error:!!d.confirmPassword}),e.jsx(Xe,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("button",{type:"button",onClick:()=>r(!a),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:a?e.jsx(Ze,{className:"w-4 h-4"}):e.jsx(Ye,{className:"w-4 h-4"})})]}),d.confirmPassword&&e.jsx("p",{className:"text-sm text-error-600 mt-1",children:d.confirmPassword.message})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(D,{id:"acceptTerms",label:e.jsxs("span",{children:["I agree to the"," ",e.jsx(E,{to:"/terms",className:"text-primary-600 hover:text-primary-500",children:"Terms of Service"})," ","and"," ",e.jsx(E,{to:"/privacy",className:"text-primary-600 hover:text-primary-500",children:"Privacy Policy"})]}),...n("acceptTerms",{required:"You must accept the terms and conditions"})}),d.acceptTerms&&e.jsx("p",{className:"text-sm text-error-600",children:d.acceptTerms.message}),e.jsx(D,{id:"acceptMarketing",label:"I want to receive job alerts and marketing emails",description:"You can unsubscribe at any time",...n("acceptMarketing")})]}),e.jsx(y,{type:"submit",className:"w-full",loading:i,disabled:i,children:"Create account"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-300"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or sign up with"})})]}),e.jsxs(y,{type:"button",variant:"outline",className:"w-full",onClick:C,children:[e.jsxs("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[e.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),e.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),e.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),e.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Sign up with Google"]})]}),e.jsx("div",{className:"text-center mt-6",children:e.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",e.jsx(E,{to:"/login",className:"font-medium text-primary-600 hover:text-primary-500",children:"Sign in"})]})})]})},Qr=()=>e.jsx(Kr,{}),ei=()=>{const[s,t]=g.useState(!1),[a,r]=g.useState(!1),{register:i,handleSubmit:l,formState:{errors:o},getValues:c}=ss(),n=async h=>{try{t(!0),await L.forgotPassword(h.email),r(!0),S.success("Password reset email sent!")}catch(d){const p=d.response?.data?.message||"Failed to send reset email";S.error(p)}finally{t(!1)}};return a?e.jsxs("div",{className:"w-full max-w-md mx-auto text-center",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-success-100",children:e.jsx(qs,{className:"h-6 w-6 text-success-600"})}),e.jsx("h1",{className:"mt-4 text-3xl font-bold text-gray-900",children:"Check your email"}),e.jsxs("p",{className:"text-gray-600 mt-2",children:["We've sent a password reset link to"," ",e.jsx("span",{className:"font-medium",children:c("email")})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Didn't receive the email? Check your spam folder or"," ",e.jsx("button",{onClick:()=>r(!1),className:"text-primary-600 hover:text-primary-500 font-medium",children:"try again"})]}),e.jsxs(E,{to:"/login",className:"inline-flex items-center text-sm text-primary-600 hover:text-primary-500",children:[e.jsx(Se,{className:"w-4 h-4 mr-1"}),"Back to login"]})]})]}):e.jsxs("div",{className:"w-full max-w-md mx-auto",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Forgot password?"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Enter your email and we'll send you a link to reset your password"})]}),e.jsxs("form",{onSubmit:l(n),className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),e.jsxs("div",{className:"relative",children:[e.jsx(T,{id:"email",type:"email",placeholder:"Enter your email",className:"pl-10",...i("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}}),error:!!o.email}),e.jsx(Te,{className:"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})]}),o.email&&e.jsx("p",{className:"text-sm text-error-600 mt-1",children:o.email.message})]}),e.jsx(y,{type:"submit",className:"w-full",loading:s,disabled:s,children:"Send reset link"})]}),e.jsx("div",{className:"text-center mt-6",children:e.jsxs(E,{to:"/login",className:"inline-flex items-center text-sm text-primary-600 hover:text-primary-500",children:[e.jsx(Se,{className:"w-4 h-4 mr-1"}),"Back to login"]})})]})},si=()=>{const{user:s}=ne(),t={totalApplications:24,activeApplications:8,interviews:3,responses:12},a=[{id:"1",jobTitle:"Senior Software Engineer",company:"TechCorp Inc.",status:"interview",appliedAt:"2024-01-15"},{id:"2",jobTitle:"Full Stack Developer",company:"StartupXYZ",status:"submitted",appliedAt:"2024-01-14"},{id:"3",jobTitle:"Frontend Developer",company:"BigTech Ltd.",status:"rejected",appliedAt:"2024-01-12"}],r=l=>{switch(l){case"interview":return e.jsx(We,{className:"w-4 h-4 text-warning-500"});case"accepted":return e.jsx(qs,{className:"w-4 h-4 text-success-500"});case"rejected":return e.jsx(lr,{className:"w-4 h-4 text-error-500"});default:return e.jsx(oe,{className:"w-4 h-4 text-primary-500"})}},i=l=>{switch(l){case"interview":return"status-interview";case"accepted":return"status-accepted";case"rejected":return"status-rejected";default:return"status-submitted"}};return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("h1",{className:"text-3xl font-bold text-gray-900",children:["Welcome back, ",s?.firstName,"!"]}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Here's an overview of your job search progress"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx("div",{className:"card",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center",children:e.jsx(oe,{className:"w-4 h-4 text-primary-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Applications"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.totalApplications})]})]})})}),e.jsx("div",{className:"card",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 bg-warning-100 rounded-lg flex items-center justify-center",children:e.jsx(We,{className:"w-4 h-4 text-warning-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Active Applications"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.activeApplications})]})]})})}),e.jsx("div",{className:"card",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center",children:e.jsx(Ae,{className:"w-4 h-4 text-success-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Interviews"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.interviews})]})]})})}),e.jsx("div",{className:"card",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx(Ur,{className:"w-4 h-4 text-blue-600"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Response Rate"}),e.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[Math.round(t.responses/t.totalApplications*100),"%"]})]})]})})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[e.jsx(E,{to:"/jobs",className:"card hover-lift",children:e.jsxs("div",{className:"card-body text-center",children:[e.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:e.jsx(Ae,{className:"w-6 h-6 text-primary-600"})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Find Jobs"}),e.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"Discover new opportunities that match your skills"}),e.jsxs(y,{variant:"outline",size:"sm",children:["Browse Jobs",e.jsx(Fe,{className:"w-4 h-4 ml-2"})]})]})}),e.jsx(E,{to:"/resumes",className:"card hover-lift",children:e.jsxs("div",{className:"card-body text-center",children:[e.jsx("div",{className:"w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:e.jsx(oe,{className:"w-6 h-6 text-success-600"})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Upload Resume"}),e.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"Add or optimize your resume for better results"}),e.jsxs(y,{variant:"outline",size:"sm",children:["Manage Resumes",e.jsx(Fe,{className:"w-4 h-4 ml-2"})]})]})}),e.jsx(E,{to:"/profile",className:"card hover-lift",children:e.jsxs("div",{className:"card-body text-center",children:[e.jsx("div",{className:"w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:e.jsx(Js,{className:"w-6 h-6 text-warning-600"})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Complete Profile"}),e.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"Enhance your profile to get better job matches"}),e.jsxs(y,{variant:"outline",size:"sm",children:["Update Profile",e.jsx(Fe,{className:"w-4 h-4 ml-2"})]})]})})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Recent Applications"}),e.jsx(E,{to:"/applications",className:"text-sm text-primary-600 hover:text-primary-500",children:"View all"})]})}),e.jsx("div",{className:"card-body p-0",children:e.jsx("div",{className:"divide-y divide-gray-200",children:a.map(l=>e.jsxs("div",{className:"p-6 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[r(l.status),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900",children:l.jobTitle}),e.jsx("p",{className:"text-sm text-gray-600",children:l.company})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("span",{className:`badge ${i(l.status)}`,children:l.status}),e.jsx("span",{className:"text-sm text-gray-500",children:new Date(l.appliedAt).toLocaleDateString()})]})]},l.id))})})]})]})},ti=()=>e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Find Your Next Job"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Discover opportunities that match your skills and preferences"})]}),e.jsx("div",{className:"card mb-6",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(Ke,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e.jsx(T,{type:"text",placeholder:"Job title, keywords, or company",className:"pl-10"})]})}),e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(Me,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e.jsx(T,{type:"text",placeholder:"Location or remote",className:"pl-10"})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(y,{variant:"outline",children:[e.jsx(Gs,{className:"w-4 h-4 mr-2"}),"Filters"]}),e.jsxs(y,{children:[e.jsx(Ke,{className:"w-4 h-4 mr-2"}),"Search"]})]})]})})}),e.jsx("div",{className:"space-y-4",children:[1,2,3,4,5].map(s=>e.jsx("div",{className:"card hover-lift",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center",children:e.jsx(Ae,{className:"w-6 h-6 text-primary-600"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Senior Software Engineer"}),e.jsx("p",{className:"text-gray-600",children:"TechCorp Inc."})]})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-500 mb-3",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(Me,{className:"w-4 h-4"}),"San Francisco, CA"]}),e.jsx("span",{children:"Full-time"}),e.jsx("span",{children:"$120k - $180k"})]}),e.jsx("p",{className:"text-gray-600 text-sm truncate-2-lines",children:"We're looking for a senior software engineer to join our growing team. You'll work on cutting-edge technologies and help shape the future of our platform..."}),e.jsxs("div",{className:"flex flex-wrap gap-2 mt-3",children:[e.jsx("span",{className:"badge badge-secondary",children:"React"}),e.jsx("span",{className:"badge badge-secondary",children:"Node.js"}),e.jsx("span",{className:"badge badge-secondary",children:"TypeScript"}),e.jsx("span",{className:"badge badge-secondary",children:"AWS"})]})]}),e.jsxs("div",{className:"ml-6 flex flex-col items-end gap-2",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"2 days ago"}),e.jsx(y,{size:"sm",children:"Apply Now"})]})]})})},s))}),e.jsx("div",{className:"text-center mt-8",children:e.jsx(y,{variant:"outline",children:"Load More Jobs"})})]}),ai=()=>{const{id:s}=Ks(),t={title:"Senior Software Engineer",company:"TechCorp Inc.",location:"San Francisco, CA",type:"Full-time",salary:"$120,000 - $180,000",postedAt:"2024-01-15",description:`We're looking for a senior software engineer to join our growing team. You'll work on cutting-edge technologies and help shape the future of our platform.

Key Responsibilities:
• Design and develop scalable web applications
• Collaborate with cross-functional teams
• Mentor junior developers
• Participate in code reviews and architectural decisions

Requirements:
• 5+ years of experience in software development
• Strong proficiency in React, Node.js, and TypeScript
• Experience with cloud platforms (AWS preferred)
• Excellent problem-solving skills`,requirements:["React","Node.js","TypeScript","AWS","PostgreSQL"],benefits:["Health Insurance","401(k)","Remote Work","Stock Options"],companyInfo:{name:"TechCorp Inc.",size:"100-500 employees",industry:"Technology"}};return e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("div",{className:"card mb-6",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"flex items-start justify-between mb-6",children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center",children:e.jsx(Ya,{className:"w-8 h-8 text-primary-600"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:t.title}),e.jsx("p",{className:"text-lg text-gray-600 mb-3",children:t.company}),e.jsxs("div",{className:"flex flex-wrap gap-4 text-sm text-gray-500",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(Me,{className:"w-4 h-4"}),t.location]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(We,{className:"w-4 h-4"}),t.type]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(mr,{className:"w-4 h-4"}),t.salary]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(Ka,{className:"w-4 h-4"}),"Posted ",new Date(t.postedAt).toLocaleDateString()]})]})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(y,{variant:"outline",children:"Save Job"}),e.jsx(y,{children:"Apply Now"})]})]})})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Job Description"})}),e.jsx("div",{className:"card-body",children:e.jsx("div",{className:"prose prose-sm max-w-none",children:t.description.split(`
`).map((a,r)=>e.jsx("p",{className:"mb-4 last:mb-0 whitespace-pre-line",children:a},r))})})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Required Skills"})}),e.jsx("div",{className:"card-body",children:e.jsx("div",{className:"flex flex-wrap gap-2",children:t.requirements.map(a=>e.jsx("span",{className:"badge badge-primary",children:a},a))})})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Benefits"})}),e.jsx("div",{className:"card-body",children:e.jsx("div",{className:"grid grid-cols-2 gap-2",children:t.benefits.map(a=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-success-500 rounded-full"}),e.jsx("span",{className:"text-sm text-gray-700",children:a})]},a))})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"About the Company"})}),e.jsxs("div",{className:"card-body space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:t.companyInfo.name}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:t.companyInfo.industry})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[e.jsx(Hr,{className:"w-4 h-4"}),t.companyInfo.size]}),e.jsx(y,{variant:"outline",size:"sm",className:"w-full",children:"Visit Company Page"})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Similar Jobs"})}),e.jsx("div",{className:"card-body p-0",children:e.jsx("div",{className:"divide-y divide-gray-200",children:[1,2,3].map(a=>e.jsxs("div",{className:"p-4",children:[e.jsx("h3",{className:"font-medium text-gray-900 text-sm mb-1",children:"Frontend Developer"}),e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"StartupXYZ"}),e.jsx("p",{className:"text-xs text-gray-500",children:"San Francisco, CA"})]},a))})})]})]})]})]})})},ri=()=>{const s=[{id:"1",jobTitle:"Senior Software Engineer",company:"TechCorp Inc.",status:"interview",appliedAt:"2024-01-15",lastUpdate:"2024-01-18"},{id:"2",jobTitle:"Full Stack Developer",company:"StartupXYZ",status:"submitted",appliedAt:"2024-01-14",lastUpdate:"2024-01-14"},{id:"3",jobTitle:"Frontend Developer",company:"BigTech Ltd.",status:"rejected",appliedAt:"2024-01-12",lastUpdate:"2024-01-16"},{id:"4",jobTitle:"Backend Engineer",company:"CloudCorp",status:"accepted",appliedAt:"2024-01-10",lastUpdate:"2024-01-17"}],t=a=>{switch(a){case"interview":return"status-interview";case"accepted":return"status-accepted";case"rejected":return"status-rejected";case"submitted":return"status-submitted";case"reviewed":return"status-reviewed";default:return"status-pending"}};return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"My Applications"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Track and manage all your job applications"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[e.jsx("div",{className:"card",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:"24"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Total Applications"})]})})}),e.jsx("div",{className:"card",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-2xl font-bold text-warning-600",children:"8"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Pending"})]})})}),e.jsx("div",{className:"card",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-2xl font-bold text-primary-600",children:"3"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Interviews"})]})})}),e.jsx("div",{className:"card",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-2xl font-bold text-success-600",children:"50%"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Response Rate"})]})})})]}),e.jsx("div",{className:"card mb-6",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(y,{variant:"outline",size:"sm",children:[e.jsx(Gs,{className:"w-4 h-4 mr-2"}),"All Status"]}),e.jsx(y,{variant:"outline",size:"sm",children:"Last 30 days"})]}),e.jsxs(y,{variant:"outline",size:"sm",children:[e.jsx(Ce,{className:"w-4 h-4 mr-2"}),"Export"]})]})})}),e.jsx("div",{className:"card",children:e.jsx("div",{className:"card-body p-0",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Company"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Applied"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Update"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(a=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:a.jobTitle})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:a.company})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`badge ${t(a.status)}`,children:a.status})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(a.appliedAt).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(a.lastUpdate).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsx(y,{variant:"ghost",size:"sm",children:"View Details"})})]},a.id))})]})})})})]})},ii=()=>{const s=[{id:"1",name:"Software Engineer Resume.pdf",status:"ready",uploadedAt:"2024-01-15",isDefault:!0,fileSize:"1.2 MB",analysis:{overallScore:85,atsScore:90,readabilityScore:88}},{id:"2",name:"Frontend Developer Resume.pdf",status:"ready",uploadedAt:"2024-01-10",isDefault:!1,fileSize:"980 KB",analysis:{overallScore:78,atsScore:82,readabilityScore:75}},{id:"3",name:"General Resume.pdf",status:"processing",uploadedAt:"2024-01-18",isDefault:!1,fileSize:"1.5 MB",analysis:null}],t=r=>{switch(r){case"ready":return"status-active";case"processing":return"status-pending";case"error":return"status-error";default:return"status-inactive"}},a=r=>r>=80?"text-success-600":r>=60?"text-warning-600":"text-error-600";return e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"My Resumes"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Upload, manage, and optimize your resumes for better job matches"})]}),e.jsx("div",{className:"card mb-6",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:e.jsx(qr,{className:"w-8 h-8 text-primary-600"})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Upload a New Resume"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Drag and drop your resume file or click to browse"}),e.jsxs("div",{className:"flex justify-center gap-2",children:[e.jsxs(y,{children:[e.jsx(Js,{className:"w-4 h-4 mr-2"}),"Choose File"]}),e.jsx(y,{variant:"outline",children:"Create from Template"})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"Supports PDF, DOC, DOCX files up to 10MB"})]})})}),e.jsx("div",{className:"space-y-4",children:s.map(r=>e.jsx("div",{className:"card",children:e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center",children:e.jsx(oe,{className:"w-6 h-6 text-gray-600"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:r.name}),r.isDefault&&e.jsx(Or,{className:"w-4 h-4 text-warning-500 fill-current"}),e.jsx("span",{className:`badge ${t(r.status)}`,children:r.status})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[e.jsxs("span",{children:["Uploaded ",new Date(r.uploadedAt).toLocaleDateString()]}),e.jsx("span",{children:r.fileSize})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-6",children:[r.analysis&&e.jsxs("div",{className:"flex space-x-4 text-sm",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:`font-semibold ${a(r.analysis.overallScore)}`,children:[r.analysis.overallScore,"%"]}),e.jsx("div",{className:"text-gray-500",children:"Overall"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:`font-semibold ${a(r.analysis.atsScore)}`,children:[r.analysis.atsScore,"%"]}),e.jsx("div",{className:"text-gray-500",children:"ATS"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:`font-semibold ${a(r.analysis.readabilityScore)}`,children:[r.analysis.readabilityScore,"%"]}),e.jsx("div",{className:"text-gray-500",children:"Readability"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(y,{variant:"ghost",size:"sm",children:e.jsx(Ce,{className:"w-4 h-4"})}),e.jsx(y,{variant:"ghost",size:"sm",children:e.jsx(Lr,{className:"w-4 h-4"})}),!r.isDefault&&e.jsx(y,{variant:"ghost",size:"sm",children:e.jsx(Ws,{className:"w-4 h-4 text-error-600"})})]})]})]}),r.analysis&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:["Last analyzed ",new Date(r.uploadedAt).toLocaleDateString()]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(y,{variant:"outline",size:"sm",children:"View Analysis"}),e.jsx(y,{variant:"outline",size:"sm",children:"Optimize Resume"}),!r.isDefault&&e.jsx(y,{size:"sm",children:"Set as Default"})]})]})})]})},r.id))}),e.jsxs("div",{className:"card mt-8",children:[e.jsx("div",{className:"card-header",children:e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Resume Tips"})}),e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Optimize for ATS"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Use standard section headings and avoid complex formatting to ensure your resume passes through Applicant Tracking Systems."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Tailor for Each Job"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Customize your resume for each application by highlighting relevant skills and experiences that match the job description."})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Use Action Verbs"}),e.jsx("p",{className:"text-sm text-gray-600",children:'Start bullet points with strong action verbs like "developed," "managed," or "implemented" to make your achievements stand out.'})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Quantify Results"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Include specific numbers and metrics to demonstrate the impact of your work and make your accomplishments more compelling."})]})]})})]})]})},li=()=>{const{user:s}=ne();return e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"My Profile"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Manage your profile information and preferences"})]}),e.jsx("div",{className:"card mb-6",children:e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{className:"w-24 h-24 rounded-full",src:s?.avatar||`https://ui-avatars.com/api/?name=${s?.firstName}+${s?.lastName}&background=3B82F6&color=fff&size=96`,alt:`${s?.firstName} ${s?.lastName}`}),e.jsx("button",{className:"absolute bottom-0 right-0 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center hover:bg-primary-700",children:e.jsx(er,{className:"w-4 h-4"})})]}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[s?.firstName," ",s?.lastName]}),e.jsx("p",{className:"text-gray-600",children:s?.email}),e.jsxs("div",{className:"flex items-center gap-4 mt-2 text-sm text-gray-500",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(Me,{className:"w-4 h-4"}),"San Francisco, CA"]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(Te,{className:"w-4 h-4"}),"Available for work"]})]})]}),e.jsx(y,{children:"Edit Profile"})]})})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Basic Information"})}),e.jsxs("div",{className:"card-body space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"First Name"}),e.jsx(T,{value:s?.firstName||"",readOnly:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Last Name"}),e.jsx(T,{value:s?.lastName||"",readOnly:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Email"}),e.jsx(T,{value:s?.email||"",readOnly:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Phone"}),e.jsx(T,{placeholder:"+****************"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Bio"}),e.jsx("textarea",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm",rows:4,placeholder:"Tell us about yourself..."})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Experience"}),e.jsx(y,{variant:"outline",size:"sm",children:"Add Experience"})]})}),e.jsx("div",{className:"card-body",children:e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"border-l-2 border-primary-200 pl-4",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900",children:"Senior Software Engineer"}),e.jsx("p",{className:"text-gray-600",children:"TechCorp Inc."}),e.jsx("p",{className:"text-sm text-gray-500",children:"Jan 2022 - Present"})]}),e.jsx(y,{variant:"ghost",size:"sm",children:"Edit"})]}),e.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Leading development of scalable web applications using React and Node.js. Mentoring junior developers and participating in architectural decisions."})]})})})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Education"}),e.jsx(y,{variant:"outline",size:"sm",children:"Add Education"})]})}),e.jsx("div",{className:"card-body",children:e.jsx("div",{className:"space-y-4",children:e.jsx("div",{className:"border-l-2 border-primary-200 pl-4",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900",children:"Bachelor of Science in Computer Science"}),e.jsx("p",{className:"text-gray-600",children:"University of California, Berkeley"}),e.jsx("p",{className:"text-sm text-gray-500",children:"2018 - 2022"})]}),e.jsx(y,{variant:"ghost",size:"sm",children:"Edit"})]})})})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Contact"})}),e.jsxs("div",{className:"card-body space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(zr,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-sm text-gray-600",children:"+****************"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(jr,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-sm text-gray-600",children:"johndoe.dev"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(wr,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-sm text-gray-600",children:"linkedin.com/in/johndoe"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(yr,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-sm text-gray-600",children:"github.com/johndoe"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Skills"}),e.jsx(y,{variant:"outline",size:"sm",children:"Add Skill"})]})}),e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx("span",{className:"badge badge-primary",children:"React"}),e.jsx("span",{className:"badge badge-primary",children:"Node.js"}),e.jsx("span",{className:"badge badge-primary",children:"TypeScript"}),e.jsx("span",{className:"badge badge-primary",children:"Python"}),e.jsx("span",{className:"badge badge-primary",children:"AWS"}),e.jsx("span",{className:"badge badge-primary",children:"PostgreSQL"})]})})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Profile Completion"})}),e.jsx("div",{className:"card-body",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsx("span",{children:"Overall Progress"}),e.jsx("span",{className:"font-semibold",children:"75%"})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:"bg-primary-600 h-2 rounded-full",style:{width:"75%"}})}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-success-500 rounded-full"}),e.jsx("span",{className:"text-gray-600",children:"Basic info completed"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-success-500 rounded-full"}),e.jsx("span",{className:"text-gray-600",children:"Experience added"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-warning-500 rounded-full"}),e.jsx("span",{className:"text-gray-600",children:"Add more skills"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-300 rounded-full"}),e.jsx("span",{className:"text-gray-600",children:"Upload profile photo"})]})]})]})})]})]})]})]})})},oi=()=>e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Settings"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Manage your account settings and preferences"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Pe,{className:"w-5 h-5 text-gray-600"}),e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Account Settings"})]})}),e.jsxs("div",{className:"card-body space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Email Address"}),e.jsx("p",{className:"text-sm text-gray-600",children:"<EMAIL>"})]}),e.jsx(y,{variant:"outline",size:"sm",children:"Change"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Password"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Last changed 30 days ago"})]}),e.jsx(y,{variant:"outline",size:"sm",children:"Change"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Two-Factor Authentication"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Add an extra layer of security"})]}),e.jsx(y,{variant:"outline",size:"sm",children:"Enable"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Us,{className:"w-5 h-5 text-gray-600"}),e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Notification Settings"})]})}),e.jsxs("div",{className:"card-body space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-3",children:"Email Notifications"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(D,{id:"jobAlerts",label:"Job Alerts",description:"Receive notifications about new job opportunities",defaultChecked:!0}),e.jsx(D,{id:"applicationUpdates",label:"Application Updates",description:"Get notified when your application status changes",defaultChecked:!0}),e.jsx(D,{id:"weeklyDigest",label:"Weekly Digest",description:"Weekly summary of your job search activity"}),e.jsx(D,{id:"marketingEmails",label:"Marketing Emails",description:"Receive tips and product updates"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-3",children:"Push Notifications"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(D,{id:"pushJobAlerts",label:"Job Alerts",description:"Push notifications for new job matches",defaultChecked:!0}),e.jsx(D,{id:"pushApplications",label:"Application Updates",description:"Push notifications for application status changes",defaultChecked:!0})]})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Hs,{className:"w-5 h-5 text-gray-600"}),e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Privacy Settings"})]})}),e.jsxs("div",{className:"card-body space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Profile Visibility"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Control who can see your profile"})]}),e.jsxs("select",{className:"border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-primary-500 focus:border-primary-500",children:[e.jsx("option",{children:"Public"}),e.jsx("option",{children:"Private"}),e.jsx("option",{children:"Connections only"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(D,{id:"allowMessages",label:"Allow messages from recruiters",description:"Let recruiters contact you directly",defaultChecked:!0}),e.jsx(D,{id:"showSalary",label:"Show salary expectations",description:"Display your salary range on your profile"}),e.jsx(D,{id:"anonymousMode",label:"Anonymous job browsing",description:"Browse jobs without companies seeing your profile"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(cr,{className:"w-5 h-5 text-gray-600"}),e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Subscription"})]})}),e.jsxs("div",{className:"card-body",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Current Plan"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Free Plan - Basic features included"})]}),e.jsx(y,{children:"Upgrade to Premium"})]}),e.jsxs("div",{className:"mt-6 p-4 bg-primary-50 rounded-lg border border-primary-200",children:[e.jsx("h4",{className:"font-medium text-primary-900 mb-2",children:"Premium Benefits"}),e.jsxs("ul",{className:"text-sm text-primary-800 space-y-1",children:[e.jsx("li",{children:"• Unlimited resume uploads and optimization"}),e.jsx("li",{children:"• Advanced job matching and alerts"}),e.jsx("li",{children:"• Priority customer support"}),e.jsx("li",{children:"• Detailed application analytics"})]})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("div",{className:"card-header",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ce,{className:"w-5 h-5 text-gray-600"}),e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Data & Privacy"})]})}),e.jsxs("div",{className:"card-body space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Download Your Data"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Get a copy of all your data"})]}),e.jsxs(y,{variant:"outline",size:"sm",children:[e.jsx(Ce,{className:"w-4 h-4 mr-2"}),"Download"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900",children:"Delete Account"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Permanently delete your account and all data"})]}),e.jsxs(y,{variant:"outline",size:"sm",className:"text-error-600 border-error-600 hover:bg-error-50",children:[e.jsx(Ws,{className:"w-4 h-4 mr-2"}),"Delete"]})]})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 mt-8",children:[e.jsx(y,{variant:"outline",children:"Cancel"}),e.jsx(y,{children:"Save Changes"})]})]})}),ni=()=>e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:e.jsxs("div",{className:"max-w-md w-full text-center",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-9xl font-bold text-gray-300",children:"404"}),e.jsx("h2",{className:"text-3xl font-bold text-gray-900 mt-4",children:"Page not found"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Sorry, we couldn't find the page you're looking for."})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(E,{to:"/dashboard",children:e.jsxs(y,{children:[e.jsx(rs,{className:"w-4 h-4 mr-2"}),"Go to Dashboard"]})}),e.jsxs(y,{variant:"outline",onClick:()=>window.history.back(),children:[e.jsx(Se,{className:"w-4 h-4 mr-2"}),"Go Back"]})]})]})}),ci=()=>e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:e.jsxs("div",{className:"max-w-md w-full text-center",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("div",{className:"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-error-100 mb-4",children:e.jsx(Hs,{className:"h-8 w-8 text-error-600"})}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Access Denied"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"You don't have permission to access this page."})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(E,{to:"/dashboard",children:e.jsxs(y,{children:[e.jsx(rs,{className:"w-4 h-4 mr-2"}),"Go to Dashboard"]})}),e.jsxs(y,{variant:"outline",onClick:()=>window.history.back(),children:[e.jsx(Se,{className:"w-4 h-4 mr-2"}),"Go Back"]})]})]})}),di=[{name:"Dashboard",href:"/dashboard",icon:rs},{name:"Jobs",href:"/jobs",icon:Ae},{name:"Applications",href:"/applications",icon:oe},{name:"Resumes",href:"/resumes",icon:oe},{name:"Profile",href:"/profile",icon:Pe},{name:"Settings",href:"/settings",icon:Rr}],ae=({children:s})=>{const[t,a]=g.useState(!1),[r,i]=g.useState(!1),{user:l,logout:o}=ne(),c=Qe(),n=es(),h=async()=>{await o(),n("/login")},d=p=>c.pathname===p;return e.jsxs("div",{className:"h-screen flex overflow-hidden bg-gray-100",children:[t&&e.jsx("div",{className:"fixed inset-0 z-40 lg:hidden",onClick:()=>a(!1),children:e.jsx("div",{className:"absolute inset-0 bg-gray-600 opacity-75"})}),e.jsxs("div",{className:`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${t?"translate-x-0":"-translate-x-full"}
      `,children:[e.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[e.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"JobAutomator"}),e.jsx("button",{onClick:()=>a(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",children:e.jsx(Zr,{className:"w-5 h-5"})})]}),e.jsx("nav",{className:"mt-6 px-3",children:e.jsx("div",{className:"space-y-1",children:di.map(p=>{const N=p.icon,u=d(p.href);return e.jsxs(E,{to:p.href,className:`
                    group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                    ${u?"bg-primary-100 text-primary-700 border-r-2 border-primary-700":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}
                  `,onClick:()=>a(!1),children:[e.jsx(N,{className:`
                    mr-3 h-5 w-5 flex-shrink-0
                    ${u?"text-primary-500":"text-gray-400 group-hover:text-gray-500"}
                  `}),p.name]},p.name)})})}),e.jsx("div",{className:"absolute bottom-0 w-full p-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("img",{className:"h-8 w-8 rounded-full",src:l?.avatar||`https://ui-avatars.com/api/?name=${l?.firstName}+${l?.lastName}&background=3B82F6&color=fff`,alt:`${l?.firstName} ${l?.lastName}`})}),e.jsxs("div",{className:"ml-3 flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900 truncate",children:[l?.firstName," ",l?.lastName]}),e.jsx("p",{className:"text-xs text-gray-500 truncate",children:l?.email})]})]})})]}),e.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[e.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between h-16 px-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("button",{onClick:()=>a(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",children:e.jsx(Mr,{className:"w-5 h-5"})}),e.jsx("div",{className:"hidden md:block ml-4",children:e.jsxs("div",{className:"relative",children:[e.jsx(Ke,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),e.jsx("input",{type:"text",placeholder:"Search jobs, companies...",className:"pl-10 pr-4 py-2 w-80 border border-gray-300 rounded-md text-sm focus:ring-primary-500 focus:border-primary-500"})]})})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("button",{className:"p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-md relative",children:[e.jsx(Us,{className:"w-5 h-5"}),e.jsx("span",{className:"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("button",{onClick:()=>i(!r),className:"flex items-center space-x-2 p-2 text-gray-700 hover:bg-gray-100 rounded-md",children:[e.jsx("img",{className:"h-8 w-8 rounded-full",src:l?.avatar||`https://ui-avatars.com/api/?name=${l?.firstName}+${l?.lastName}&background=3B82F6&color=fff`,alt:`${l?.firstName} ${l?.lastName}`}),e.jsx(tr,{className:"w-4 h-4"})]}),r&&e.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50",children:e.jsxs("div",{className:"py-1",children:[e.jsx(E,{to:"/profile",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:()=>i(!1),children:"Your Profile"}),e.jsx(E,{to:"/settings",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:()=>i(!1),children:"Settings"}),e.jsx("hr",{className:"my-1"}),e.jsx("button",{onClick:h,className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign out"})]})})]})]})]})}),e.jsx("main",{className:"flex-1 overflow-auto",children:s})]}),r&&e.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>i(!1)})]})},Be=({children:s})=>e.jsxs("div",{className:"min-h-screen flex",children:[e.jsxs("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary-600 to-primary-800 relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black opacity-10"}),e.jsx("div",{className:"absolute inset-0 opacity-20",children:e.jsxs("svg",{className:"w-full h-full",viewBox:"0 0 100 100",preserveAspectRatio:"none",children:[e.jsx("defs",{children:e.jsx("pattern",{id:"grid",width:"10",height:"10",patternUnits:"userSpaceOnUse",children:e.jsx("path",{d:"M 10 0 L 0 0 0 10",fill:"none",stroke:"white",strokeWidth:"0.5"})})}),e.jsx("rect",{width:"100",height:"100",fill:"url(#grid)"})]})}),e.jsxs("div",{className:"relative z-10 flex flex-col justify-center px-12 text-white",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:"Job Application Automator"}),e.jsx("p",{className:"text-xl text-primary-100 leading-relaxed",children:"Streamline your job search with AI-powered tools, automated applications, and intelligent resume optimization."})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"AI-Powered Resume Optimization"}),e.jsx("p",{className:"text-primary-100",children:"Automatically tailor your resume for each job application"})]})]}),e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Smart Job Matching"}),e.jsx("p",{className:"text-primary-100",children:"Find the perfect opportunities based on your skills and preferences"})]})]}),e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Application Tracking"}),e.jsx("p",{className:"text-primary-100",children:"Keep track of all your applications in one centralized dashboard"})]})]})]}),e.jsxs("div",{className:"mt-12 text-sm text-primary-200",children:[e.jsx("p",{children:'"This platform helped me land my dream job in just 2 weeks!"'}),e.jsx("p",{className:"mt-1 font-semibold",children:"- Sarah Johnson, Software Engineer"})]})]})]}),e.jsx("div",{className:"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"w-full max-w-md",children:s})})]}),mi=new tt({defaultOptions:{queries:{retry:1,refetchOnWindowFocus:!1,staleTime:300*1e3}}});function xi(){return e.jsx(at,{client:mi,children:e.jsx(Qs,{children:e.jsx(ta,{children:e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsxs(et,{children:[e.jsx($,{path:"/login",element:e.jsx(Be,{children:e.jsx(Xr,{})})}),e.jsx($,{path:"/register",element:e.jsx(Be,{children:e.jsx(Qr,{})})}),e.jsx($,{path:"/forgot-password",element:e.jsx(Be,{children:e.jsx(ei,{})})}),e.jsx($,{path:"/dashboard",element:e.jsx(te,{children:e.jsx(ae,{children:e.jsx(si,{})})})}),e.jsx($,{path:"/jobs",element:e.jsx(te,{children:e.jsx(ae,{children:e.jsx(ti,{})})})}),e.jsx($,{path:"/jobs/:id",element:e.jsx(te,{children:e.jsx(ae,{children:e.jsx(ai,{})})})}),e.jsx($,{path:"/applications",element:e.jsx(te,{children:e.jsx(ae,{children:e.jsx(ri,{})})})}),e.jsx($,{path:"/resumes",element:e.jsx(te,{children:e.jsx(ae,{children:e.jsx(ii,{})})})}),e.jsx($,{path:"/profile",element:e.jsx(te,{children:e.jsx(ae,{children:e.jsx(li,{})})})}),e.jsx($,{path:"/settings",element:e.jsx(te,{children:e.jsx(ae,{children:e.jsx(oi,{})})})}),e.jsx($,{path:"/",element:e.jsx(Ue,{to:"/dashboard",replace:!0})}),e.jsx($,{path:"/unauthorized",element:e.jsx(ci,{})}),e.jsx($,{path:"*",element:e.jsx(ni,{})})]}),e.jsx(Yt,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#10B981",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#EF4444",secondary:"#fff"}}}})]})})})})}nt.createRoot(document.getElementById("root")).render(e.jsx(ze.StrictMode,{children:e.jsx(xi,{})}));
//# sourceMappingURL=index-JYTKCdy3.js.map
