"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceRegistry = void 0;
const logger_1 = require("../utils/logger");
const http_1 = __importDefault(require("http"));
class ServiceRegistry {
    services = new Map();
    healthCheckInterval = null;
    constructor() {
        this.registerDefaultServices();
        this.startHealthChecks();
    }
    registerDefaultServices() {
        // Use service names for Docker container communication, localhost for local dev
        const getServiceUrl = (serviceName, port) => {
            const envVar = `${serviceName.toUpperCase().replace('-', '_')}_URL`;
            const defaultHost = process.env.NODE_ENV === 'production' ? serviceName : 'localhost';
            return process.env[envVar] ?? `http://${defaultHost}:${port}`;
        };
        const services = [
            {
                name: 'auth-service',
                url: getServiceUrl('auth-service', 3001),
                healthCheck: '/health',
            },
            {
                name: 'user-service',
                url: getServiceUrl('user-service', 3002),
                healthCheck: '/health',
            },
            {
                name: 'job-service',
                url: getServiceUrl('job-service', 3003),
                healthCheck: '/health',
            },
            {
                name: 'resume-service',
                url: getServiceUrl('resume-service', 3004),
                healthCheck: '/health',
            },
            {
                name: 'analytics-service',
                url: getServiceUrl('analytics-service', 3005),
                healthCheck: '/health',
            },
            {
                name: 'notification-service',
                url: getServiceUrl('notification-service', 3006),
                healthCheck: '/health',
            },
            {
                name: 'integration-service',
                url: getServiceUrl('integration-service', 3007),
                healthCheck: '/health',
            },
            {
                name: 'payment-service',
                url: getServiceUrl('payment-service', 3008),
                healthCheck: '/health',
            },
        ];
        services.forEach(service => {
            this.services.set(service.name, {
                ...service,
                isHealthy: false,
                lastHealthCheck: null,
            });
        });
        logger_1.logger.info(`Registered ${services.length} services`);
    }
    getServiceUrl(serviceName) {
        const service = this.services.get(serviceName);
        if (!service) {
            throw new Error(`Service ${serviceName} not found`);
        }
        return service.url;
    }
    getServiceList() {
        return Array.from(this.services.keys());
    }
    getHealthyServices() {
        return Array.from(this.services.entries())
            .filter(([, service]) => service.isHealthy)
            .map(([name]) => name);
    }
    getServiceHealth() {
        const health = {};
        this.services.forEach((service, name) => {
            health[name] = {
                isHealthy: service.isHealthy,
                lastCheck: service.lastHealthCheck,
            };
        });
        return health;
    }
    async checkServiceHealth(serviceName) {
        const service = this.services.get(serviceName);
        if (!service)
            return false;
        try {
            const url = new URL(`${service.url}${service.healthCheck}`);
            const isHealthy = await new Promise((resolve) => {
                const req = http_1.default.request({
                    hostname: url.hostname,
                    port: url.port || (url.protocol === 'https:' ? 443 : 80),
                    path: url.pathname + url.search,
                    method: 'GET',
                    timeout: 5000,
                }, (res) => {
                    resolve((res.statusCode ?? 500) >= 200 && (res.statusCode ?? 500) < 300);
                });
                req.on('error', () => {
                    resolve(false);
                });
                req.on('timeout', () => {
                    req.destroy();
                    resolve(false);
                });
                req.end();
            });
            this.services.set(serviceName, {
                ...service,
                isHealthy,
                lastHealthCheck: new Date(),
            });
            return isHealthy;
        }
        catch (error) {
            logger_1.logger.warn(`Health check failed for ${serviceName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            this.services.set(serviceName, {
                ...service,
                isHealthy: false,
                lastHealthCheck: new Date(),
            });
            return false;
        }
    }
    startHealthChecks() {
        const interval = parseInt(process.env.HEALTH_CHECK_INTERVAL ?? '30000');
        this.healthCheckInterval = setInterval(() => {
            void (async () => {
                const healthPromises = Array.from(this.services.keys()).map(serviceName => this.checkServiceHealth(serviceName));
                await Promise.allSettled(healthPromises);
                const healthyCount = this.getHealthyServices().length;
                const totalCount = this.services.size;
                logger_1.logger.debug(`Health check completed: ${healthyCount}/${totalCount} services healthy`);
            })();
        }, interval);
        logger_1.logger.info(`Health checks started with ${interval}ms interval`);
    }
    stopHealthChecks() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
            logger_1.logger.info('Health checks stopped');
        }
    }
}
exports.serviceRegistry = new ServiceRegistry();
//# sourceMappingURL=service-registry.js.map