"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = void 0;
const errors_1 = require("../utils/errors");
const logger_1 = require("../utils/logger");
const errorHandler = (error, req, res, next // eslint-disable-line @typescript-eslint/no-explicit-any
) => {
    // Get request ID safely
    const requestId = req.headers?.['x-request-id'] || 'unknown';
    // Log the error
    logger_1.logger.error('Error occurred:', {
        message: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.id,
        requestId,
    });
    // Check if res is a valid Express response object
    if (!res || typeof res.status !== 'function') {
        logger_1.logger.error('Invalid response object in error handler:', { error: error.message });
        return;
    }
    // Handle known application errors
    if (error instanceof errors_1.BaseError) {
        res.status(error.statusCode).json({
            success: false,
            message: error.message,
            meta: {
                timestamp: new Date().toISOString(),
                requestId,
            },
        });
        return;
    }
    // Handle Mongoose validation errors
    if (error.name === 'ValidationError') {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
        const validationErrors = Object.values(error.errors).map(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (err) => ({
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
            field: err.path,
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
            message: err.message,
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
            value: err.value,
        }));
        res.status(400).json({
            success: false,
            message: 'Validation failed',
            code: 'VALIDATION_ERROR',
            details: validationErrors,
            meta: {
                timestamp: new Date().toISOString(),
                requestId,
            },
        });
        return;
    }
    // Handle Mongoose cast errors
    if (error.name === 'CastError') {
        res.status(400).json({
            success: false,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
            message: `Invalid ${error.path}: ${error.value}`,
            code: 'INVALID_ID',
            meta: {
                timestamp: new Date().toISOString(),
                requestId,
            },
        });
        return;
    }
    // Handle MongoDB duplicate key errors
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
    if (error.code === 11000) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
        const field = Object.keys(error.keyValue)[0];
        res.status(409).json({
            success: false,
            message: `${field} already exists`,
            code: 'DUPLICATE_ERROR',
            meta: {
                timestamp: new Date().toISOString(),
                requestId,
            },
        });
        return;
    }
    // Handle JWT errors
    if (error.name === 'JsonWebTokenError') {
        res.status(401).json({
            success: false,
            message: 'Invalid token',
            code: 'INVALID_TOKEN',
            meta: {
                timestamp: new Date().toISOString(),
                requestId,
            },
        });
        return;
    }
    if (error.name === 'TokenExpiredError') {
        res.status(401).json({
            success: false,
            message: 'Token expired',
            code: 'TOKEN_EXPIRED',
            meta: {
                timestamp: new Date().toISOString(),
                requestId,
            },
        });
        return;
    }
    // Handle multer errors (file upload)
    if (error.name === 'MulterError') {
        let message = 'File upload error';
        const statusCode = 400;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
        switch (error.code) {
            case 'LIMIT_FILE_SIZE':
                message = 'File too large';
                break;
            case 'LIMIT_FILE_COUNT':
                message = 'Too many files';
                break;
            case 'LIMIT_UNEXPECTED_FILE':
                message = 'Unexpected file field';
                break;
            case 'LIMIT_PART_COUNT':
                message = 'Too many parts';
                break;
            case 'LIMIT_FIELD_KEY':
                message = 'Field key too long';
                break;
            case 'LIMIT_FIELD_VALUE':
                message = 'Field value too long';
                break;
            case 'LIMIT_FIELD_COUNT':
                message = 'Too many fields';
                break;
        }
        res.status(statusCode).json({
            success: false,
            message,
            code: 'FILE_UPLOAD_ERROR',
            meta: {
                timestamp: new Date().toISOString(),
                requestId,
            },
        });
        return;
    }
    // Handle syntax errors (malformed JSON)
    if (error instanceof SyntaxError && 'body' in error) {
        res.status(400).json({
            success: false,
            message: 'Invalid JSON in request body',
            code: 'INVALID_JSON',
            meta: {
                timestamp: new Date().toISOString(),
                requestId,
            },
        });
        return;
    }
    // Default error response
    const isDevelopment = process.env.NODE_ENV === 'development';
    res.status(500).json({
        success: false,
        message: isDevelopment ? error.message : 'Internal server error',
        code: 'INTERNAL_ERROR',
        ...(isDevelopment && { stack: error.stack }),
        meta: {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'],
        },
    });
};
exports.errorHandler = errorHandler;
//# sourceMappingURL=error.middleware.js.map