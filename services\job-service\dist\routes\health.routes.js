"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthRoutes = void 0;
const express_1 = require("express");
const connection_1 = require("../database/connection");
const response_1 = require("../utils/response");
const router = (0, express_1.Router)();
exports.healthRoutes = router;
router.get('/', (req, res) => {
    const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: process.env.API_VERSION ?? '1.0.0',
        database: {
            connected: connection_1.database.isHealthy(),
            state: connection_1.database.getConnectionState(),
        },
        memory: process.memoryUsage(),
    };
    const isHealthy = health.database.connected;
    if (isHealthy) {
        return res.json(response_1.ResponseUtil.success(health, 'Job Service is healthy'));
    }
    else {
        return res.status(503).json({
            success: false,
            message: 'Job Service is unhealthy',
            data: health,
        });
    }
});
//# sourceMappingURL=health.routes.js.map