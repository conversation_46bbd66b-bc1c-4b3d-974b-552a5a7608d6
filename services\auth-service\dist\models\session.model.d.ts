import { Document, Types } from 'mongoose';
export interface ISession extends Document {
    _id: Types.ObjectId;
    userId: Types.ObjectId;
    sessionId: string;
    deviceInfo: {
        userAgent: string;
        ip: string;
        deviceType: string;
        browser: string;
        os: string;
    };
    isActive: boolean;
    expiresAt: Date;
    createdAt: Date;
    updatedAt: Date;
}
export declare const SessionModel: import("mongoose").Model<ISession, {}, {}, {}, Document<unknown, {}, ISession, {}, {}> & ISession & Required<{
    _id: Types.ObjectId;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=session.model.d.ts.map