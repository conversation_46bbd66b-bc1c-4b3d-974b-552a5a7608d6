"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupPassport = void 0;
const passport_1 = __importDefault(require("passport"));
const passport_google_oauth20_1 = require("passport-google-oauth20");
const passport_jwt_1 = require("passport-jwt");
const environment_1 = require("./environment");
const logger_1 = require("../utils/logger");
const user_service_1 = require("../services/user.service");
const auth_service_1 = require("../services/auth.service");
const setupPassport = () => {
    // Google OAuth Strategy
    if (environment_1.googleConfig.clientId && environment_1.googleConfig.clientSecret) {
        passport_1.default.use(new passport_google_oauth20_1.Strategy({
            clientID: environment_1.googleConfig.clientId,
            clientSecret: environment_1.googleConfig.clientSecret,
            callbackURL: environment_1.googleConfig.callbackURL,
            scope: ['profile', 'email'],
        }, (accessToken, refreshToken, profile, done) => {
            void (async () => {
                try {
                    logger_1.logger.debug('Google OAuth callback received:', {
                        profileId: profile.id,
                        email: profile.emails?.[0]?.value,
                    });
                    const googleUser = {
                        googleId: profile.id,
                        email: profile.emails?.[0]?.value ?? '',
                        firstName: profile.name?.givenName ?? '',
                        lastName: profile.name?.familyName ?? '',
                        avatar: profile.photos?.[0]?.value,
                        emailVerified: profile.emails?.[0]?.verified ?? false,
                    };
                    const authService = new auth_service_1.AuthService();
                    const user = await authService.handleGoogleAuth(googleUser);
                    return done(null, user);
                }
                catch (error) {
                    logger_1.logger.error('Google OAuth error:', error);
                    return done(error, undefined);
                }
            })();
        }));
    }
    else {
        logger_1.logger.warn('Google OAuth not configured - missing client credentials');
    }
    // JWT Strategy
    passport_1.default.use(new passport_jwt_1.Strategy({
        jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
        secretOrKey: environment_1.jwtConfig.secret,
        issuer: 'job-platform',
        audience: 'job-platform-users',
    }, (payload, done) => {
        void (async () => {
            try {
                const userService = new user_service_1.UserService();
                const user = await userService.findById(payload.userId);
                if (!user) {
                    logger_1.logger.warn('JWT validation failed - user not found:', payload.userId);
                    return done(null, false);
                }
                if (!user.isActive) {
                    logger_1.logger.warn('JWT validation failed - user inactive:', payload.userId);
                    return done(null, false);
                }
                // Check if token is blacklisted
                const authService = new auth_service_1.AuthService();
                const isBlacklisted = await authService.isTokenBlacklisted(passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken()(null) ?? '');
                if (isBlacklisted) {
                    logger_1.logger.warn('JWT validation failed - token blacklisted:', payload.userId);
                    return done(null, false);
                }
                return done(null, {
                    id: user._id.toString(),
                    email: user.email,
                    role: user.role,
                    isVerified: user.isVerified,
                    subscriptionTier: user.subscriptionTier,
                    permissions: [], // TODO: Load user permissions
                });
            }
            catch (error) {
                logger_1.logger.error('JWT validation error:', error);
                return done(error, false);
            }
        })();
    }));
    // Serialize/Deserialize user for session support (if needed)
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    passport_1.default.serializeUser((user, done) => {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        done(null, user.id);
    });
    passport_1.default.deserializeUser((id, done) => {
        void (async () => {
            try {
                const userService = new user_service_1.UserService();
                const user = await userService.findById(id);
                done(null, user);
            }
            catch (error) {
                done(error, null);
            }
        })();
    });
};
exports.setupPassport = setupPassport;
//# sourceMappingURL=passport.config.js.map