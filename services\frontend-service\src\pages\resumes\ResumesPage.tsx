import React, { useState, useEffect } from 'react';
import { Upload, FileText, Download, Edit, Trash2, Star, Plus, Loader2, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { resumeService } from '@/services/resume.service';
import { Resume } from '@/types/api';

export const ResumesPage: React.FC = () => {
  const [resumes, setResumes] = useState<Resume[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchResumes = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await resumeService.getResumes();
        setResumes(response.resumes || []);
      } catch (err) {
        console.error('Error fetching resumes:', err);
        setError('Failed to load resumes. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchResumes();
  }, []);

  const handleRefresh = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await resumeService.getResumes();
      setResumes(response.resumes || []);
    } catch (err) {
      console.error('Error refreshing resumes:', err);
      setError('Failed to refresh resumes. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready':
        return 'status-active';
      case 'processing':
        return 'status-pending';
      case 'error':
        return 'status-error';
      default:
        return 'status-inactive';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-success-600';
    if (score >= 60) return 'text-warning-600';
    return 'text-error-600';
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>Loading resumes...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-red-500 mr-2">⚠️</div>
              <span className="text-red-700">{error}</span>
            </div>
            <Button onClick={handleRefresh} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Resumes</h1>
            <p className="text-gray-600 mt-2">
              Upload, manage, and optimize your resumes for better job matches
            </p>
          </div>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Upload section */}
      <div className="card mb-6">
        <div className="card-body">
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Upload className="w-8 h-8 text-primary-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Upload a New Resume
            </h3>
            <p className="text-gray-600 mb-4">
              Drag and drop your resume file or click to browse
            </p>
            <div className="flex justify-center gap-2">
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Choose File
              </Button>
              <Button variant="outline">
                Create from Template
              </Button>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Supports PDF, DOC, DOCX files up to 10MB
            </p>
          </div>
        </div>
      </div>

      {/* Resume list */}
      <div className="space-y-4">
        {resumes.length > 0 ? (
          resumes.map((resume) => (
          <div key={resume.id} className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-6 h-6 text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {resume.title}
                      </h3>
                      {resume.isDefault && (
                        <Star className="w-4 h-4 text-warning-500 fill-current" />
                      )}
                      <span className={`badge ${getStatusColor('ready')}`}>
                        ready
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span>Created {new Date(resume.createdAt).toLocaleDateString()}</span>
                      <span>{Math.round(resume.fileSize / 1024)} KB</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  {/* Analysis scores */}
                  {resume.analysis && (
                    <div className="flex space-x-4 text-sm">
                      <div className="text-center">
                        <div className={`font-semibold ${getScoreColor(resume.analysis.overallScore)}`}>
                          {resume.analysis.overallScore}%
                        </div>
                        <div className="text-gray-500">Overall</div>
                      </div>
                      <div className="text-center">
                        <div className={`font-semibold ${getScoreColor(resume.analysis.atsScore)}`}>
                          {resume.analysis.atsScore}%
                        </div>
                        <div className="text-gray-500">ATS</div>
                      </div>
                      <div className="text-center">
                        <div className={`font-semibold ${getScoreColor(resume.analysis.readabilityScore)}`}>
                          {resume.analysis.readabilityScore}%
                        </div>
                        <div className="text-gray-500">Readability</div>
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit className="w-4 h-4" />
                    </Button>
                    {!resume.isDefault && (
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4 text-error-600" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              {/* Analysis details */}
              {resume.analysis && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-600">
                      Last analyzed {new Date(resume.createdAt).toLocaleDateString()}
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        View Analysis
                      </Button>
                      <Button variant="outline" size="sm">
                        Optimize Resume
                      </Button>
                      {!resume.isDefault && (
                        <Button size="sm">
                          Set as Default
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))
        ) : (
          <div className="card">
            <div className="card-body text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <FileText className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No resumes found</h3>
              <p className="text-gray-500 mb-4">Upload your first resume to get started!</p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Upload Resume
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Tips section */}
      <div className="card mt-8">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-gray-900">Resume Tips</h2>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Optimize for ATS</h3>
              <p className="text-sm text-gray-600">
                Use standard section headings and avoid complex formatting to ensure 
                your resume passes through Applicant Tracking Systems.
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Tailor for Each Job</h3>
              <p className="text-sm text-gray-600">
                Customize your resume for each application by highlighting relevant 
                skills and experiences that match the job description.
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Use Action Verbs</h3>
              <p className="text-sm text-gray-600">
                Start bullet points with strong action verbs like "developed," 
                "managed," or "implemented" to make your achievements stand out.
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Quantify Results</h3>
              <p className="text-sm text-gray-600">
                Include specific numbers and metrics to demonstrate the impact 
                of your work and make your accomplishments more compelling.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
