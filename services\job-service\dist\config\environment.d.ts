export declare const env: {
    NODE_ENV: "development" | "production" | "test";
    PORT: string;
    MONGODB_URI: string;
    REDIS_URL: string;
    JWT_SECRET: string;
    JWT_REFRESH_SECRET: string;
    JWT_ACCESS_EXPIRES_IN: string;
    JWT_REFRESH_EXPIRES_IN: string;
    BCRYPT_ROUNDS: string;
    EMAIL_SERVICE_URL: string;
    USER_SERVICE_URL: string;
    CORS_ORIGIN: string;
    LOG_LEVEL: string;
    GOOGLE_CLIENT_ID?: string | undefined;
    GOOGLE_CLIENT_SECRET?: string | undefined;
};
export declare const appConfig: {
    port: number;
    nodeEnv: "development" | "production" | "test";
    corsOrigin: string;
    logLevel: string;
    isDevelopment: boolean;
    isProduction: boolean;
    apiVersion: string;
};
export declare const databaseConfig: {
    mongoUri: string;
    redisUrl: string;
};
export declare const jwtConfig: {
    secret: string;
    refreshSecret: string;
    accessExpiresIn: string;
    refreshExpiresIn: string;
};
export declare const serviceConfig: {
    emailServiceUrl: string;
    userServiceUrl: string;
};
export declare const googleConfig: {
    clientId: string | undefined;
    clientSecret: string | undefined;
};
//# sourceMappingURL=environment.d.ts.map