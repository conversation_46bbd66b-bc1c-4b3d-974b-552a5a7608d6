import { Schema } from 'mongoose';
export declare const Application: import("mongoose").Model<{
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    status: "pending" | "reviewed" | "shortlisted" | "interviewed" | "rejected" | "accepted" | "withdrawn";
    jobId: string;
    userId: string;
    resumeId: string;
    appliedAt: NativeDate;
    isReferred: boolean;
    source: "direct" | "job-board" | "referral" | "recruiter";
    notes?: string | null;
    coverLetter?: string | null;
    customAnswers?: any;
    interviewScheduledAt?: NativeDate | null;
    interviewNotes?: string | null;
    rejectionReason?: string | null;
    salaryExpectation?: number | null;
    availabilityDate?: NativeDate | null;
    referredBy?: string | null;
    trackingData?: {
        views: number;
        downloads: number;
        lastViewedAt?: NativeDate | null;
    } | null;
}, {}, {}, {}, import("mongoose").Document<unknown, {}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    status: "pending" | "reviewed" | "shortlisted" | "interviewed" | "rejected" | "accepted" | "withdrawn";
    jobId: string;
    userId: string;
    resumeId: string;
    appliedAt: NativeDate;
    isReferred: boolean;
    source: "direct" | "job-board" | "referral" | "recruiter";
    notes?: string | null;
    coverLetter?: string | null;
    customAnswers?: any;
    interviewScheduledAt?: NativeDate | null;
    interviewNotes?: string | null;
    rejectionReason?: string | null;
    salaryExpectation?: number | null;
    availabilityDate?: NativeDate | null;
    referredBy?: string | null;
    trackingData?: {
        views: number;
        downloads: number;
        lastViewedAt?: NativeDate | null;
    } | null;
}, {}, {
    timestamps: true;
    versionKey: false;
    toJSON: {
        transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown>;
    };
}> & {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    status: "pending" | "reviewed" | "shortlisted" | "interviewed" | "rejected" | "accepted" | "withdrawn";
    jobId: string;
    userId: string;
    resumeId: string;
    appliedAt: NativeDate;
    isReferred: boolean;
    source: "direct" | "job-board" | "referral" | "recruiter";
    notes?: string | null;
    coverLetter?: string | null;
    customAnswers?: any;
    interviewScheduledAt?: NativeDate | null;
    interviewNotes?: string | null;
    rejectionReason?: string | null;
    salaryExpectation?: number | null;
    availabilityDate?: NativeDate | null;
    referredBy?: string | null;
    trackingData?: {
        views: number;
        downloads: number;
        lastViewedAt?: NativeDate | null;
    } | null;
} & {
    _id: import("mongoose").Types.ObjectId;
}, Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, {
    timestamps: true;
    versionKey: false;
    toJSON: {
        transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown>;
    };
}, {
    createdAt: NativeDate;
    updatedAt: NativeDate;
} & {
    status: "pending" | "reviewed" | "shortlisted" | "interviewed" | "rejected" | "accepted" | "withdrawn";
    jobId: string;
    userId: string;
    resumeId: string;
    appliedAt: NativeDate;
    isReferred: boolean;
    source: "direct" | "job-board" | "referral" | "recruiter";
    notes?: string | null;
    coverLetter?: string | null;
    customAnswers?: any;
    interviewScheduledAt?: NativeDate | null;
    interviewNotes?: string | null;
    rejectionReason?: string | null;
    salaryExpectation?: number | null;
    availabilityDate?: NativeDate | null;
    referredBy?: string | null;
    trackingData?: {
        views: number;
        downloads: number;
        lastViewedAt?: NativeDate | null;
    } | null;
}, unknown>>;
//# sourceMappingURL=application.model.d.ts.map