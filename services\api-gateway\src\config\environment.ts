import { z } from 'zod';
import dotenv from 'dotenv';

dotenv.config();

const envSchema = z.object({
  NODE_ENV: z
    .enum(['development', 'production', 'test'])
    .default('development'),
  PORT: z.string().default('3000'),
  MONGODB_URI: z.string().default('mongodb://localhost:27017/job_platform'),
  REDIS_URL: z.string().default('redis://localhost:6379'),
  CORS_ORIGIN: z.string().default('*'),
  LOG_LEVEL: z.string().default('info'),
  AUTH_SERVICE_URL: z.string().default('http://resume-automator-services-auth-s:3001'),
  USER_SERVICE_URL: z.string().default('http://resume-automator-services-user-s:3002'),
  JOB_SERVICE_URL: z.string().default('http://resume-automator-services-job-se:3003'),
  RESUME_SERVICE_URL: z.string().default('http://resume-automator-services-resume:3005'),
});

export const env = envSchema.parse(process.env);

export const appConfig = {
  port: parseInt(env.PORT),
  nodeEnv: env.NODE_ENV,
  corsOrigin: env.CORS_ORIGIN,
  logLevel: env.LOG_LEVEL,
  isDevelopment: env.NODE_ENV === 'development',
  isProduction: env.NODE_ENV === 'production',
  apiVersion: '1.0.0',
};

export const databaseConfig = {
  mongoUri: env.MONGODB_URI,
  redisUrl: env.REDIS_URL,
};

export const serviceConfig = {
  authServiceUrl: env.AUTH_SERVICE_URL,
  userServiceUrl: env.USER_SERVICE_URL,
  jobServiceUrl: env.JOB_SERVICE_URL,
  resumeServiceUrl: env.RESUME_SERVICE_URL,
};
