export declare class S3Service {
    private initialized;
    /**
     * Initialize S3 service
     */
    initialize(): Promise<void>;
    /**
     * Upload file to S3
     */
    uploadFile(file: unknown, key: string): Promise<string>;
    /**
     * Delete file from S3
     */
    deleteFile(key: string): Promise<void>;
    /**
     * Check if service is initialized
     */
    isInitialized(): boolean;
}
//# sourceMappingURL=s3.service.d.ts.map