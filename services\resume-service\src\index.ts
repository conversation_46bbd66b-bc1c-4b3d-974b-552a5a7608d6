import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { appConfig } from './config/environment';
import { database } from './database/connection';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/error.middleware';
import { resumeRoutes } from './routes/resume.routes';
import { healthRoutes } from './routes/health.routes';
import { S3Service } from './services/s3.service';

class ResumeService {
  private app: express.Application;
  private server: ReturnType<express.Application['listen']> | null = null;
  private s3Service: S3Service;

  constructor() {
    this.app = express();
    // Enable trust proxy for proper IP detection behind reverse proxy
    this.app.set('trust proxy', true);
    this.s3Service = new S3Service();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    this.app.use(helmet());
    this.app.use(cors({ origin: true, credentials: true }));
    this.app.use(compression());
    this.app.use(express.json({ limit: '15mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '15mb' }));
    this.app.use(morgan(appConfig.isDevelopment ? 'dev' : 'combined'));
  }

  private setupRoutes(): void {
    this.app.use('/health', healthRoutes);
    // Support both /resumes and /api/v1 for API Gateway compatibility
    this.app.use('/resumes', resumeRoutes);
    this.app.use('/api/v1', resumeRoutes);
    this.app.get('/api/v1', (req, res) => {
      res.json({
        name: 'Job Platform Resume Service',
        version: appConfig.apiVersion,
        environment: appConfig.nodeEnv,
        timestamp: new Date().toISOString(),
        features: [
          'Resume Upload',
          'PDF Processing',
          'ATS Optimization',
          'AI Analysis',
        ],
      });
    });
  }

  private setupErrorHandling(): void {
    this.app.use((req, res) => {
      res.status(404).json({
        success: false,
        message: `Route ${req.originalUrl} not found`,
      });
    });
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      // Start server first to respond to health checks
      const port = appConfig.port;
      this.server = this.app.listen(port, '0.0.0.0', () => {
        logger.info(`📄 Resume Service running on port ${port}`);
        logger.info(`📝 Environment: ${appConfig.nodeEnv}`);
        logger.info(`🔗 Health Check: http://localhost:${port}/health`);
      });

      // Connect to database after server is running
      try {
        await database.connect();
        logger.info('Database connected successfully');
      } catch (dbError) {
        logger.warn('Database connection failed - service will run with limited functionality:', dbError);
        // Continue running without database - some features will be disabled
      }

      // Initialize S3 service
      try {
        await this.s3Service.initialize();
        logger.info('S3 service initialized successfully');
      } catch (s3Error) {
        logger.warn('S3 service initialization failed - file upload features will be disabled:', s3Error);
        // Continue running without S3 - file upload features will be disabled
      }

      process.on('SIGTERM', () => void this.shutdown());
      process.on('SIGINT', () => void this.shutdown());
    } catch (error) {
      logger.error('Failed to start Resume Service:', error);
      process.exit(1);
    }
  }

  private async shutdown(): Promise<void> {
    logger.info('🔄 Resume Service shutting down...');
    if (this.server) {
      await new Promise<void>(resolve => {
        this.server!.close(() => {
          void database.disconnect().then(() => {
            logger.info('✅ Resume Service shutdown completed');
            process.exit(0);
          });
        });
        resolve();
      });
    }
  }
}

const resumeService = new ResumeService();
resumeService.start().catch(error => {
  logger.error('Failed to start Resume Service:', error);
  process.exit(1);
});
