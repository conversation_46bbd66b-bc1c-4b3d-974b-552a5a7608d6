"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.baseSchema = void 0;
const mongoose_1 = require("mongoose");
exports.baseSchema = new mongoose_1.Schema({
    isDeleted: {
        type: Boolean,
        default: false,
    },
    deletedAt: {
        type: Date,
    },
    deletedBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
    },
}, {
    timestamps: true,
    versionKey: false,
});
// Add common methods
exports.baseSchema.methods.softDelete = function (deletedBy) {
    this.isDeleted = true;
    this.deletedAt = new Date();
    if (deletedBy) {
        this.deletedBy = deletedBy;
    }
    return this.save();
};
exports.baseSchema.methods.restore = function () {
    this.isDeleted = false;
    this.deletedAt = undefined;
    this.deletedBy = undefined;
    return this.save();
};
//# sourceMappingURL=base.schema.js.map