import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { jwtConfig } from '../config/environment';
import { ResponseUtil } from '../utils/response';
import { logger } from '../utils/logger';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
        sessionId: string;
      };
    }
  }
}

interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  sessionId: string;
  iat: number;
  exp: number;
  iss: string;
}

/**
 * JWT Authentication Middleware
 * Extracts and verifies JW<PERSON> token from Authorization header
 * Sets req.user with authenticated user information
 */
export const authenticateToken = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      const response = ResponseUtil.error('Access token required', 401);
      res.status(response.statusCode).json(response);
      return;
    }

    // Verify the token
    const decoded = jwt.verify(token, jwtConfig.secret) as JWTPayload;

    // Check if token has required fields
    if (!decoded.userId || !decoded.email) {
      const response = ResponseUtil.error('Invalid token payload', 401);
      res.status(response.statusCode).json(response);
      return;
    }

    // Set user information in request
    req.user = {
      id: decoded.userId,
      email: decoded.email,
      role: decoded.role,
      sessionId: decoded.sessionId,
    };

    logger.debug('User authenticated successfully:', {
      userId: decoded.userId,
      email: decoded.email,
    });

    next();
  } catch (error) {
    logger.warn('JWT authentication failed:', error);

    if (error instanceof jwt.TokenExpiredError) {
      const response = ResponseUtil.error('Token expired', 401);
      res.status(response.statusCode).json(response);
      return;
    }

    if (error instanceof jwt.JsonWebTokenError) {
      const response = ResponseUtil.error('Invalid token', 401);
      res.status(response.statusCode).json(response);
      return;
    }

    const response = ResponseUtil.error('Authentication failed', 401);
    res.status(response.statusCode).json(response);
  }
};

/**
 * Optional Authentication Middleware
 * Similar to authenticateToken but doesn't fail if no token is provided
 * Useful for endpoints that work for both authenticated and unauthenticated users
 */
export const optionalAuth = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      // No token provided, continue without authentication
      next();
      return;
    }

    // Verify the token
    const decoded = jwt.verify(token, jwtConfig.secret) as JWTPayload;

    if (decoded.userId && decoded.email) {
      req.user = {
        id: decoded.userId,
        email: decoded.email,
        role: decoded.role,
        sessionId: decoded.sessionId,
      };
    }

    next();
  } catch (error) {
    // Token is invalid, but we continue without authentication
    logger.debug('Optional auth failed, continuing without authentication:', error);
    next();
  }
};
