"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resumeRoutes = void 0;
const express_1 = require("express");
const response_1 = require("../utils/response");
const resume_model_1 = require("../models/resume.model");
const logger_1 = require("../utils/logger");
const auth_middleware_1 = require("../middleware/auth.middleware");
const router = (0, express_1.Router)();
exports.resumeRoutes = router;
// Get all resumes
router.get('/', async (req, res) => {
    try {
        const { page = 1, limit = 10, userId, isDefault } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        const filter = {};
        if (userId)
            filter.userId = userId;
        if (isDefault !== undefined)
            filter.isDefault = isDefault === 'true';
        const resumes = await resume_model_1.Resume.find(filter)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(Number(limit));
        const total = await resume_model_1.Resume.countDocuments(filter);
        const response = response_1.ResponseUtil.success({
            resumes,
            total,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(total / Number(limit)),
        }, 'Resumes retrieved successfully');
        res.status(response.statusCode).json(response);
    }
    catch (error) {
        const response = response_1.ResponseUtil.error('Failed to retrieve resumes', 500);
        res.status(response.statusCode).json(response);
    }
});
// Upload resume
router.post('/upload', async (req, res) => {
    try {
        const resumeData = {
            ...req.body,
            createdAt: new Date(),
        };
        const resume = new resume_model_1.Resume(resumeData);
        await resume.save();
        const response = response_1.ResponseUtil.created(resume, 'Resume uploaded successfully');
        res.status(response.statusCode).json(response);
    }
    catch (error) {
        const response = response_1.ResponseUtil.error('Failed to upload resume', 500);
        res.status(response.statusCode).json(response);
    }
});
// Get resume statistics
router.get('/stats', async (req, res) => {
    try {
        logger_1.logger.info('Resume stats endpoint called', {
            query: req.query,
            headers: req.headers,
            url: req.url
        });
        const { userId } = req.query;
        const filter = {};
        if (userId)
            filter.userId = userId;
        logger_1.logger.info('Querying resume statistics', { filter });
        const totalResumes = await resume_model_1.Resume.countDocuments(filter);
        logger_1.logger.info('Total resumes count', { totalResumes });
        // Get resumes with analysis data
        const resumesWithAnalysis = await resume_model_1.Resume.find({
            ...filter,
            'analysis.overallScore': { $exists: true }
        });
        logger_1.logger.info('Resumes with analysis', { count: resumesWithAnalysis.length });
        const averageScore = resumesWithAnalysis.length > 0
            ? resumesWithAnalysis.reduce((sum, resume) => sum + (resume.analysis?.overallScore || 0), 0) / resumesWithAnalysis.length
            : 0;
        // Extract top skills from all resumes
        const allSkills = resumesWithAnalysis.flatMap(resume => resume.content?.skills || []);
        const skillCounts = allSkills.reduce((acc, skill) => {
            acc[skill] = (acc[skill] || 0) + 1;
            return acc;
        }, {});
        const topSkills = Object.entries(skillCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([skill]) => skill);
        const statsData = {
            totalResumes,
            resumesByStatus: {
                ready: totalResumes, // All resumes are considered "ready" in our model
                processing: 0,
                error: 0,
            },
            averageScore: Math.round(averageScore * 100) / 100,
            topSkills,
        };
        logger_1.logger.info('Resume statistics calculated successfully', { statsData });
        const response = response_1.ResponseUtil.success(statsData, 'Resume statistics retrieved successfully');
        res.status(response.statusCode).json(response);
    }
    catch (error) {
        logger_1.logger.error('Error retrieving resume statistics:', {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
            query: req.query,
            url: req.url
        });
        const response = response_1.ResponseUtil.error('Failed to retrieve resume statistics', 500);
        res.status(response.statusCode).json(response);
    }
});
// Get resume by ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const resume = await resume_model_1.Resume.findById(id);
        if (!resume) {
            const response = response_1.ResponseUtil.error('Resume not found', 404);
            return res.status(response.statusCode).json(response);
        }
        // Increment view count
        await resume_model_1.Resume.findByIdAndUpdate(id, { $inc: { viewCount: 1 } });
        const response = response_1.ResponseUtil.success(resume, 'Resume retrieved successfully');
        return res.status(response.statusCode).json(response);
    }
    catch (error) {
        const response = response_1.ResponseUtil.error('Failed to retrieve resume', 500);
        return res.status(response.statusCode).json(response);
    }
});
// Get resume analytics
router.get('/analytics', auth_middleware_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const filter = { userId };
        const totalResumes = await resume_model_1.Resume.countDocuments(filter);
        // Get resumes with analysis data
        const resumesWithAnalysis = await resume_model_1.Resume.find({
            ...filter,
            'analysis.overallScore': { $exists: true }
        });
        const averageScore = resumesWithAnalysis.length > 0
            ? resumesWithAnalysis.reduce((sum, resume) => sum + (resume.analysis?.overallScore || 0), 0) / resumesWithAnalysis.length
            : 0;
        // Extract top skills from all resumes
        const allSkills = resumesWithAnalysis.flatMap(resume => resume.content?.skills || []);
        const skillCounts = allSkills.reduce((acc, skill) => {
            acc[skill] = (acc[skill] || 0) + 1;
            return acc;
        }, {});
        const topSkills = Object.entries(skillCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([skill]) => skill);
        const response = response_1.ResponseUtil.success({
            totalResumes,
            resumesByStatus: {
                ready: totalResumes,
                processing: 0,
                error: 0,
            },
            averageScore: Math.round(averageScore * 100) / 100,
            topSkills,
        }, 'Resume analytics retrieved successfully');
        return res.status(response.statusCode).json(response);
    }
    catch (error) {
        const response = response_1.ResponseUtil.error('Failed to retrieve resume analytics', 500);
        return res.status(response.statusCode).json(response);
    }
});
//# sourceMappingURL=resume.routes.js.map