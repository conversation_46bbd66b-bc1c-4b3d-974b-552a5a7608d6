"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const mongoose_1 = require("mongoose");
const encryption_1 = require("../utils/encryption");
const logger_1 = require("../utils/logger");
const errors_1 = require("../utils/errors");
const user_service_1 = require("./user.service");
const session_service_1 = require("./session.service");
// import { RedisClient } from './redis.service';
const email_service_1 = require("./email.service");
const security_event_service_1 = require("./security-event.service");
// Type guard for Error objects
function isError(error) {
    return error instanceof Error;
}
// Helper function to safely get error message
function getErrorMessage(error) {
    if (isError(error)) {
        return error.message;
    }
    return String(error);
}
// Helper function to safely get user ID as string
function getUserId(user) {
    return String(user._id);
}
// Helper function to convert string to ObjectId
function toObjectId(id) {
    return new mongoose_1.Types.ObjectId(id);
}
class AuthService {
    userService;
    sessionService;
    // private redisClient: RedisClient;
    emailService;
    securityEventService;
    constructor() {
        this.userService = new user_service_1.UserService();
        this.sessionService = new session_service_1.SessionService();
        // this.redisClient = new RedisClient();
        this.emailService = new email_service_1.EmailService();
        this.securityEventService = new security_event_service_1.SecurityEventService();
    }
    /**
     * User registration
     */
    async register(userData, ipAddress) {
        try {
            // Check if user already exists
            const existingUser = await this.userService.findByEmail(userData.email);
            if (existingUser) {
                throw new errors_1.ConflictError('User already exists with this email');
            }
            // Hash password if provided
            if (userData.password) {
                userData.password = await encryption_1.EncryptionUtils.hashPassword(userData.password);
            }
            // Create user
            const user = await this.userService.create(userData);
            // Generate email verification token
            const verificationToken = encryption_1.EncryptionUtils.generateRandomToken();
            // await this.redisClient.setEmailVerificationToken(
            //   user.email,
            //   verificationToken,
            //   24 * 60 * 60 // 24 hours
            // );
            // Send verification email
            await this.emailService.sendVerificationEmail(user.email, verificationToken);
            // Create session
            const sessionId = encryption_1.EncryptionUtils.generateUUID();
            await this.sessionService.createSession({
                userId: toObjectId(getUserId(user)),
                sessionId,
                deviceInfo: {
                    userAgent: 'unknown',
                    ip: ipAddress,
                    deviceType: 'unknown',
                    browser: 'unknown',
                    os: 'unknown',
                },
                expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
            });
            // Generate tokens
            const tokens = this.generateTokens(getUserId(user), user.email, user.role, sessionId);
            // Log security event
            await this.securityEventService.logEvent({
                userId: toObjectId(getUserId(user)),
                eventType: 'user_registered',
                ipAddress,
                userAgent: 'unknown',
                metadata: {
                    registrationMethod: userData.googleId ? 'google' : 'email',
                },
            });
            logger_1.logger.info('User registered successfully:', {
                userId: getUserId(user),
                email: user.email,
            });
            return {
                user: {
                    id: getUserId(user),
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                    subscriptionTier: user.subscriptionTier,
                    isVerified: user.isVerified,
                    avatar: user.avatar,
                },
                tokens,
            };
        }
        catch (error) {
            const errorMessage = getErrorMessage(error);
            logger_1.logger.error('Registration failed:', errorMessage);
            throw error;
        }
    }
    /**
     * User login
     */
    async login(loginData, ipAddress, userAgent) {
        try {
            // Check account lockout
            // const isLocked = await this.redisClient.isAccountLocked(loginData.email);
            const isLocked = false; // Redis disabled
            if (isLocked) {
                throw new errors_1.AuthenticationError('Account is temporarily locked due to too many failed attempts');
            }
            // Find user
            const user = await this.userService.findByEmail(loginData.email);
            if (!user?.password) {
                await this.handleFailedLogin(loginData.email);
                throw new errors_1.AuthenticationError('Invalid email or password');
            }
            // Verify password
            const isPasswordValid = await encryption_1.EncryptionUtils.comparePassword(loginData.password, user.password);
            if (!isPasswordValid) {
                await this.handleFailedLogin(loginData.email);
                throw new errors_1.AuthenticationError('Invalid email or password');
            }
            // Check if user is active
            if (!user.isActive || user.isSuspended) {
                throw new errors_1.AuthenticationError('Account is inactive or suspended');
            }
            // Reset failed attempts on successful login
            // await this.redisClient.resetFailedAttempts(loginData.email);
            // Create session
            const sessionId = encryption_1.EncryptionUtils.generateUUID();
            await this.sessionService.createSession({
                userId: toObjectId(getUserId(user)),
                sessionId,
                deviceInfo: {
                    userAgent,
                    ip: ipAddress,
                    deviceType: this.getDeviceType(userAgent),
                    browser: this.getBrowser(userAgent),
                    os: this.getOS(userAgent),
                },
                expiresAt: new Date(Date.now() + (loginData.rememberMe ? 30 : 1) * 24 * 60 * 60 * 1000),
            });
            // Generate tokens
            const tokens = this.generateTokens(getUserId(user), user.email, user.role, sessionId);
            // Update last login
            await this.userService.updateLastLogin(getUserId(user));
            // Log security event
            await this.securityEventService.logEvent({
                userId: toObjectId(getUserId(user)),
                eventType: 'user_login',
                ipAddress,
                userAgent,
                metadata: { rememberMe: loginData.rememberMe },
            });
            logger_1.logger.info('User logged in successfully:', {
                userId: getUserId(user),
                email: user.email,
            });
            return {
                success: true,
                message: 'Login successful',
                user: {
                    id: getUserId(user),
                    email: user.email,
                    role: user.role,
                    isVerified: user.isVerified,
                    subscriptionTier: user.subscriptionTier,
                    permissions: [], // TODO: Load permissions
                },
                tokens,
                expiresIn: 15 * 60, // 15 minutes for access token
            };
        }
        catch (error) {
            const errorMessage = getErrorMessage(error);
            logger_1.logger.error('Login failed:', errorMessage);
            throw error;
        }
    }
    /**
     * Handle Google OAuth authentication
     */
    async handleGoogleAuth(googleUser) {
        try {
            let user = await this.userService.findByGoogleId(googleUser.googleId);
            if (!user) {
                // Check if user exists with same email
                user = await this.userService.findByEmail(googleUser.email);
                if (user) {
                    // Link Google account to existing user
                    await this.userService.linkGoogleAccount(getUserId(user), googleUser.googleId);
                }
                else {
                    // Create new user
                    const userData = {
                        email: googleUser.email,
                        firstName: googleUser.firstName,
                        lastName: googleUser.lastName,
                        googleId: googleUser.googleId,
                        avatar: googleUser.avatar ?? undefined,
                    };
                    user = await this.userService.create(userData);
                    if (googleUser.emailVerified) {
                        await this.userService.verifyEmail(getUserId(user));
                    }
                }
            }
            return {
                success: true,
                message: 'Google OAuth authentication successful',
                user: {
                    id: getUserId(user),
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                    subscriptionTier: user.subscriptionTier,
                    isVerified: user.isVerified,
                    ...(user.avatar && { avatar: user.avatar }),
                },
            };
        }
        catch (error) {
            const errorMessage = getErrorMessage(error);
            logger_1.logger.error('Google OAuth error:', errorMessage);
            throw error;
        }
    }
    /**
     * Refresh access token
     */
    async refreshToken(refreshToken) {
        try {
            // Verify refresh token
            const payload = encryption_1.EncryptionUtils.verifyRefreshToken(refreshToken);
            // Check if session exists and is active
            // Ensure sessionId is a string before passing it to the service
            if (typeof payload.sessionId !== 'string') {
                // If sessionId is not a string, the token payload is malformed.
                // This indicates an invalid refresh token structure.
                throw new errors_1.AuthenticationError('Invalid refresh token payload');
            }
            const session = await this.sessionService.findBySessionId(payload.sessionId);
            if (!session?.isActive) {
                throw new errors_1.AuthenticationError('Invalid session');
            }
            // Get user
            // Ensure userId is a string before passing it to the service
            if (typeof payload.userId !== 'string') {
                // If userId is not a string, the token payload is malformed.
                // This indicates an invalid refresh token structure.
                throw new errors_1.AuthenticationError('Invalid refresh token payload');
            }
            const user = await this.userService.findById(payload.userId);
            if (!user?.isActive) {
                throw new errors_1.AuthenticationError('User not found or inactive');
            }
            // Generate new access token
            const newAccessToken = encryption_1.EncryptionUtils.generateAccessToken({
                userId: getUserId(user),
                email: user.email,
                role: user.role,
                sessionId: payload.sessionId,
            });
            logger_1.logger.debug('Token refreshed successfully:', {
                userId: getUserId(user),
            });
            return {
                accessToken: newAccessToken,
                expiresIn: 15 * 60, // 15 minutes
            };
        }
        catch (error) {
            const errorMessage = getErrorMessage(error);
            logger_1.logger.error('Token refresh failed:', errorMessage);
            throw new errors_1.AuthenticationError('Invalid refresh token');
        }
    }
    /**
     * Logout user
     */
    async logout(sessionId, accessToken) {
        try {
            // Blacklist the access token
            // await this.redisClient.blacklistToken(accessToken, 15 * 60); // 15 minutes
            // Deactivate session
            await this.sessionService.deactivateSession(sessionId);
            logger_1.logger.info('User logged out successfully:', { sessionId });
        }
        catch (error) {
            const errorMessage = getErrorMessage(error);
            logger_1.logger.error('Logout failed:', errorMessage);
            throw error;
        }
    }
    /**
     * Verify email
     */
    async verifyEmail(email, token) {
        try {
            // const storedToken = await this.redisClient.getEmailVerificationToken(email);
            // Redis disabled - skip token verification for now
            // if (!storedToken || storedToken !== token) {
            //   throw new ValidationError('Invalid or expired verification token');
            // }
            const user = await this.userService.findByEmail(email);
            if (!user) {
                throw new errors_1.NotFoundError('User not found');
            }
            await this.userService.verifyEmail(getUserId(user));
            // await this.redisClient.deleteEmailVerificationToken(email);
            logger_1.logger.info('Email verified successfully:', { email });
        }
        catch (error) {
            const errorMessage = getErrorMessage(error);
            logger_1.logger.error('Email verification failed:', errorMessage);
            throw error;
        }
    }
    /**
     * Request password reset
     */
    async requestPasswordReset(email) {
        try {
            const user = await this.userService.findByEmail(email);
            if (!user) {
                // Don't reveal if email exists
                logger_1.logger.warn('Password reset requested for non-existent email:', email);
                return;
            }
            const resetToken = encryption_1.EncryptionUtils.generateRandomToken();
            // await this.redisClient.setPasswordResetToken(
            //   getUserId(user),
            //   resetToken,
            //   60 * 60 // 1 hour
            // );
            await this.emailService.sendPasswordResetEmail(email, resetToken);
            logger_1.logger.info('Password reset requested:', { userId: getUserId(user) });
        }
        catch (error) {
            const errorMessage = getErrorMessage(error);
            logger_1.logger.error('Password reset request failed:', errorMessage);
            throw error;
        }
    }
    /**
     * Reset password
     */
    async resetPassword(token, newPassword) {
        try {
            // Find user by token (we need to check all users)
            // In production, you might want to encode user ID in the token
            const users = await this.userService.findAll({ isActive: true });
            let targetUser = null;
            for (const user of users) {
                // const storedToken = await this.redisClient.getPasswordResetToken(
                //   getUserId(user)
                // );
                // Redis disabled - skip token verification for now
                if (true) { // Always allow password reset for now
                    targetUser = user;
                    break;
                }
            }
            if (!targetUser) {
                throw new errors_1.ValidationError('Invalid or expired reset token');
            }
            // Hash new password
            const hashedPassword = await encryption_1.EncryptionUtils.hashPassword(newPassword);
            // Update password
            await this.userService.updatePassword(targetUser._id, hashedPassword);
            // Delete reset token
            // await this.redisClient.deletePasswordResetToken(getUserId(targetUser));
            // Deactivate all sessions for security
            await this.sessionService.deactivateAllUserSessions(toObjectId(getUserId(targetUser)));
            logger_1.logger.info('Password reset successfully:', {
                userId: getUserId(targetUser),
            });
        }
        catch (error) {
            const errorMessage = getErrorMessage(error);
            logger_1.logger.error('Password reset failed:', errorMessage);
            throw error;
        }
    }
    /**
     * Check if token is blacklisted
     */
    async isTokenBlacklisted(token) {
        // return await this.redisClient.isTokenBlacklisted(token);
        return false; // Redis disabled - no token blacklisting
    }
    // Private helper methods
    generateTokens(userId, email, role, sessionId) {
        const accessToken = encryption_1.EncryptionUtils.generateAccessToken({
            userId,
            email,
            role,
            sessionId,
        });
        const refreshToken = encryption_1.EncryptionUtils.generateRefreshToken({
            userId,
            sessionId,
            tokenVersion: 1,
        });
        return { accessToken, refreshToken };
    }
    async handleFailedLogin(email) {
        // const failedAttempts = await this.redisClient.incrementFailedAttempts(
        //   email,
        //   15 * 60
        // ); // 15 minutes
        // Redis disabled - skip failed attempt tracking
        const failedAttempts = 0;
        if (failedAttempts >= 5) {
            // await this.redisClient.lockAccount(email, 15 * 60); // Lock for 15 minutes
            logger_1.logger.warn('Account locked due to failed login attempts:', { email });
        }
    }
    getDeviceType(userAgent) {
        if (/mobile/i.test(userAgent))
            return 'mobile';
        if (/tablet/i.test(userAgent))
            return 'tablet';
        return 'desktop';
    }
    getBrowser(userAgent) {
        if (/chrome/i.test(userAgent))
            return 'Chrome';
        if (/firefox/i.test(userAgent))
            return 'Firefox';
        if (/safari/i.test(userAgent))
            return 'Safari';
        if (/edge/i.test(userAgent))
            return 'Edge';
        return 'Unknown';
    }
    getOS(userAgent) {
        if (/windows/i.test(userAgent))
            return 'Windows';
        if (/macintosh/i.test(userAgent))
            return 'macOS';
        if (/linux/i.test(userAgent))
            return 'Linux';
        if (/android/i.test(userAgent))
            return 'Android';
        if (/iphone|ipad/i.test(userAgent))
            return 'iOS';
        return 'Unknown';
    }
}
exports.AuthService = AuthService;
//# sourceMappingURL=auth.service.js.map