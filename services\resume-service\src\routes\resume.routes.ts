import { Router } from 'express';
import { ResponseUtil } from '../utils/response';
import { Resume } from '../models/resume.model';
import { logger } from '../utils/logger';

const router = Router();

// Get all resumes
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, userId, isDefault } = req.query;
    const skip = (Number(page) - 1) * Number(limit);
    
    const filter: any = {};
    if (userId) filter.userId = userId;
    if (isDefault !== undefined) filter.isDefault = isDefault === 'true';
    
    const resumes = await Resume.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit));
    
    const total = await Resume.countDocuments(filter);
    
    const response = ResponseUtil.success(
      {
        resumes,
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / Number(limit)),
      },
      'Resumes retrieved successfully'
    );

    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve resumes', 500);
    res.status(response.statusCode).json(response);
  }
});

// Upload resume
router.post('/upload', async (req, res) => {
  try {
    const resumeData = {
      ...req.body,
      createdAt: new Date(),
    };
    
    const resume = new Resume(resumeData);
    await resume.save();
    
    const response = ResponseUtil.created(resume, 'Resume uploaded successfully');
    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to upload resume', 500);
    res.status(response.statusCode).json(response);
  }
});

// Get resume statistics
router.get('/stats', async (req, res) => {
  try {
    logger.info('Resume stats endpoint called', {
      query: req.query,
      headers: req.headers,
      url: req.url
    });

    const { userId } = req.query;
    const filter: any = {};
    if (userId) filter.userId = userId;

    logger.info('Querying resume statistics', { filter });

    const totalResumes = await Resume.countDocuments(filter);
    logger.info('Total resumes count', { totalResumes });

    // Get resumes with analysis data
    const resumesWithAnalysis = await Resume.find({
      ...filter,
      'analysis.overallScore': { $exists: true }
    });
    logger.info('Resumes with analysis', { count: resumesWithAnalysis.length });

    const averageScore = resumesWithAnalysis.length > 0
      ? resumesWithAnalysis.reduce((sum, resume) => sum + (resume.analysis?.overallScore || 0), 0) / resumesWithAnalysis.length
      : 0;

    // Extract top skills from all resumes
    const allSkills = resumesWithAnalysis.flatMap(resume => resume.content?.skills || []);
    const skillCounts = allSkills.reduce((acc, skill) => {
      acc[skill] = (acc[skill] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topSkills = Object.entries(skillCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([skill]) => skill);

    const statsData = {
      totalResumes,
      resumesByStatus: {
        ready: totalResumes, // All resumes are considered "ready" in our model
        processing: 0,
        error: 0,
      },
      averageScore: Math.round(averageScore * 100) / 100,
      topSkills,
    };

    logger.info('Resume statistics calculated successfully', { statsData });

    const response = ResponseUtil.success(
      statsData,
      'Resume statistics retrieved successfully'
    );

    res.status(response.statusCode).json(response);
  } catch (error) {
    logger.error('Error retrieving resume statistics:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      query: req.query,
      url: req.url
    });

    const response = ResponseUtil.error('Failed to retrieve resume statistics', 500);
    res.status(response.statusCode).json(response);
  }
});

// Get resume by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const resume = await Resume.findById(id);
    if (!resume) {
      const response = ResponseUtil.error('Resume not found', 404);
      return res.status(response.statusCode).json(response);
    }
    
    // Increment view count
    await Resume.findByIdAndUpdate(id, { $inc: { viewCount: 1 } });
    
    const response = ResponseUtil.success(resume, 'Resume retrieved successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve resume', 500);
    return res.status(response.statusCode).json(response);
  }
});



// Get resume analytics
router.get('/analytics', async (req, res) => {
  try {
    const { userId } = req.query;
    if (!userId) {
      const response = ResponseUtil.error('User ID is required', 400);
      return res.status(response.statusCode).json(response);
    }
    
    const filter = { userId };
    
    const totalResumes = await Resume.countDocuments(filter);
    
    // Get resumes with analysis data
    const resumesWithAnalysis = await Resume.find({ 
      ...filter, 
      'analysis.overallScore': { $exists: true } 
    });
    
    const averageScore = resumesWithAnalysis.length > 0 
      ? resumesWithAnalysis.reduce((sum, resume) => sum + (resume.analysis?.overallScore || 0), 0) / resumesWithAnalysis.length
      : 0;
    
    // Extract top skills from all resumes
    const allSkills = resumesWithAnalysis.flatMap(resume => resume.content?.skills || []);
    const skillCounts = allSkills.reduce((acc, skill) => {
      acc[skill] = (acc[skill] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const topSkills = Object.entries(skillCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([skill]) => skill);
    
    const response = ResponseUtil.success(
      {
        totalResumes,
        resumesByStatus: {
          ready: totalResumes,
          processing: 0,
          error: 0,
        },
        averageScore: Math.round(averageScore * 100) / 100,
        topSkills,
      },
      'Resume analytics retrieved successfully'
    );

    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve resume analytics', 500);
    return res.status(response.statusCode).json(response);
  }
});

export { router as resumeRoutes };
